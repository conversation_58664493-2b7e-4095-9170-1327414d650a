<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط للدورات</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 20px; margin: 5px; background: #4f46e5; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .course-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>اختبار بسيط للدورات</h1>
    
    <button onclick="testCoursesLoad()">اختبار تحميل الدورات</button>
    
    <div id="status"></div>
    <div id="courses-display"></div>

    <script>
        async function testCoursesLoad() {
            const statusDiv = document.getElementById('status');
            const coursesDiv = document.getElementById('courses-display');
            
            statusDiv.innerHTML = '<p class="info">جاري التحميل...</p>';
            coursesDiv.innerHTML = '';
            
            try {
                // اختبار تحميل البيانات
                const response = await fetch('api.php?section=courses');
                const result = await response.json();
                
                console.log('API Response:', result);
                
                if (result.success && result.data.length > 0) {
                    statusDiv.innerHTML = `<p class="success">✅ تم تحميل ${result.data.length} دورة بنجاح</p>`;
                    
                    // عرض الدورات
                    result.data.forEach(course => {
                        const courseDiv = document.createElement('div');
                        courseDiv.className = 'course-card';
                        courseDiv.innerHTML = `
                            <h3>${course.title}</h3>
                            <p><strong>المقدم:</strong> ${course.provider}</p>
                            <p><strong>المدة:</strong> ${course.duration}</p>
                            <p><strong>الحالة:</strong> ${course.status}</p>
                            <p><strong>الوصف:</strong> ${course.description}</p>
                        `;
                        coursesDiv.appendChild(courseDiv);
                    });
                    
                } else {
                    statusDiv.innerHTML = '<p class="error">❌ لم يتم العثور على دورات</p>';
                }
                
            } catch (error) {
                statusDiv.innerHTML = `<p class="error">❌ خطأ: ${error.message}</p>`;
                console.error('Error:', error);
            }
        }
        
        // تحميل تلقائي
        document.addEventListener('DOMContentLoaded', testCoursesLoad);
    </script>
</body>
</html>
