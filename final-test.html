<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاختبار النهائي</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .test-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background: #4f46e5; color: white; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🎯 الاختبار النهائي للدورات</h1>
    
    <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
    
    <div id="results"></div>

    <script>
        async function runAllTests() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>جاري تشغيل الاختبارات...</h2>';
            
            let results = '';
            let allPassed = true;
            
            // اختبار 1: الملف مباشرة
            try {
                const response = await fetch('data/courses.json');
                const data = await response.json();
                if (data.length === 3) {
                    results += '<div class="test-item success">✅ اختبار 1: ملف courses.json - نجح (3 دورات)</div>';
                } else {
                    results += '<div class="test-item error">❌ اختبار 1: ملف courses.json - فشل (' + data.length + ' دورات)</div>';
                    allPassed = false;
                }
            } catch (error) {
                results += '<div class="test-item error">❌ اختبار 1: ملف courses.json - خطأ: ' + error.message + '</div>';
                allPassed = false;
            }
            
            // اختبار 2: get-data.php
            try {
                const response = await fetch('get-data.php?type=courses');
                const result = await response.json();
                if (result.success && result.data.length === 3) {
                    results += '<div class="test-item success">✅ اختبار 2: get-data.php - نجح (3 دورات)</div>';
                } else {
                    results += '<div class="test-item error">❌ اختبار 2: get-data.php - فشل (' + (result.data ? result.data.length : 0) + ' دورات)</div>';
                    allPassed = false;
                }
            } catch (error) {
                results += '<div class="test-item error">❌ اختبار 2: get-data.php - خطأ: ' + error.message + '</div>';
                allPassed = false;
            }
            
            // اختبار 3: api.php
            try {
                const response = await fetch('api.php?section=courses');
                const result = await response.json();
                if (result.success && result.data.length === 3) {
                    results += '<div class="test-item success">✅ اختبار 3: api.php - نجح (3 دورات)</div>';
                } else {
                    results += '<div class="test-item error">❌ اختبار 3: api.php - فشل (' + (result.data ? result.data.length : 0) + ' دورات)</div>';
                    allPassed = false;
                }
            } catch (error) {
                results += '<div class="test-item error">❌ اختبار 3: api.php - خطأ: ' + error.message + '</div>';
                allPassed = false;
            }
            
            // اختبار 4: عرض في الصفحة الرئيسية
            try {
                const response = await fetch('get-data.php?type=courses');
                const result = await response.json();
                if (result.success && result.data.length === 3) {
                    results += '<div class="test-item success">✅ اختبار 4: البيانات جاهزة للعرض في الصفحة الرئيسية</div>';
                } else {
                    results += '<div class="test-item error">❌ اختبار 4: البيانات غير جاهزة للعرض</div>';
                    allPassed = false;
                }
            } catch (error) {
                results += '<div class="test-item error">❌ اختبار 4: خطأ في التحضير للعرض</div>';
                allPassed = false;
            }
            
            // النتيجة النهائية
            if (allPassed) {
                results += '<div class="test-item success"><h2>🎉 جميع الاختبارات نجحت! الدورات تعمل بشكل صحيح</h2></div>';
                results += '<div class="test-item"><p><strong>يمكنك الآن:</strong></p>';
                results += '<ul>';
                results += '<li>فتح <a href="admin-courses.html">صفحة إدارة الدورات</a> لإدارة الدورات</li>';
                results += '<li>فتح <a href="index.html">الصفحة الرئيسية</a> لرؤية الدورات</li>';
                results += '<li>إضافة/تعديل/حذف الدورات من لوحة التحكم</li>';
                results += '</ul></div>';
            } else {
                results += '<div class="test-item error"><h2>❌ بعض الاختبارات فشلت</h2></div>';
            }
            
            resultsDiv.innerHTML = results;
        }
        
        // تشغيل تلقائي
        document.addEventListener('DOMContentLoaded', runAllTests);
    </script>
</body>
</html>
