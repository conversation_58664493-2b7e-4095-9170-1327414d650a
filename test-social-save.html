<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حفظ روابط التواصل</title>
</head>
<body>
    <h1>اختبار حفظ روابط التواصل الاجتماعي</h1>
    
    <button onclick="testSave()">اختبار الحفظ</button>
    <button onclick="testLoad()">اختبار التحميل</button>
    
    <div id="result"></div>

    <script>
        async function testSave() {
            const testData = {
                linkedin: "https://linkedin.com/in/test",
                github: "https://github.com/test",
                twitter: "https://twitter.com/test",
                instagram: "https://instagram.com/test",
                email: "<EMAIL>",
                phone: "1234567890",
                youtube: "https://youtube.com/test"
            };

            try {
                console.log('إرسال البيانات:', testData);
                
                const response = await fetch('save-social-links.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                console.log('حالة الاستجابة:', response.status);
                const responseText = await response.text();
                console.log('نص الاستجابة:', responseText);

                const result = JSON.parse(responseText);
                document.getElementById('result').innerHTML = 
                    '<h3>نتيجة الحفظ:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';

            } catch (error) {
                console.error('خطأ:', error);
                document.getElementById('result').innerHTML = 
                    '<h3>خطأ:</h3><p>' + error.message + '</p>';
            }
        }

        async function testLoad() {
            try {
                const response = await fetch('get-data.php?type=social-links');
                const result = await response.json();
                
                console.log('البيانات المحملة:', result);
                document.getElementById('result').innerHTML = 
                    '<h3>البيانات المحملة:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';

            } catch (error) {
                console.error('خطأ في التحميل:', error);
                document.getElementById('result').innerHTML = 
                    '<h3>خطأ في التحميل:</h3><p>' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
