<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// إعدادات الرفع للشهادات
$uploadDir = __DIR__ . '/image/';
$allowedTypes = [
    'image/jpeg', 
    'image/jpg', 
    'image/png', 
    'image/gif', 
    'image/webp',
    'application/pdf'
];
$maxFileSize = 10 * 1024 * 1024; // 10MB للشهادات

// التحقق من وجود مجلد الصور
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير صحيحة');
    }
    
    if (!isset($_FILES['image'])) {
        throw new Exception('لم يتم اختيار ملف');
    }
    
    $file = $_FILES['image'];
    
    // التحقق من وجود أخطاء في الرفع
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('خطأ في رفع الملف');
    }
    
    // التحقق من نوع الملف
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WebP, PDF');
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > $maxFileSize) {
        throw new Exception('حجم الملف كبير جداً. الحد الأقصى 10MB');
    }
    
    // إنشاء اسم فريد للملف
    $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $isPDF = strtolower($fileExtension) === 'pdf';
    
    if ($isPDF) {
        $fileName = 'cert_' . time() . '_' . uniqid() . '.pdf';
    } else {
        $fileName = 'cert_' . time() . '_' . uniqid() . '.' . $fileExtension;
    }
    
    $filePath = $uploadDir . $fileName;
    
    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        echo json_encode([
            'success' => true,
            'message' => 'تم رفع الملف بنجاح',
            'filename' => 'image/' . $fileName,
            'type' => $isPDF ? 'pdf' : 'image',
            'size' => $file['size']
        ]);
    } else {
        throw new Exception('فشل في رفع الملف');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
