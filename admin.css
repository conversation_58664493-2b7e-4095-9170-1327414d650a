/* Admin Layout */
.admin-container {
    display: flex;
    min-height: 100vh;
    background: var(--bg-primary);
}

/* Sidebar */
.admin-sidebar {
    width: 280px;
    background: var(--bg-secondary);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-header h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin: 0;
}

.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 1rem 2rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    border-right: 3px solid transparent;
}

.nav-item:hover,
.nav-item.active {
    background: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
    border-right-color: var(--primary-color);
}

.nav-item i {
    margin-left: 1rem;
    width: 20px;
}

.sidebar-footer {
    padding: 2rem;
    border-top: 1px solid var(--border-color);
}

.logout-btn {
    width: 100%;
    background: #ef4444;
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 10px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.logout-btn:hover {
    background: #dc2626;
}

/* Main Content */
.admin-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.admin-header {
    background: var(--bg-secondary);
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-header h1 {
    color: var(--text-primary);
    margin: 0;
}

.header-actions .btn-primary {
    background: var(--gradient);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 10px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
}

/* Sections */
.admin-section {
    display: none;
    flex: 1;
    padding: 2rem;
}

.admin-section.active {
    display: block;
}

.section-content {
    max-width: 1200px;
    margin: 0 auto;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card i {
    font-size: 2rem;
    color: var(--primary-color);
}

.stat-card h3 {
    font-size: 2rem;
    margin: 0;
    color: var(--text-primary);
}

.stat-card p {
    margin: 0;
    color: var(--text-secondary);
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.admin-card {
    background: var(--bg-secondary);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    position: relative;
    transition: var(--transition);
}

.admin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.card-media {
    height: 200px;
    overflow: hidden;
}

.card-media img,
.card-media video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-content {
    padding: 1.5rem;
}

.card-actions {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    gap: 0.5rem;
    z-index: 10;
    opacity: 0;
    transition: var(--transition);
}

.admin-card:hover .card-actions {
    opacity: 1;
}

.action-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    color: white;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.edit-btn {
    background: var(--secondary-color);
}

.edit-btn:hover {
    background: #059669;
    transform: scale(1.1);
}

.delete-btn {
    background: #ef4444;
}

.delete-btn:hover {
    background: #dc2626;
    transform: scale(1.1);
}

/* تحسين مظهر الكروت */
.admin-card {
    background: var(--bg-secondary);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    position: relative;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.admin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* تحسين عرض المحتوى */
.card-media {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.card-media img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.admin-card:hover .card-media img {
    transform: scale(1.05);
}

.card-content {
    padding: 1.5rem;
}

.card-content h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.card-content p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* تصميم المعاينات */
.certificate-preview,
.skill-preview,
.experience-preview,
.course-preview {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--bg-primary);
    text-align: center;
    padding: 1rem;
}

.certificate-preview i,
.skill-preview i,
.experience-preview i,
.course-preview i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.certificate-preview h4,
.skill-preview h4,
.experience-preview h4,
.course-preview h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

/* شريط التقدم للمهارات */
.skill-level {
    margin-top: 1rem;
}

.skill-level span {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--border-color);
    border-radius: 3px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background: var(--gradient);
    border-radius: 3px;
    transition: width 1s ease-in-out;
}

/* زر الإضافة */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.add-btn {
    background: linear-gradient(135deg, #4f46e5, #06b6d4);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.add-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}

.add-btn:active {
    transform: translateY(0);
}

.add-btn i {
    font-size: 1rem;
}

/* النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--bg-primary);
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-secondary);
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.3rem;
}

.close {
    color: var(--text-secondary);
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close:hover {
    color: var(--text-primary);
    background: var(--border-color);
}

.modal-body {
    padding: 1.5rem;
}

/* النماذج */
.form-section {
    display: none;
}

.form-section.active {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 600;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* أزرار النموذج */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.btn-primary {
    background: linear-gradient(135deg, #4f46e5, #06b6d4);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: var(--border-color);
}

/* Profile Form */
.profile-form {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
}

.profile-form h3 {
    color: var(--primary-color);
    margin-bottom: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }
    
    .admin-sidebar {
        width: 100%;
        order: 2;
    }
    
    .admin-main {
        order: 1;
    }
    
    .sidebar-nav {
        display: flex;
        overflow-x: auto;
        padding: 0;
    }
    
    .nav-item {
        white-space: nowrap;
        border-right: none;
        border-bottom: 3px solid transparent;
    }
    
    .nav-item:hover,
    .nav-item.active {
        border-right: none;
        border-bottom-color: var(--primary-color);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

/* شريط التقدم */
.upload-progress {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin: 1rem 0;
    display: none;
}

.upload-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* تحسين معلومات الملف */
.file-info {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    padding: 1rem;
    border-radius: 10px;
    margin: 1rem 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.file-info strong {
    color: var(--text-primary);
}

/* تحسين النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: var(--transition);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* رسائل النجاح والخطأ */
.success-message,
.error-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    z-index: 3000;
    animation: slideIn 0.3s ease;
}

.success-message {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.error-message {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تصميم المحتوى الجديد */
.content-fields {
    margin-top: 1rem;
}

.website-preview {
    text-align: center;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: 10px;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.website-preview i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.website-preview h4 {
    margin: 0.5rem 0;
    color: var(--text-primary);
}

.website-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    transition: var(--transition);
}

.website-link:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.youtube-preview {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 10px;
}

.youtube-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.youtube-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 0, 0, 0.9);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.youtube-overlay:hover {
    background: rgba(255, 0, 0, 1);
    transform: translate(-50%, -50%) scale(1.1);
}

/* تحسين النماذج */
.form-group input[type="url"] {
    direction: ltr;
    text-align: left;
}

.form-group input[type="url"]:focus {
    border-color: var(--secondary-color);
}

/* تحسين الكاردات */
.admin-card .card-media {
    height: 200px;
    overflow: hidden;
    border-radius: 10px 10px 0 0;
}

.admin-card .card-media img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* حالة فارغة */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--border-color);
}

/* رسائل التنبيه */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    z-index: 3000;
    animation: slideIn 0.3s ease;
}

.message.success {
    background: #10b981;
}

.message.error {
    background: #ef4444;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Profile Styles */
.profile-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.profile-card {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.profile-card h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.profile-image-section {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
}

.current-image {
    flex-shrink: 0;
}

.current-image img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
}

.image-controls {
    flex: 1;
}

.nav-item a {
    color: inherit;
    text-decoration: none;
    display: flex;
    align-items: center;
    width: 100%;
}

.nav-item a i {
    margin-left: 1rem;
    width: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }

    .admin-sidebar {
        width: 100%;
        height: auto;
    }

    .sidebar-nav {
        display: flex;
        overflow-x: auto;
        padding: 0;
    }

    .nav-item {
        min-width: 120px;
        text-align: center;
        padding: 1rem;
    }

    .admin-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 1rem;
        width: calc(100% - 2rem);
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .profile-image-section {
        flex-direction: column;
        text-align: center;
    }
}

/* تحسينات جديدة */
.website-preview {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}

.website-preview i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.website-url {
    font-size: 0.9rem;
    opacity: 0.8;
    word-break: break-all;
}

.card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
}

.image-preview {
    margin-top: 1rem;
    text-align: center;
}

.action-btn.view-btn {
    background: #10b981;
}

.action-btn.view-btn:hover {
    background: #059669;
}

.card-date {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    display: block;
}

/* نافذة عرض الصورة بملء الشاشة */
.fullscreen-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 4000;
}

.fullscreen-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.fullscreen-content img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 10px;
}

.close-fullscreen {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.close-fullscreen:hover {
    background: rgba(0, 0, 0, 0.8);
}

/* تحسين نموذج رفع الصور */
input[type="file"] {
    padding: 0.75rem;
    border: 2px dashed var(--border-color);
    border-radius: 10px;
    background: var(--bg-primary);
    cursor: pointer;
    transition: var(--transition);
}

input[type="file"]:hover {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.05);
}

/* تحسين البطاقات */
.admin-card {
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.admin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
}

.admin-card:hover .card-actions {
    opacity: 1;
}

.action-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 0.9rem;
}

.action-btn.edit-btn {
    background: #f59e0b;
}

.action-btn.edit-btn:hover {
    background: #d97706;
}

.action-btn.delete-btn {
    background: #ef4444;
}

.action-btn.delete-btn:hover {
    background: #dc2626;
}

/* رسائل النظام */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    color: white;
    font-weight: 500;
    z-index: 5000;
    animation: slideInRight 0.3s ease;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.message.success {
    background: #10b981;
}

.message.error {
    background: #ef4444;
}

.message.warning {
    background: #f59e0b;
}

.message.info {
    background: #3b82f6;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسينات للغات */
.language-item {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.language-item .form-grid {
    align-items: end;
}

.btn-danger {
    background: #ef4444;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.btn-danger:hover {
    background: #dc2626;
}






