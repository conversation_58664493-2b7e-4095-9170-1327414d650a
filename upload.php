<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// إعدادات الرفع
$uploadDir = __DIR__ . '/image/';
$allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
$maxFileSize = 5 * 1024 * 1024; // 5MB

// التحقق من وجود مجلد الصور
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير صحيحة');
    }
    
    if (!isset($_FILES['image'])) {
        throw new Exception('لم يتم اختيار صورة');
    }
    
    $file = $_FILES['image'];
    
    // التحقق من وجود أخطاء في الرفع
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('خطأ في رفع الصورة');
    }
    
    // التحقق من نوع الملف
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('نوع الصورة غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WebP');
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > $maxFileSize) {
        throw new Exception('حجم الصورة كبير جداً. الحد الأقصى 5MB');
    }
    
    // إنشاء اسم فريد للملف
    $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $fileName = 'img_' . time() . '_' . uniqid() . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;
    
    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        // إضافة الصورة إلى ملف JSON
        $dataFile = __DIR__ . '/data/images.json';
        $images = [];
        
        if (file_exists($dataFile)) {
            $content = file_get_contents($dataFile);
            $images = json_decode($content, true) ?: [];
        }
        
        // إنشاء معرف جديد
        $newId = empty($images) ? 1 : max(array_column($images, 'id')) + 1;
        
        // إضافة بيانات الصورة
        $imageData = [
            'id' => $newId,
            'title' => $_POST['title'] ?? 'صورة جديدة',
            'filename' => $fileName,
            'description' => $_POST['description'] ?? '',
            'date' => date('Y-m-d'),
            'order' => count($images) + 1
        ];
        
        $images[] = $imageData;
        
        // حفظ البيانات
        file_put_contents($dataFile, json_encode($images, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        echo json_encode([
            'success' => true,
            'message' => 'تم رفع الصورة بنجاح',
            'data' => $imageData,
            'url' => 'image/' . $fileName
        ]);
        
    } else {
        throw new Exception('فشل في حفظ الصورة');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
