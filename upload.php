<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// تسجيل الأخطاء للتشخيص
error_log("=== بدء معالجة رفع الصورة ===");

// إعدادات الرفع
$uploadDir = __DIR__ . '/image/';
$allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
$maxFileSize = 5 * 1024 * 1024; // 5MB

error_log("مجلد الرفع: " . $uploadDir);

// التحقق من وجود مجلد الصور
if (!is_dir($uploadDir)) {
    $created = mkdir($uploadDir, 0755, true);
    error_log("إنشاء مجلد الصور: " . ($created ? "نجح" : "فشل"));
}

// التحقق من صلاحيات الكتابة
if (!is_writable($uploadDir)) {
    error_log("خطأ: لا توجد صلاحيات كتابة في مجلد الصور");
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير صحيحة');
    }

    error_log("البيانات المستلمة: " . print_r($_POST, true));
    error_log("الملفات المستلمة: " . print_r($_FILES, true));

    if (!isset($_FILES['image'])) {
        throw new Exception('لم يتم اختيار صورة');
    }

    $file = $_FILES['image'];

    // التحقق من وجود أخطاء في الرفع
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => 'حجم الملف أكبر من الحد المسموح في إعدادات الخادم',
            UPLOAD_ERR_FORM_SIZE => 'حجم الملف أكبر من الحد المسموح في النموذج',
            UPLOAD_ERR_PARTIAL => 'تم رفع جزء من الملف فقط',
            UPLOAD_ERR_NO_FILE => 'لم يتم رفع أي ملف',
            UPLOAD_ERR_NO_TMP_DIR => 'مجلد مؤقت غير موجود',
            UPLOAD_ERR_CANT_WRITE => 'فشل في كتابة الملف',
            UPLOAD_ERR_EXTENSION => 'امتداد PHP أوقف رفع الملف'
        ];
        $errorMsg = $errorMessages[$file['error']] ?? 'خطأ غير معروف في رفع الصورة';
        error_log("خطأ في رفع الملف: " . $file['error'] . " - " . $errorMsg);
        throw new Exception($errorMsg);
    }
    
    // التحقق من نوع الملف
    error_log("نوع الملف: " . $file['type']);
    error_log("حجم الملف: " . $file['size'] . " بايت");

    if (!in_array($file['type'], $allowedTypes)) {
        error_log("نوع ملف غير مدعوم: " . $file['type']);
        throw new Exception('نوع الصورة غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WebP');
    }

    // التحقق من حجم الملف
    if ($file['size'] > $maxFileSize) {
        error_log("حجم الملف كبير جداً: " . $file['size']);
        throw new Exception('حجم الصورة كبير جداً. الحد الأقصى 5MB');
    }

    // إنشاء اسم فريد للملف
    $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $fileName = 'img_' . time() . '_' . uniqid() . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;

    error_log("اسم الملف الجديد: " . $fileName);
    error_log("مسار الملف: " . $filePath);
    
    // رفع الملف
    error_log("محاولة نقل الملف من: " . $file['tmp_name'] . " إلى: " . $filePath);

    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        error_log("تم نقل الملف بنجاح");

        // إضافة الصورة إلى ملف JSON
        $dataDir = __DIR__ . '/data/';
        $dataFile = $dataDir . 'images.json';
        $images = [];

        error_log("مجلد البيانات: " . $dataDir);
        error_log("ملف البيانات: " . $dataFile);

        // التحقق من وجود مجلد البيانات
        if (!is_dir($dataDir)) {
            $created = mkdir($dataDir, 0755, true);
            error_log("إنشاء مجلد البيانات: " . ($created ? "نجح" : "فشل"));
        }

        // التحقق من صلاحيات الكتابة
        if (!is_writable($dataDir)) {
            error_log("خطأ: لا توجد صلاحيات كتابة في مجلد البيانات");
            throw new Exception('لا توجد صلاحيات كتابة في مجلد البيانات');
        }

        if (file_exists($dataFile)) {
            $content = file_get_contents($dataFile);
            $images = json_decode($content, true) ?: [];
            error_log("تم تحميل " . count($images) . " صورة موجودة");
        } else {
            error_log("ملف البيانات غير موجود، سيتم إنشاؤه");
        }
        
        // إنشاء معرف جديد
        $newId = empty($images) ? 1 : max(array_column($images, 'id')) + 1;
        
        // إضافة بيانات الصورة
        $imageData = [
            'id' => $newId,
            'title' => $_POST['title'] ?? 'صورة جديدة',
            'filename' => $fileName,
            'description' => $_POST['description'] ?? '',
            'date' => date('Y-m-d'),
            'order' => count($images) + 1
        ];
        
        $images[] = $imageData;

        // حفظ البيانات
        error_log("محاولة حفظ البيانات في: " . $dataFile);
        $jsonData = json_encode($images, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        $saveResult = file_put_contents($dataFile, $jsonData);

        if ($saveResult === false) {
            error_log("فشل في حفظ ملف البيانات");
            throw new Exception('فشل في حفظ بيانات الصورة');
        }

        error_log("تم حفظ البيانات بنجاح. حجم الملف: " . $saveResult . " بايت");

        echo json_encode([
            'success' => true,
            'message' => 'تم رفع الصورة بنجاح',
            'data' => $imageData,
            'url' => 'image/' . $fileName
        ]);
        
    } else {
        throw new Exception('فشل في حفظ الصورة');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
