<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

try {
    // مسار ملف البيانات
    $dataDir = __DIR__ . '/data/';
    $personalInfoFile = $dataDir . 'personal-info.json';
    
    // التحقق من وجود الملف
    if (!file_exists($personalInfoFile)) {
        // إرجاع البيانات الافتراضية
        $defaultData = [
            'name' => 'طارق محمد الشتيوي',
            'subtitle' => 'شاب متحمس ومحب لتخصص الحاسب الالي',
            'email' => '<EMAIL>',
            'phone' => '0503839769',
            'location' => 'القصيم، بريدة',
            'education' => 'خريج ثانوية صالح العثيم',
            'gpa' => 'معدل تراكمي: 95%'
        ];
        
        echo json_encode([
            'success' => true,
            'data' => $defaultData
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // قراءة البيانات من الملف
    $content = file_get_contents($personalInfoFile);
    $data = json_decode($content, true);
    
    if ($data === null) {
        throw new Exception('خطأ في قراءة البيانات');
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
