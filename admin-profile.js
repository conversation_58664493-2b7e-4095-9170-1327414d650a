// ملف JavaScript لإدارة الملف الشخصي

let profileData = {};
let languagesData = [];

// تحميل بيانات الملف الشخصي
document.addEventListener('DOMContentLoaded', function() {
    loadProfileData();
    setupEventListeners();
});

// تحميل البيانات من ملف JSON
async function loadProfileData() {
    try {
        // تحميل المعلومات الشخصية من الملف الجديد
        const personalResponse = await fetch('get-personal-info.php');
        const personalResult = await personalResponse.json();

        let personalInfo = {};
        if (personalResult.success && personalResult.data) {
            personalInfo = personalResult.data;
        }

        // تحميل باقي البيانات من الملف القديم
        let otherData = {};
        try {
            const response = await fetch('get-data.php?type=profile');
            const result = await response.json();
            if (result.success && result.data) {
                otherData = result.data;
            }
        } catch (error) {
            console.log('لا توجد بيانات إضافية');
        }

        // دمج البيانات
        profileData = {
            personal_info: personalInfo,
            about_section: otherData.about_section || {},
            languages_section: otherData.languages_section || {}
        };

        populateForm();

    } catch (error) {
        console.error('Error loading profile data:', error);
        // تحميل البيانات الافتراضية
        loadDefaultData();
    }
}

// تحميل البيانات الافتراضية
function loadDefaultData() {
    profileData = {
        personal_info: {
            name: "طارق محمد الشتيوي",
            subtitle: "شاب هاوٍ ومحب للحاسب الالي",
            location: "القصيم، بريدة",
            email: "<EMAIL>",
            phone: "0503839769",
            education: "خريج ثانوية صالح العثيم",
            gpa: "معدل تراكمي: 95%"
        },
        about_section: {
            title: "نبذة عني",
            paragraphs: [
                "أتمتع بخلفية تقنية قوية في مجالات متعددة مثل البرمجة، الأمن السيبراني، والصيانة التقنية.",
                "أستمتع بتعلم التقنيات الحديثة وأتطلع دائمًا لتطبيق المهارات المكتسبة في مشاريع مبتكرة."
            ]
        },
        languages_section: {
            title: "اللغات",
            languages: [
                { name: "العربية", level: "اللغة الأم", percentage: 100 },
                { name: "الإنجليزية", level: "مستوى متوسط", percentage: 40 }
            ]
        }
    };
    populateForm();
}

// ملء النموذج بالبيانات
function populateForm() {
    // المعلومات الشخصية
    const personalInfo = profileData.personal_info || {};
    document.getElementById('name').value = personalInfo.name || '';
    document.getElementById('subtitle').value = personalInfo.subtitle || '';
    document.getElementById('email').value = personalInfo.email || '';
    document.getElementById('phone').value = personalInfo.phone || '';
    document.getElementById('location').value = personalInfo.location || '';
    document.getElementById('education').value = personalInfo.education || '';
    document.getElementById('gpa').value = personalInfo.gpa || '';
    
    // قسم نبذة عني
    const aboutSection = profileData.about_section || {};
    document.getElementById('aboutTitle').value = aboutSection.title || 'نبذة عني';
    document.getElementById('aboutParagraph1').value = aboutSection.paragraphs?.[0] || '';
    document.getElementById('aboutParagraph2').value = aboutSection.paragraphs?.[1] || '';
    
    // قسم اللغات
    const languagesSection = profileData.languages_section || {};
    document.getElementById('languagesTitle').value = languagesSection.title || 'اللغات';
    languagesData = languagesSection.languages || [];
    renderLanguages();

    // روابط التواصل الاجتماعي
    const socialLinks = profileData.social_links || {};
    document.getElementById('linkedin').value = socialLinks.linkedin || '';
    document.getElementById('github').value = socialLinks.github || '';
    document.getElementById('twitter').value = socialLinks.twitter || '';
    document.getElementById('instagram').value = socialLinks.instagram || '';
    document.getElementById('socialEmail').value = socialLinks.email || '';
    document.getElementById('socialPhone').value = socialLinks.phone || '';
    document.getElementById('youtube').value = socialLinks.youtube || '';
}

// عرض اللغات
function renderLanguages() {
    const container = document.getElementById('languagesList');
    container.innerHTML = '';
    
    languagesData.forEach((language, index) => {
        const languageDiv = document.createElement('div');
        languageDiv.className = 'language-item';
        languageDiv.innerHTML = `
            <div class="form-grid">
                <div class="form-group">
                    <label>اسم اللغة:</label>
                    <input type="text" value="${language.name}" onchange="updateLanguage(${index}, 'name', this.value)">
                </div>
                <div class="form-group">
                    <label>المستوى:</label>
                    <input type="text" value="${language.level}" onchange="updateLanguage(${index}, 'level', this.value)">
                </div>
                <div class="form-group">
                    <label>النسبة المئوية:</label>
                    <input type="number" min="0" max="100" value="${language.percentage}" onchange="updateLanguage(${index}, 'percentage', this.value)">
                </div>
                <div class="form-group">
                    <button type="button" class="btn-danger" onclick="removeLanguage(${index})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
        container.appendChild(languageDiv);
    });
}

// إضافة لغة جديدة
function addLanguage() {
    languagesData.push({
        name: '',
        level: '',
        percentage: 0
    });
    renderLanguages();
}

// تحديث لغة
function updateLanguage(index, field, value) {
    if (languagesData[index]) {
        languagesData[index][field] = field === 'percentage' ? parseInt(value) : value;
    }
}

// حذف لغة
function removeLanguage(index) {
    if (confirm('هل أنت متأكد من حذف هذه اللغة؟')) {
        languagesData.splice(index, 1);
        renderLanguages();
    }
}

// إعداد معالجات الأحداث
function setupEventListeners() {
    // لا حاجة لمعالجات خاصة حالياً
}

// حفظ الملف الشخصي
async function saveProfile() {
    try {
        // جمع البيانات من النموذج
        const updatedProfile = {
            personal_info: {
                name: document.getElementById('name').value,
                subtitle: document.getElementById('subtitle').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                location: document.getElementById('location').value,
                education: document.getElementById('education').value,
                gpa: document.getElementById('gpa').value,
                profile_image: profileData.personal_info?.profile_image || "photo/7.jpg"
            },
            about_section: {
                title: document.getElementById('aboutTitle').value,
                paragraphs: [
                    document.getElementById('aboutParagraph1').value,
                    document.getElementById('aboutParagraph2').value
                ]
            },
            languages_section: {
                title: document.getElementById('languagesTitle').value,
                languages: languagesData
            },
            hero_buttons: profileData.hero_buttons || {
                contact_text: "تواصل معي",
                about_text: "اعرف المزيد"
            },
            floating_icons: profileData.floating_icons || [],
            social_links: profileData.social_links || {},
            meta_data: profileData.meta_data || {}
        };
        
        // حفظ البيانات
        const response = await fetch('save-profile.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updatedProfile)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('تم حفظ الملف الشخصي بنجاح!', 'success');
            profileData = updatedProfile;
        } else {
            throw new Error(result.message);
        }
        
    } catch (error) {
        console.error('Error saving profile:', error);
        showMessage('خطأ في حفظ الملف الشخصي: ' + error.message, 'error');
    }
}

// تحديث صورة الملف الشخصي
function updateProfileImage() {
    const imageUrl = document.getElementById('profileImageUrl').value;
    const preview = document.getElementById('profileImagePreview');
    
    if (imageUrl && preview) {
        preview.src = imageUrl;
        if (profileData.personal_info) {
            profileData.personal_info.profile_image = imageUrl;
        }
        showMessage('تم تحديث الصورة!', 'success');
    } else {
        showMessage('يرجى إدخال رابط صحيح للصورة', 'error');
    }
}

// تغيير كلمة المرور
function changePassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (!currentPassword || !newPassword || !confirmPassword) {
        showMessage('يرجى ملء جميع حقول كلمة المرور', 'error');
        return;
    }
    
    if (newPassword !== confirmPassword) {
        showMessage('كلمة المرور الجديدة وتأكيدها غير متطابقين', 'error');
        return;
    }
    
    if (newPassword.length < 6) {
        showMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return;
    }
    
    // هنا يمكن إضافة التحقق من كلمة المرور الحالية
    localStorage.setItem('adminPassword', newPassword);
    
    // مسح الحقول
    document.getElementById('currentPassword').value = '';
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmPassword').value = '';
    
    showMessage('تم تغيير كلمة المرور بنجاح!', 'success');
}

// عرض رسالة
function showMessage(message, type = 'success') {
    // إزالة الرسائل السابقة
    const existingMessages = document.querySelectorAll('.message');
    existingMessages.forEach(msg => msg.remove());
    
    // إنشاء رسالة جديدة
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    // إضافة الرسالة للصفحة
    document.body.appendChild(messageDiv);
    
    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

// حفظ المعلومات الشخصية
async function savePersonalInfo() {
    try {
        console.log('=== بدء حفظ المعلومات الشخصية ===');

        const personalInfo = {
            name: document.getElementById('name').value.trim(),
            subtitle: document.getElementById('subtitle').value.trim(),
            email: document.getElementById('email').value.trim(),
            phone: document.getElementById('phone').value.trim(),
            location: document.getElementById('location').value.trim(),
            education: document.getElementById('education').value.trim(),
            gpa: document.getElementById('gpa').value.trim()
        };

        console.log('البيانات الجديدة:', personalInfo);

        // حفظ في الخادم باستخدام الملف الجديد
        const response = await fetch('save-personal-info.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(personalInfo)
        });

        console.log('استجابة الخادم:', response.status);

        const result = await response.json();
        console.log('نتيجة الحفظ:', result);

        if (result.success) {
            // حفظ في localStorage أيضاً
            localStorage.setItem('personalInfo', JSON.stringify(personalInfo));

            showMessage('تم حفظ المعلومات الشخصية بنجاح!', 'success');

            console.log('تم حفظ البيانات بنجاح');
        } else {
            throw new Error(result.message || 'فشل في حفظ البيانات');
        }

    } catch (error) {
        console.error('خطأ في حفظ المعلومات الشخصية:', error);
        showMessage('خطأ في حفظ المعلومات: ' + error.message, 'error');
    }
}

// حفظ نبذة عني
function saveAboutInfo() {
    const aboutInfo = {
        title: document.getElementById('aboutTitle').value,
        paragraphs: [
            document.getElementById('aboutParagraph1').value,
            document.getElementById('aboutParagraph2').value
        ]
    };

    profileData.about_section = aboutInfo;
    localStorage.setItem('profileData', JSON.stringify(profileData));
    showMessage('تم حفظ قسم نبذة عني بنجاح!', 'success');
}

// حفظ اللغات
function saveLanguages() {
    profileData.languages_section = {
        title: document.getElementById('languagesTitle').value,
        languages: languagesData
    };

    localStorage.setItem('profileData', JSON.stringify(profileData));
    showMessage('تم حفظ قسم اللغات بنجاح!', 'success');
}

// حفظ روابط التواصل الاجتماعي
async function saveSocialLinks() {
    const socialLinks = {
        linkedin: document.getElementById('linkedin').value,
        github: document.getElementById('github').value,
        twitter: document.getElementById('twitter').value,
        instagram: document.getElementById('instagram').value,
        email: document.getElementById('socialEmail').value,
        phone: document.getElementById('socialPhone').value,
        youtube: document.getElementById('youtube').value
    };

    try {
        // حفظ في الخادم
        const response = await fetch('save-social-links.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(socialLinks)
        });

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || 'فشل في حفظ البيانات');
        }

        // حفظ في localStorage
        profileData.social_links = socialLinks;
        localStorage.setItem('profileData', JSON.stringify(profileData));

        // إشعار النوافذ الأخرى بالتحديث
        localStorage.setItem('socialLinksUpdated', Date.now().toString());

        showMessage('تم حفظ روابط التواصل الاجتماعي بنجاح!', 'success');

    } catch (error) {
        console.error('Error saving social links:', error);
        showMessage('خطأ في حفظ روابط التواصل الاجتماعي: ' + error.message, 'error');
    }
}

// حفظ إعدادات الموقع
function saveSiteSettings() {
    const siteSettings = {
        title: document.getElementById('siteTitle').value,
        description: document.getElementById('siteDescription').value,
        keywords: document.getElementById('siteKeywords').value,
        language: document.getElementById('siteLanguage').value
    };

    profileData.site_settings = siteSettings;
    localStorage.setItem('profileData', JSON.stringify(profileData));
    showMessage('تم حفظ إعدادات الموقع بنجاح!', 'success');
}

// رفع صورة من الكمبيوتر
async function uploadProfileImage(input) {
    const file = input.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
        showMessage('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG أو GIF', 'error');
        input.value = '';
        return;
    }

    // التحقق من حجم الملف (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
        showMessage('حجم الملف كبير جداً. الحد الأقصى هو 5MB', 'error');
        input.value = '';
        return;
    }

    // إظهار مؤشر التحميل
    const overlay = document.getElementById('imageOverlay');
    overlay.style.display = 'flex';

    try {
        const formData = new FormData();
        formData.append('image', file);

        const response = await fetch('upload-image.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            // تحديث معاينة الصورة
            const preview = document.getElementById('profileImagePreview');
            preview.src = result.url + '?t=' + new Date().getTime(); // إضافة timestamp لتجنب cache

            // حفظ مسار الصورة
            profileData.profile_image = result.url;
            localStorage.setItem('profileData', JSON.stringify(profileData));

            showMessage('تم رفع الصورة وحفظها بنجاح!', 'success');
        } else {
            showMessage(result.message || 'فشل في رفع الصورة', 'error');
        }
    } catch (error) {
        console.error('خطأ في رفع الصورة:', error);
        showMessage('حدث خطأ أثناء رفع الصورة', 'error');
    } finally {
        // إخفاء مؤشر التحميل
        overlay.style.display = 'none';
        input.value = ''; // مسح اختيار الملف
    }
}

// تحديث الصورة من الرابط
function updateProfileImageFromUrl() {
    const imageUrl = document.getElementById('profileImageUrl').value;

    if (!imageUrl) {
        showMessage('يرجى إدخال رابط الصورة أولاً!', 'warning');
        return;
    }

    const preview = document.getElementById('profileImagePreview');
    const overlay = document.getElementById('imageOverlay');

    // إظهار مؤشر التحميل
    overlay.style.display = 'flex';

    // إنشاء صورة جديدة للتحقق من صحة الرابط
    const img = new Image();

    img.onload = function() {
        preview.src = imageUrl;
        overlay.style.display = 'none';
        showMessage('تم تحديث معاينة الصورة بنجاح!', 'success');
    };

    img.onerror = function() {
        overlay.style.display = 'none';
        showMessage('رابط الصورة غير صحيح أو لا يمكن الوصول إليه', 'error');
    };

    img.src = imageUrl;
}

// استعادة الصورة الافتراضية
function resetProfileImage() {
    const preview = document.getElementById('profileImagePreview');
    const defaultImage = 'photo/7.jpg';

    preview.src = defaultImage;
    document.getElementById('profileImageUrl').value = '';

    profileData.profile_image = defaultImage;
    localStorage.setItem('profileData', JSON.stringify(profileData));

    showMessage('تم استعادة الصورة الافتراضية', 'success');
}

// حفظ الصورة الشخصية
function saveProfileImage() {
    const currentSrc = document.getElementById('profileImagePreview').src;

    if (currentSrc) {
        // استخراج المسار النسبي من الـ URL الكامل
        const url = new URL(currentSrc);
        const relativePath = url.pathname.substring(url.pathname.lastIndexOf('/') + 1);

        profileData.profile_image = relativePath.includes('image/') ? currentSrc.split(window.location.origin + '/')[1] : currentSrc;
        localStorage.setItem('profileData', JSON.stringify(profileData));
        showMessage('تم حفظ الصورة الشخصية بنجاح!', 'success');
    } else {
        showMessage('لا توجد صورة للحفظ!', 'warning');
    }
}

// تسجيل الخروج
function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        localStorage.removeItem('adminLoggedIn');
        window.location.href = 'index.html';
    }
}
