<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة الدورات</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 20px; margin: 5px; background: #4f46e5; color: white; border: none; border-radius: 5px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>تشخيص مشكلة الدورات</h1>
    
    <div class="test-section">
        <h2>1. اختبار ملف courses.json مباشرة</h2>
        <button onclick="testDirectFile()">اختبار الملف مباشرة</button>
        <div id="direct-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. اختبار get-data.php</h2>
        <button onclick="testGetData()">اختبار get-data.php</button>
        <div id="getdata-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. اختبار api.php</h2>
        <button onclick="testApiPhp()">اختبار api.php</button>
        <div id="api-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. اختبار admin-new.js</h2>
        <button onclick="testAdminJs()">اختبار تحميل البيانات</button>
        <div id="adminjs-result"></div>
    </div>

    <script>
        // اختبار الملف مباشرة
        async function testDirectFile() {
            const resultDiv = document.getElementById('direct-result');
            try {
                const response = await fetch('data/courses.json');
                const data = await response.json();
                resultDiv.innerHTML = `
                    <p class="success">✅ تم تحميل الملف مباشرة</p>
                    <p class="info">عدد الدورات: ${data.length}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ: ${error.message}</p>`;
            }
        }
        
        // اختبار get-data.php
        async function testGetData() {
            const resultDiv = document.getElementById('getdata-result');
            try {
                const response = await fetch('get-data.php?type=courses');
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <p class="success">✅ get-data.php يعمل بشكل صحيح</p>
                        <p class="info">عدد الدورات: ${result.data.length}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<p class="error">❌ خطأ من get-data.php: ${result.message}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ في الاتصال: ${error.message}</p>`;
            }
        }
        
        // اختبار api.php
        async function testApiPhp() {
            const resultDiv = document.getElementById('api-result');
            try {
                const response = await fetch('api.php?section=courses');
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <p class="success">✅ api.php يعمل بشكل صحيح</p>
                        <p class="info">عدد الدورات: ${result.data.length}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<p class="error">❌ خطأ من api.php: ${result.message}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ في الاتصال: ${error.message}</p>`;
            }
        }
        
        // اختبار admin-new.js
        async function testAdminJs() {
            const resultDiv = document.getElementById('adminjs-result');
            try {
                // محاكاة تحميل البيانات كما في admin-new.js
                let courses = [];
                
                const response = await fetch('api.php?section=courses');
                const result = await response.json();
                
                if (result.success) {
                    courses = result.data;
                    resultDiv.innerHTML = `
                        <p class="success">✅ تحميل البيانات في admin-new.js يعمل</p>
                        <p class="info">عدد الدورات المحملة: ${courses.length}</p>
                        <p class="info">البيانات:</p>
                        <pre>${JSON.stringify(courses, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<p class="error">❌ فشل تحميل البيانات: ${result.message}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ: ${error.message}</p>`;
            }
        }
        
        // تشغيل جميع الاختبارات تلقائياً
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testDirectFile();
                setTimeout(() => testGetData(), 500);
                setTimeout(() => testApiPhp(), 1000);
                setTimeout(() => testAdminJs(), 1500);
            }, 500);
        });
    </script>
</body>
</html>
