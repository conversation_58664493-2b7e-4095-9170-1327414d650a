/* ===== نافذة تسجيل الدخول ===== */

/* الخلفية الشفافة */
.login-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.login-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* النافذة الرئيسية */
.login-modal {
    background: var(--bg-primary, #ffffff);
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 400px;
    margin: 20px;
    transform: scale(0.8) translateY(50px);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    overflow: hidden;
    border: 1px solid var(--border-color, #e5e7eb);
}

.login-overlay.active .login-modal {
    transform: scale(1) translateY(0);
}

/* رأس النافذة */
.login-header {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
}

.login-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.login-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.login-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0 0 0.5rem;
}

.login-subtitle {
    opacity: 0.9;
    font-size: 1rem;
    margin: 0;
}

/* محتوى النافذة */
.login-content {
    padding: 2rem;
}

/* النموذج */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    position: relative;
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary, #1f2937);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--border-color, #e5e7eb);
    border-radius: 12px;
    font-size: 1rem;
    background: var(--bg-secondary, #f9fafb);
    color: var(--text-primary, #1f2937);
    transition: all 0.3s ease;
    outline: none;
}

.form-input:focus {
    border-color: #4f46e5;
    background: var(--bg-primary, #ffffff);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary, #6b7280);
    font-size: 1.1rem;
    pointer-events: none;
    transition: color 0.3s ease;
}

.form-input:focus + .form-icon {
    color: #4f46e5;
}

/* زر تسجيل الدخول */
.login-submit {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-submit:hover {
    background: linear-gradient(135deg, #3730a3, #6b21a8);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(79, 70, 229, 0.4);
}

.login-submit:active {
    transform: translateY(0);
}

.login-submit:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* حالة التحميل */
.login-submit.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* رسائل الخطأ */
.error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    margin-top: 1rem;
    display: none;
}

.error-message.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* روابط إضافية */
.login-footer {
    padding: 1rem 2rem 2rem;
    text-align: center;
    border-top: 1px solid var(--border-color, #e5e7eb);
    background: var(--bg-secondary, #f9fafb);
}

.forgot-password {
    color: #4f46e5;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #3730a3;
    text-decoration: underline;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 480px) {
    .login-modal {
        margin: 10px;
        border-radius: 15px;
    }
    
    .login-header {
        padding: 1.5rem;
    }
    
    .login-title {
        font-size: 1.5rem;
    }
    
    .login-content {
        padding: 1.5rem;
    }
    
    .form-input {
        padding: 0.9rem 0.9rem 0.9rem 2.5rem;
    }
    
    .form-icon {
        left: 0.8rem;
        font-size: 1rem;
    }
    
    .login-submit {
        padding: 0.9rem 1.5rem;
    }
    
    .login-footer {
        padding: 1rem 1.5rem 1.5rem;
    }
}

/* تحسينات للثيم الداكن */
[data-theme="dark"] .login-modal {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .form-input {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .form-input:focus {
    background: var(--bg-primary);
}

[data-theme="dark"] .login-footer {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .error-message {
    background: rgba(220, 38, 38, 0.1);
    border-color: rgba(220, 38, 38, 0.3);
    color: #fca5a5;
}
