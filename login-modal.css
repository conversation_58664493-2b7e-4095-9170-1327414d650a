/* ===== نافذة تسجيل الدخول ===== */

/* الخلفية الشفافة */
.login-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    animation: fadeIn 0.4s ease-out;
}

.login-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* النافذة الرئيسية */
.login-modal {
    background: var(--bg-primary, #ffffff);
    border-radius: 24px;
    box-shadow:
        0 32px 64px rgba(0, 0, 0, 0.4),
        0 16px 32px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    width: 100%;
    max-width: 450px;
    margin: 20px;
    transform: scale(0.7) translateY(100px) rotateX(15deg);
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    overflow: hidden;
    border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
    position: relative;
}

.login-overlay.active .login-modal {
    transform: scale(1) translateY(0) rotateX(0deg);
}

/* تأثير الجزيئات في الخلفية */
.login-modal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* رأس النافذة */
.login-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    color: white;
    padding: 2.5rem 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.login-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: float 20s linear infinite;
    opacity: 0.3;
}

@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.login-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.login-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.login-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0 0 0.5rem;
}

.login-subtitle {
    opacity: 0.9;
    font-size: 1rem;
    margin: 0;
}

/* محتوى النافذة */
.login-content {
    padding: 2rem;
}

/* النموذج */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    position: relative;
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary, #1f2937);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 1.2rem 1.2rem 1.2rem 3.5rem;
    border: 2px solid var(--border-color, #e5e7eb);
    border-radius: 16px;
    font-size: 1rem;
    background: var(--bg-secondary, #f8fafc);
    color: var(--text-primary, #1f2937);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    outline: none;
    position: relative;
    font-family: 'Tajawal', sans-serif;
}

.form-input:focus {
    border-color: #4f46e5;
    background: var(--bg-primary, #ffffff);
    box-shadow:
        0 0 0 4px rgba(79, 70, 229, 0.1),
        0 8px 25px rgba(79, 70, 229, 0.15);
    transform: translateY(-2px);
}

.form-input:hover {
    border-color: #a855f7;
    transform: translateY(-1px);
}

.form-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary, #6b7280);
    font-size: 1.1rem;
    pointer-events: none;
    transition: color 0.3s ease;
}

.form-input:focus + .form-icon {
    color: #4f46e5;
}

/* زر تسجيل الدخول */
.login-submit {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    color: white;
    border: none;
    padding: 1.2rem 2rem;
    border-radius: 16px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    width: 100%;
    font-family: 'Tajawal', sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.login-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.login-submit:hover::before {
    left: 100%;
}

.login-submit:hover {
    background: linear-gradient(135deg, #3730a3 0%, #6b21a8 50%, #db2777 100%);
    transform: translateY(-3px);
    box-shadow:
        0 15px 35px rgba(79, 70, 229, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.1);
}

.login-submit:active {
    transform: translateY(-1px);
}

.login-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    background: #9ca3af;
}

/* حالة التحميل */
.login-submit.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* رسائل الخطأ والنجاح */
.error-message {
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 1rem 1.2rem;
    border-radius: 12px;
    font-size: 0.9rem;
    margin-top: 1rem;
    display: none;
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

.error-message::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #dc2626;
}

.error-message.show {
    display: block;
    animation: slideDown 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.success-message {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    border: 1px solid #bbf7d0;
    color: #16a34a;
    padding: 1rem 1.2rem;
    border-radius: 12px;
    font-size: 0.9rem;
    margin-top: 1rem;
    display: none;
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

.success-message::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #16a34a;
}

.success-message.show {
    display: block;
    animation: slideDown 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-15px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(15px);
    }
}

/* تأثيرات إضافية */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.login-modal.shake {
    animation: shake 0.5s ease-in-out;
}

/* تأثير النجاح */
.login-modal.success {
    animation: pulse 0.6s ease-in-out;
}

/* روابط إضافية */
.login-footer {
    padding: 1rem 2rem 2rem;
    text-align: center;
    border-top: 1px solid var(--border-color, #e5e7eb);
    background: var(--bg-secondary, #f9fafb);
}

.forgot-password {
    color: #4f46e5;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #3730a3;
    text-decoration: underline;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 480px) {
    .login-modal {
        margin: 10px;
        border-radius: 15px;
    }
    
    .login-header {
        padding: 1.5rem;
    }
    
    .login-title {
        font-size: 1.5rem;
    }
    
    .login-content {
        padding: 1.5rem;
    }
    
    .form-input {
        padding: 0.9rem 0.9rem 0.9rem 2.5rem;
    }
    
    .form-icon {
        left: 0.8rem;
        font-size: 1rem;
    }
    
    .login-submit {
        padding: 0.9rem 1.5rem;
    }
    
    .login-footer {
        padding: 1rem 1.5rem 1.5rem;
    }
}

/* تحسينات للثيم الداكن */
[data-theme="dark"] .login-modal {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .form-input {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .form-input:focus {
    background: var(--bg-primary);
}

[data-theme="dark"] .login-footer {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .error-message {
    background: rgba(220, 38, 38, 0.1);
    border-color: rgba(220, 38, 38, 0.3);
    color: #fca5a5;
}
