/* CSS Variables */
:root {
    --primary-color: #4f46e5;
    --secondary-color: #06b6d4;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --border-color: #e5e7eb;
    --shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --transition: all 0.3s ease;
}

/* Dark Theme */
[data-theme="dark"] {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --border-color: #374151;
    --shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Ta<PERSON><PERSON>', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    transition: var(--transition);
}

html {
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* تخصيص container للناف بار */
.nav.container {
    padding: 0 20px;
    max-width: 100%;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .header {
    background: rgba(17, 24, 39, 0.95);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0.5rem;
    margin: 0;
    padding: 0;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    font-size: 1.1rem !important;
    padding: 0.6rem 0.4rem;
    white-space: nowrap;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* مجموعة التحكم (لوحة التحكم + الدارك مود) */
.nav-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-controls .admin-link {
    font-size: 0.95rem;
    padding: 0.5rem 1rem;
    color: var(--primary-color);
    transition: var(--transition);
    position: relative;
}

.nav-controls .admin-link:hover {
    color: var(--primary-color);
}

.nav-controls .admin-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: width 0.3s ease;
}

.nav-controls .admin-link:hover::after {
    width: 100%;
}

/* تحسين الناف بار للشاشات المختلفة */
@media (max-width: 1200px) {
    .nav {
        padding: 1.3rem 0;
    }

    .nav.container {
        padding: 0 15px;
    }

    .nav-menu {
        gap: 0.4rem;
    }

    .nav-link {
        font-size: 1.05rem !important;
        padding: 0.5rem 0.3rem;
    }
}

@media (max-width: 1024px) {
    .nav {
        padding: 1.2rem 0;
    }

    .nav.container {
        padding: 0 12px;
    }

    .nav-menu {
        gap: 0.3rem;
    }

    .nav-link {
        font-size: 1rem !important;
        padding: 0.4rem 0.25rem;
    }
}

@media (max-width: 900px) {
    .nav {
        padding: 1rem 0;
    }

    .nav.container {
        padding: 0 8px;
    }

    .nav-menu {
        gap: 0.2rem;
    }

    .nav-link {
        font-size: 0.95rem !important;
        padding: 0.4rem 0.2rem;
    }

    .logo {
        font-size: 1.6rem;
    }

    .nav-controls {
        gap: 0.5rem;
    }

    .nav-controls .admin-link {
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
    }

    .theme-toggle {
        width: 40px;
        height: 40px;
        padding: 0.3rem;
        font-size: 1rem;
    }
}

.theme-toggle {
    background: none;
    border: 2px solid var(--border-color);
    border-radius: 50px;
    padding: 0.5rem;
    cursor: pointer;
    font-size: 1.2rem;
    color: var(--text-primary);
    transition: var(--transition);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: var(--primary-color);
    color: white;
    transform: rotate(360deg);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
    overflow: hidden;
    padding-top: 80px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%234f46e5" fill-opacity="0.05" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeInUp 1s ease;
}

.hero-text .subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-text .location {
    font-size: 1.1rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease 0.4s both;
}

.contact-info {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease 0.6s both;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
}

.contact-item i {
    color: var(--primary-color);
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    animation: fadeInUp 1s ease 0.8s both;
}

.btn {
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: var(--transition);
    display: inline-block;
}

.btn-primary {
    background: var(--gradient);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
}

.btn-outline {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInUp 1s ease 0.8s both;
}

.profile-card {
    background: var(--bg-primary);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow);
    text-align: center;
    border: 1px solid var(--border-color);
    transform: rotate(-5deg);
    transition: var(--transition);
}

.profile-card:hover {
    transform: rotate(0deg) scale(1.05);
}

.profile-avatar {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: 0;
}

.profile-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    object-position: center top;
    transform: translateY(-10px);
}

.gpa-badge {
    background: var(--gradient);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 700;
    margin-top: 1rem;
    display: inline-block;
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-item {
    position: absolute;
    font-size: 2rem;
    color: var(--primary-color);
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.floating-item:nth-child(2) {
    animation-delay: -2s;
}

.floating-item:nth-child(3) {
    animation-delay: -4s;
}

.floating-item:nth-child(4) {
    animation-delay: -1s;
}

/* Sections */
.section {
    padding: 5rem 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* About Section */
.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.about-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

/* Skills Section */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.skill-category {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.skill-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.skill-category h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.skill-list {
    list-style: none;
}

.skill-list li {
    padding: 0.5rem 0;
    color: var(--text-secondary);
    position: relative;
    padding-right: 1.5rem;
}

.skill-list li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

/* Timeline Styles */
.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 3px;
    height: 100%;
    background: var(--gradient);
    transform: translateX(-50%);
}

.timeline-item {
    margin-bottom: 3rem;
    position: relative;
}

.timeline-content {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: relative;
}

.timeline-item:nth-child(even) .timeline-content {
    justify-content: flex-end;
}

.timeline-marker {
    width: 20px;
    height: 20px;
    background: var(--gradient);
    border-radius: 50%;
    position: absolute;
    right: 50%;
    transform: translateX(50%);
    z-index: 2;
    border: 3px solid var(--bg-primary);
}

.timeline-card {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    max-width: 45%;
    margin-right: 2.5rem;
    transition: var(--transition);
}

.timeline-item:nth-child(even) .timeline-card {
    margin-right: 0;
    margin-left: 2.5rem;
}

.timeline-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.timeline-card h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.timeline-card h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.timeline-card .role {
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.timeline-card ul {
    list-style: none;
    margin-top: 1rem;
}

.timeline-card li {
    padding: 0.25rem 0;
    color: var(--text-secondary);
    position: relative;
    padding-right: 1.5rem;
}

.timeline-card li::before {
    content: '•';
    position: absolute;
    right: 0;
    color: var(--accent-color);
    font-weight: bold;
}

/* Courses Grid */
.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.course-card {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.course-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.course-card h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.course-duration {
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.course-card p:last-child {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Personal Skills */
.personal-skills {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.skill-item {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.skill-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.skill-item i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.skill-item h3 {
    color: var(--text-primary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.skill-item .skill-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-top: 0.5rem;
    text-align: center;
}

/* Languages */
.languages {
    display: flex;
    justify-content: center;
    gap: 3rem;
    max-width: 800px;
    margin: 2rem auto 0;
    flex-wrap: wrap;
}

.language-item {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    flex: 1;
    min-width: 250px;
    max-width: 350px;
    text-align: center;
}

.language-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.language-item h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.language-item p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background: var(--border-color);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress {
    height: 100%;
    background: var(--gradient);
    border-radius: 10px;
    transition: width 1s ease-in-out;
    position: relative;
}

.progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Responsive للغات */
@media (max-width: 768px) {
    .languages {
        flex-direction: column;
        gap: 1.5rem;
        align-items: center;
    }
    
    .language-item {
        min-width: 280px;
        max-width: 100%;
        padding: 1.5rem;
    }
    
    .language-item h3 {
        font-size: 1.3rem;
    }
}

@media (max-width: 480px) {
    .languages {
        gap: 1rem;
    }
    
    .language-item {
        min-width: 250px;
        padding: 1.2rem;
    }
    
    .language-item h3 {
        font-size: 1.2rem;
    }
    
    .progress-bar {
        height: 8px;
    }
}

/* Contact Section */
.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.contact-card {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.contact-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.contact-card h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.contact-card p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Certificates Grid */
.certificates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.certificate-card {
    background: var(--bg-secondary);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
}

.certificate-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* تحسين عرض الصور */
.certificate-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
}

.image-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.certificate-card:hover .image-container img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: white;
}

.certificate-card:hover .image-overlay {
    opacity: 1;
}

.image-overlay i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.image-overlay span {
    font-size: 0.9rem;
    font-weight: 500;
}

/* تحسين عرض PDF مع معاينة */
.pdf-preview-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    background: #f8f9fa;
}

.pdf-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
    pointer-events: none; /* منع التفاعل مع PDF داخل الكارد */
    object-fit: cover;
    border-radius: 8px 8px 0 0;
}

.pdf-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: white;
    cursor: pointer;
    z-index: 2;
}

.certificate-card:hover .pdf-overlay {
    opacity: 1;
}

.pdf-overlay i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.pdf-overlay span {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
}

/* PDF Fallback في حالة عدم دعم المتصفح */
.pdf-fallback {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
}

.pdf-fallback i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.pdf-fallback span {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.pdf-fallback p {
    font-size: 0.9rem;
    opacity: 0.8;
    margin: 0;
}

/* تحسين عرض PDF على الشاشات الصغيرة */
@media (max-width: 768px) {
    .pdf-iframe {
        transform: scale(0.8);
        transform-origin: top left;
        width: 125%;
        height: 125%;
    }
}

.certificate-info {
    padding: 1.5rem;
    text-align: center;
}

.certificate-info h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.certificate-issuer {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.certificate-type {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    display: inline-block;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.certificate-info p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.certificate-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-color);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
}

.certificate-link:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

/* Responsive للشهادات */
@media (max-width: 768px) {
    .certificates-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .certificate-image {
        height: 200px;
    }
    
    .certificate-info {
        padding: 1rem;
    }
    
    .certificate-link {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .certificates-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1025px) {
    .certificates-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Portfolio Section */
.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.portfolio-item {
    background: var(--bg-secondary);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.portfolio-media {
    width: 100%;
    height: 250px;
    overflow: hidden;
    position: relative;
}

.portfolio-media img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.portfolio-item:hover .portfolio-media img {
    transform: scale(1.05);
}

.portfolio-info {
    padding: 1.5rem;
    text-align: center;
}

.portfolio-info h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.portfolio-info p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.portfolio-info small {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Website Card */
.website-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 2rem;
}

.website-card i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.website-card h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.visit-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    margin-top: 1rem;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.visit-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* YouTube Card */
.youtube-card {
    position: relative;
    width: 100%;
    height: 100%;
    cursor: pointer;
    border-radius: 0;
    overflow: hidden;
}

.youtube-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.youtube-play {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 0, 0, 0.9);
    color: white;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(255, 0, 0, 0.3);
}

.youtube-card:hover .youtube-play {
    background: rgba(255, 0, 0, 1);
    transform: translate(-50%, -50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 0, 0, 0.5);
}

.youtube-card:hover img {
    transform: scale(1.05);
}

/* YouTube Modal */
.youtube-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(5px);
}

.youtube-modal-content {
    position: relative;
    margin: 5% auto;
    width: 90%;
    max-width: 900px;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.youtube-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 10001;
    background: rgba(255, 255, 255, 0.1);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.youtube-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.youtube-iframe {
    width: 100%;
    height: 500px;
    border: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .portfolio-media {
        height: 200px;
    }
    
    .portfolio-info {
        padding: 1rem;
    }
    
    .youtube-modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .youtube-iframe {
        height: 250px;
    }
    
    .portfolio-filters {
        gap: 0.5rem;
    }
    
    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .portfolio-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .youtube-iframe {
        height: 400px;
    }
}

@media (min-width: 1025px) {
    .portfolio-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Scroll Reveal Effect */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Admin Login Modal */
.admin-modal {
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.admin-modal-content {
    background-color: var(--bg-primary);
    margin: 10% auto;
    padding: 2rem;
    border-radius: 15px;
    width: 90%;
    max-width: 400px;
    box-shadow: var(--shadow);
    position: relative;
    border: 1px solid var(--border-color);
}

.close-modal {
    color: var(--text-secondary);
    float: left;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 15px;
    left: 20px;
}

.close-modal:hover {
    color: var(--primary-color);
}

.admin-modal-content h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.admin-modal-content .form-group {
    margin-bottom: 1.5rem;
}

.admin-modal-content label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.admin-modal-content input {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    font-family: 'Tajawal', sans-serif;
    transition: var(--transition);
}

.admin-modal-content input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.login-btn {
    width: 100%;
    background: var(--gradient);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    font-size: 1rem;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
}

.admin-link {
    color: var(--accent-color) !important;
    font-weight: 600;
}

/* تصميم المواقع */
.website-card {
    text-align: center;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.website-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.website-card h4 {
    margin: 0.5rem 0;
    color: var(--text-primary);
}

.visit-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gradient);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    margin-top: 1rem;
    transition: var(--transition);
}

.visit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}

/* تصميم يوتيوب */
.youtube-card {
    position: relative;
    width: 100%;
    height: 100%;
    cursor: pointer;
    border-radius: 10px;
    overflow: hidden;
}

.youtube-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.youtube-play {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 0, 0, 0.9);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: var(--transition);
}

.youtube-card:hover img {
    transform: scale(1.05);
}

.youtube-card:hover .youtube-play {
    background: rgba(255, 0, 0, 1);
    transform: translate(-50%, -50%) scale(1.1);
}

/* تحسينات إضافية للتصميم */
.portfolio-item {
    transition: all 0.3s ease;
}

.portfolio-item.hidden {
    opacity: 0;
    transform: translateY(20px);
}

.portfolio-item.visible {
    opacity: 1;
    transform: translateY(0);
}

/* تحسين النافذة المنبثقة على الهواتف */
@media (max-width: 480px) {
    .youtube-modal-content {
        margin: 20% auto;
        width: 98%;
    }
    
    .youtube-iframe {
        height: 200px;
    }
    
    .youtube-close {
        top: -35px;
        font-size: 1.5rem;
        width: 35px;
        height: 35px;
    }
}

/* تحسين أزرار الفلترة */
.portfolio-filters {
    position: sticky;
    top: 80px;
    background: var(--bg-primary);
    padding: 1rem 0;
    z-index: 100;
    border-radius: 15px;
    margin-bottom: 1rem;
}

/* تأثيرات التحميل */
.portfolio-item {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين الشهادات */
.certificate-card {
    animation: fadeInUp 0.6s ease forwards;
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: 3rem 0 2rem;
    margin-top: 5rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.footer-text {
    flex: 1;
}

.footer-text p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 1rem;
    line-height: 1.6;
}

.footer-social {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: var(--bg-primary);
    color: var(--text-secondary);
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
    border: 2px solid var(--border-color);
    font-size: 1.2rem;
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

.social-link i {
    transition: var(--transition);
}

.social-link:hover i {
    transform: scale(1.1);
}

/* تحسينات للوضع المظلم */
[data-theme="dark"] .footer {
    background: var(--bg-secondary);
    border-top-color: var(--border-color);
}

[data-theme="dark"] .social-link {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .social-link:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Responsive للفوتر */
@media (max-width: 768px) {
    .footer {
        padding: 2rem 0 1.5rem;
        margin-top: 3rem;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }
    
    .footer-text {
        order: 2;
    }
    
    .footer-social {
        order: 1;
        justify-content: center;
    }
    
    .social-link {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .footer {
        padding: 1.5rem 0 1rem;
    }
    
    .footer-content {
        gap: 1rem;
    }
    
    .footer-text p {
        font-size: 0.9rem;
    }
    
    .social-link {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
    
    .footer-social {
        gap: 0.8rem;
    }
}

/* تحسين المسافات العامة */
.footer-content .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* أنماط عرض البورتفوليو الجديدة */
.portfolio-website {
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.website-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.website-info h3 {
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.website-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    text-decoration: none;
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 25px;
    transition: var(--transition);
    background: rgba(255,255,255,0.1);
}

.website-link:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.portfolio-image {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
}

.portfolio-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    cursor: pointer;
    transition: var(--transition);
}

.portfolio-image:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem 1rem 1rem;
    transform: translateY(100%);
    transition: var(--transition);
}

.portfolio-image:hover .image-overlay {
    transform: translateY(0);
}

/* نافذة عرض الصورة */
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 3000;
}

.image-modal .modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    text-align: center;
}

.image-modal img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 10px;
}

.image-modal h3 {
    color: white;
    margin-top: 1rem;
}

.close-modal {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-items {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 2rem;
    grid-column: 1 / -1;
}

/* أنماط ألبوم الصور */
.image-album-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    height: 250px;
    border-radius: 15px;
    overflow: hidden;
}

.album-thumb {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    transition: var(--transition);
}

.album-thumb:first-child {
    grid-row: span 2;
}

.album-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.album-thumb:hover img {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
    color: white;
    font-size: 1.5rem;
}

.album-thumb:hover .image-overlay {
    opacity: 1;
}

.more-images {
    position: absolute;
    bottom: 0.5rem;
    right: 0.5rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-weight: 600;
}

/* نافذة الصورة المنبثقة */
.image-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(5px);
}

.image-modal-content {
    position: relative;
    margin: 5% auto;
    width: 90%;
    max-width: 800px;
    text-align: center;
}

.image-modal-content img {
    max-width: 100%;
    max-height: 70vh;
    border-radius: 10px;
}

.close-modal {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-modal h3 {
    color: white;
    margin-top: 1rem;
}

/* تحسين كاردات الفيديو */
.youtube-card {
    position: relative;
    cursor: pointer;
    border-radius: 15px;
    overflow: hidden;
    transition: var(--transition);
}

.youtube-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.youtube-play {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 0, 0, 0.9);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: var(--transition);
}

.youtube-card:hover .youtube-play {
    transform: translate(-50%, -50%) scale(1.1);
}

.video-duration {
    position: absolute;
    bottom: 0.5rem;
    right: 0.5rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    font-size: 0.8rem;
}

/* تحسين كاردات المواقع */
.website-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    transition: var(--transition);
}

.website-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.website-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.website-info h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.website-info p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.visit-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.visit-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* أنماط زر "عرض المزيد" */
.show-more-btn {
    grid-column: 1 / -1;
    margin: 2rem 0;
}

.show-more-content {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border: 2px dashed var(--border-color);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
}

.show-more-content:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.show-more-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.show-more-content h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.show-more-content p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.show-more-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.show-more-button:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
}

/* تحسين تخطيط البورتفوليو */
#portfolio-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.portfolio-item {
    background: var(--bg-secondary);
    border-radius: 15px;
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.portfolio-media {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.portfolio-info {
    padding: 1.5rem;
}

.portfolio-info h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.portfolio-info p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* تحسين ألبوم الصور */
.image-album {
    grid-column: 1 / -1;
    max-width: 600px;
    margin: 0 auto;
}

/* تجاوب الشاشات الصغيرة */
@media (max-width: 768px) {
    #portfolio-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .image-album-grid {
        height: 200px;
    }

    .show-more-content {
        padding: 1.5rem;
    }

    .show-more-content h3 {
        font-size: 1.1rem;
    }
}
