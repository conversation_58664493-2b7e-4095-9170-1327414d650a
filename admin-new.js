// المتغيرات العامة
let currentSection = 'portfolio';
let editingId = null;
let websites = [];
let images = [];
let skills = [];
let experiences = [];
let courses = [];
let certificates = [];

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin panel loading...');
    
    // التحقق من تسجيل الدخول
    if (!checkAuth()) return;
    
    // تحديد القسم الحالي من الصفحة
    const currentPage = window.location.pathname.split('/').pop();
    console.log('Current page:', currentPage);

    if (currentPage.includes('portfolio')) currentSection = 'portfolio';
    else if (currentPage.includes('skills')) currentSection = 'skills';
    else if (currentPage.includes('experience')) currentSection = 'experience';
    else if (currentPage.includes('courses')) currentSection = 'courses';
    else if (currentPage.includes('certificates')) currentSection = 'certificates';

    console.log('Detected section:', currentSection);
    
    // تحميل البيانات والتهيئة
    loadAllData();
    setupEventListeners();
    
    console.log('Admin panel initialized for section:', currentSection);
});

// التحقق من المصادقة
function checkAuth() {
    const isLoggedIn = localStorage.getItem('adminLoggedIn');
    if (!isLoggedIn || isLoggedIn !== 'true') {
        alert('يجب تسجيل الدخول أولاً');
        window.location.href = 'index.html';
        return false;
    }
    return true;
}

// تحميل جميع البيانات من API
async function loadAllData() {
    try {
        const sections = ['websites', 'images', 'skills', 'experiences', 'courses', 'certificates'];
        
        for (const section of sections) {
            console.log(`Loading section: ${section}`);
            const response = await fetch(`api.php?section=${section}`);
            const result = await response.json();

            console.log(`Result for ${section}:`, result);

            if (result.success) {
                switch(section) {
                    case 'websites':
                        websites = result.data;
                        console.log('Websites loaded:', websites.length);
                        break;
                    case 'images':
                        images = result.data;
                        console.log('Images loaded:', images.length);
                        break;
                    case 'skills':
                        skills = result.data;
                        console.log('Skills loaded:', skills.length);
                        break;
                    case 'experiences':
                        experiences = result.data;
                        console.log('Experiences loaded:', experiences.length);
                        break;
                    case 'courses':
                        courses = result.data;
                        console.log('Courses loaded:', courses.length);
                        break;
                    case 'certificates':
                        certificates = result.data;
                        console.log('Certificates loaded:', certificates.length);
                        break;
                }
            } else {
                console.error(`Failed to load ${section}:`, result.message);
            }
        }
        
        // تحميل المحتوى المناسب
        loadSectionData(currentSection);
        updateStats();
        
        console.log('Data loaded successfully');
        
    } catch (error) {
        console.error('Error loading data:', error);
        showMessage('خطأ في تحميل البيانات', 'error');
    }
}

// إعداد معالجات الأحداث
function setupEventListeners() {
    // معالج النموذج
    const form = document.getElementById('addForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
    
    // معالج تغيير نوع المحتوى
    const contentType = document.getElementById('contentType');
    if (contentType) {
        contentType.addEventListener('change', toggleContentForm);
    }
    
    // معالج رفع الصور
    const imageFile = document.getElementById('imageFile');
    if (imageFile) {
        imageFile.addEventListener('change', previewImage);
    }

    // معالج رفع ملفات الشهادات
    const certFile = document.getElementById('certFile');
    if (certFile) {
        certFile.addEventListener('change', previewCertificateFile);
    }
}

// تحميل بيانات القسم
function loadSectionData(section) {
    switch(section) {
        case 'portfolio':
            loadPortfolioItems();
            break;
        case 'skills':
            loadSkills();
            break;
        case 'experience':
            loadExperiences();
            break;
        case 'courses':
            loadCourses();
            break;
        case 'certificates':
            loadCertificates();
            break;
    }
}

// تحميل عناصر البورتفوليو
function loadPortfolioItems() {
    const grid = document.getElementById('portfolioGrid');
    if (!grid) return;
    
    grid.innerHTML = '';
    
    // دمج المواقع والصور
    const allItems = [
        ...websites.map(item => ({...item, type: 'website'})),
        ...images.map(item => ({...item, type: 'image'}))
    ];
    
    // ترتيب حسب الترتيب أو التاريخ
    allItems.sort((a, b) => {
        if (a.order && b.order) return a.order - b.order;
        return new Date(b.date) - new Date(a.date);
    });
    
    if (allItems.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-folder-open"></i><p>لا توجد أعمال بعد</p></div>';
        return;
    }
    
    allItems.forEach(item => {
        const card = createPortfolioCard(item);
        grid.appendChild(card);
    });
}

// إنشاء كارد البورتفوليو
function createPortfolioCard(item) {
    const div = document.createElement('div');
    div.className = 'admin-card';
    
    let mediaContent = '';
    let actionButtons = '';
    
    if (item.type === 'website') {
        mediaContent = `
            <div class="card-preview website-preview">
                <i class="fas fa-globe"></i>
                <h4>${item.name}</h4>
                <p class="website-url">${item.url}</p>
            </div>
        `;
        actionButtons = `
            <button class="action-btn edit-btn" onclick="editWebsite(${item.id})" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" onclick="deleteWebsite(${item.id})" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
            <button class="action-btn view-btn" onclick="window.open('${item.url}', '_blank')" title="عرض">
                <i class="fas fa-external-link-alt"></i>
            </button>
        `;
    } else if (item.type === 'image') {
        mediaContent = `<img src="image/${item.filename}" alt="${item.title}" class="card-image">`;
        actionButtons = `
            <button class="action-btn edit-btn" onclick="editImage(${item.id})" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" onclick="deleteImage(${item.id})" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
            <button class="action-btn view-btn" onclick="viewImageFullscreen('image/${item.filename}')" title="عرض">
                <i class="fas fa-eye"></i>
            </button>
        `;
    }
    
    div.innerHTML = `
        <div class="card-actions">
            ${actionButtons}
        </div>
        <div class="card-media">
            ${mediaContent}
        </div>
        <div class="card-content">
            <h3>${item.title || item.name}</h3>
            <p>${item.description || 'لا يوجد وصف'}</p>
            <span class="card-type">${item.type === 'website' ? 'موقع إلكتروني' : 'صورة'}</span>
            <span class="card-date">${item.date}</span>
        </div>
    `;
    
    return div;
}

// معاينة الصورة قبل الرفع
function previewImage(event) {
    const file = event.target.files[0];
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
}

// معاينة ملف الشهادة (صورة أو PDF)
function previewCertificateFile(event) {
    const file = event.target.files[0];
    const preview = document.getElementById('certFilePreview');
    const previewContent = document.getElementById('certPreviewContent');

    if (file && preview && previewContent) {
        const fileType = file.type;
        const fileName = file.name;

        if (fileType.startsWith('image/')) {
            // معاينة الصورة
            const reader = new FileReader();
            reader.onload = function(e) {
                previewContent.innerHTML = `
                    <p>معاينة الصورة:</p>
                    <img src="${e.target.result}" style="max-width: 200px; max-height: 200px; border-radius: 5px;">
                `;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else if (fileType === 'application/pdf') {
            // معاينة PDF
            previewContent.innerHTML = `
                <p><i class="fas fa-file-pdf"></i> ملف PDF محدد: ${fileName}</p>
                <p>سيتم رفع الملف عند الحفظ</p>
            `;
            preview.style.display = 'block';
        } else {
            // نوع ملف غير مدعوم
            previewContent.innerHTML = `
                <p style="color: red;"><i class="fas fa-exclamation-triangle"></i> نوع الملف غير مدعوم</p>
                <p>يرجى اختيار صورة أو ملف PDF</p>
            `;
            preview.style.display = 'block';
        }
    } else {
        if (preview) preview.style.display = 'none';
    }
}

// عرض الصورة بملء الشاشة
function viewImageFullscreen(imageSrc) {
    const modal = document.createElement('div');
    modal.className = 'fullscreen-modal';
    modal.innerHTML = `
        <div class="fullscreen-content">
            <span class="close-fullscreen" onclick="this.parentElement.parentElement.remove()">&times;</span>
            <img src="${imageSrc}" alt="صورة بملء الشاشة">
        </div>
    `;
    document.body.appendChild(modal);
}

// تحديث الإحصائيات
function updateStats() {
    // إحصائيات البورتفوليو
    const totalWebsitesEl = document.getElementById('totalWebsites');
    const totalImagesEl = document.getElementById('totalImages');
    const totalVideosEl = document.getElementById('totalVideos');

    if (totalWebsitesEl) totalWebsitesEl.textContent = websites.length;
    if (totalImagesEl) totalImagesEl.textContent = images.length;
    if (totalVideosEl) totalVideosEl.textContent = 0; // مؤقتاً

    // إحصائيات المهارات
    updateSkillsStats();

    // إحصائيات الخبرات
    updateExperienceStats();

    // إحصائيات الدورات
    updateCoursesStats();

    // إحصائيات الشهادات
    updateCertificatesStats();
}

// عرض نافذة الإضافة
function showAddModal(preserveEditingId = false) {
    if (!preserveEditingId) {
        editingId = null;
    }

    const modalTitle = document.getElementById('modalTitle');
    if (modalTitle) {
        modalTitle.textContent = getModalTitle();
    }

    // إخفاء جميع النماذج
    document.querySelectorAll('.form-section').forEach(f => {
        f.style.display = 'none';
    });

    // عرض النموذج المناسب حسب القسم الحالي
    showCurrentForm();

    // مسح النموذج
    const form = document.getElementById('addForm');
    if (form) {
        form.reset();
    }

    // إخفاء معاينة الصورة (للبورتفوليو فقط)
    const preview = document.getElementById('imagePreview');
    if (preview) {
        preview.style.display = 'none';
    }

    // عرض نموذج المواقع افتراضياً (للبورتفوليو فقط)
    if (currentSection === 'portfolio') {
        toggleContentForm();
    }

    // عرض النافذة
    const modal = document.getElementById('addModal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

// عرض النموذج الحالي
function showCurrentForm() {
    const formMap = {
        portfolio: 'portfolioForm',
        certificates: 'certificatesForm',
        skills: 'skillsForm',
        experience: 'experienceForm',
        courses: 'coursesForm'
    };

    const formId = formMap[currentSection];
    console.log('Current section:', currentSection, 'Form ID:', formId);

    if (formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.style.display = 'block';
            console.log('Form displayed:', formId);

            // التأكد من أن الحقول مرئية
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                if (input.style.display === 'none') {
                    input.style.display = '';
                }
            });

            // تحميل القوائم المنسدلة
            loadDropdownOptions();
        } else {
            console.error('Form not found:', formId);
        }
    }
}

// تحميل خيارات القوائم المنسدلة
function loadDropdownOptions() {
    if (currentSection === 'skills') {
        loadSkillCategories();
    } else if (currentSection === 'experience') {
        loadJobTitles();
    }
}

// تحميل فئات المهارات
function loadSkillCategories() {
    const categorySelect = document.getElementById('skillCategory');
    if (!categorySelect) return;

    // استخراج الفئات الموجودة من البيانات
    const categories = [...new Set(skills.map(skill => skill.category))];

    // مسح الخيارات الموجودة (عدا الخيار الافتراضي)
    categorySelect.innerHTML = '<option value="">اختر الفئة</option>';

    // إضافة الفئات الموجودة
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categorySelect.appendChild(option);
    });

    // إضافة خيار "أخرى" للفئات الجديدة
    const otherOption = document.createElement('option');
    otherOption.value = 'other';
    otherOption.textContent = 'أخرى (اكتب فئة جديدة)';
    categorySelect.appendChild(otherOption);

    // إضافة معالج للخيار "أخرى"
    categorySelect.addEventListener('change', function() {
        if (this.value === 'other') {
            const newCategory = prompt('اكتب اسم الفئة الجديدة:');
            if (newCategory && newCategory.trim()) {
                this.value = newCategory.trim();
                // إضافة الخيار الجديد للقائمة
                const newOption = document.createElement('option');
                newOption.value = newCategory.trim();
                newOption.textContent = newCategory.trim();
                this.insertBefore(newOption, otherOption);
                this.value = newCategory.trim();
            } else {
                this.value = '';
            }
        }
    });
}

// تحميل المسميات الوظيفية
function loadJobTitles() {
    const titleSelect = document.getElementById('expTitle');
    if (!titleSelect) return;

    // استخراج المسميات الموجودة من البيانات
    const titles = [...new Set(experiences.map(exp => exp.title))];

    // مسح الخيارات الموجودة (عدا الخيار الافتراضي)
    titleSelect.innerHTML = '<option value="">اختر المسمى الوظيفي</option>';

    // إضافة المسميات الموجودة
    titles.forEach(title => {
        const option = document.createElement('option');
        option.value = title;
        option.textContent = title;
        titleSelect.appendChild(option);
    });

    // إضافة خيار "أخرى" للمسميات الجديدة
    const otherOption = document.createElement('option');
    otherOption.value = 'other';
    otherOption.textContent = 'أخرى (اكتب مسمى جديد)';
    titleSelect.appendChild(otherOption);

    // إضافة معالج للخيار "أخرى"
    titleSelect.addEventListener('change', function() {
        if (this.value === 'other') {
            const newTitle = prompt('اكتب المسمى الوظيفي الجديد:');
            if (newTitle && newTitle.trim()) {
                this.value = newTitle.trim();
                // إضافة الخيار الجديد للقائمة
                const newOption = document.createElement('option');
                newOption.value = newTitle.trim();
                newOption.textContent = newTitle.trim();
                this.insertBefore(newOption, otherOption);
                this.value = newTitle.trim();
            } else {
                this.value = '';
            }
        }
    });
}

// الحصول على عنوان النافذة
function getModalTitle() {
    const titles = {
        portfolio: editingId ? 'تعديل عمل' : 'إضافة عمل جديد',
        skills: editingId ? 'تعديل مهارة' : 'إضافة مهارة جديدة',
        experience: editingId ? 'تعديل خبرة' : 'إضافة خبرة جديدة',
        courses: editingId ? 'تعديل دورة' : 'إضافة دورة جديدة',
        certificates: editingId ? 'تعديل شهادة' : 'إضافة شهادة جديدة'
    };

    return titles[currentSection] || 'إضافة محتوى جديد';
}

// إغلاق النافذة
function closeModal() {
    const modal = document.getElementById('addModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
    editingId = null;
}

// تغيير نوع المحتوى
function toggleContentForm() {
    const contentType = document.getElementById('contentType');
    if (!contentType) return;

    const selectedType = contentType.value;

    // إخفاء جميع النماذج الفرعية
    document.querySelectorAll('.content-fields').forEach(field => {
        field.style.display = 'none';
    });

    // عرض النموذج المناسب
    const targetForm = document.getElementById(selectedType + 'Fields');
    if (targetForm) {
        targetForm.style.display = 'block';
    }
}

// معالج إرسال النموذج
async function handleFormSubmit(e) {
    e.preventDefault();

    try {
        switch(currentSection) {
            case 'portfolio':
                const contentType = document.getElementById('contentType').value;
                if (contentType === 'website') {
                    await handleWebsiteSubmit();
                } else if (contentType === 'image') {
                    await handleImageSubmit();
                }
                break;
            case 'skills':
                await handleSkillSubmit();
                break;
            case 'experience':
                await handleExperienceSubmit();
                break;
            case 'courses':
                await handleCourseSubmit();
                break;
            case 'certificates':
                await handleCertificateSubmit();
                break;
            default:
                throw new Error('قسم غير معروف');
        }

    } catch (error) {
        console.error('Error submitting form:', error);
        showMessage('حدث خطأ أثناء الحفظ: ' + error.message, 'error');
    }
}

// معالج إضافة المواقع
async function handleWebsiteSubmit() {
    const name = document.getElementById('websiteName').value.trim();
    const url = document.getElementById('websiteUrl').value.trim();

    if (!name || !url) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }

    const data = {
        name: name,
        url: url,
        description: `موقع إلكتروني: ${name}`
    };

    if (editingId) {
        data.id = editingId;
        await updateData('websites', data);
        showMessage('تم تحديث الموقع بنجاح!', 'success');
    } else {
        await addData('websites', data);
        showMessage('تم إضافة الموقع بنجاح!', 'success');
    }

    closeModal();
    await loadAllData();
}

// معالج إضافة الصور
async function handleImageSubmit() {
    const title = document.getElementById('imageTitle').value.trim();
    const description = document.getElementById('imageDescription').value.trim();
    const fileInput = document.getElementById('imageFile');

    if (!title) {
        throw new Error('يرجى إدخال عنوان للصورة');
    }

    if (!editingId && !fileInput.files[0]) {
        throw new Error('يرجى اختيار صورة');
    }

    if (fileInput.files[0]) {
        // رفع الصورة
        const formData = new FormData();
        formData.append('image', fileInput.files[0]);
        formData.append('title', title);
        formData.append('description', description);

        const response = await fetch('upload.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showMessage('تم رفع الصورة بنجاح!', 'success');
        } else {
            throw new Error(result.message);
        }
    } else if (editingId) {
        // تحديث بيانات الصورة فقط
        const data = {
            id: editingId,
            title: title,
            description: description
        };

        await updateData('images', data);
        showMessage('تم تحديث بيانات الصورة بنجاح!', 'success');
    }

    closeModal();
    await loadAllData();
}

// دالة إضافة البيانات
async function addData(section, data) {
    const response = await fetch(`api.php?section=${section}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    });

    const result = await response.json();

    if (!result.success) {
        throw new Error(result.message);
    }

    return result;
}

// دالة تحديث البيانات
async function updateData(section, data) {
    const response = await fetch(`api.php?section=${section}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    });

    const result = await response.json();

    if (!result.success) {
        throw new Error(result.message);
    }

    return result;
}

// دالة حذف البيانات
async function deleteData(section, id) {
    const response = await fetch(`api.php?section=${section}&id=${id}`, {
        method: 'DELETE'
    });

    const result = await response.json();

    if (!result.success) {
        throw new Error(result.message);
    }

    return result;
}

// دوال التعديل والحذف للمواقع
async function editWebsite(id) {
    const website = websites.find(item => item.id === id);
    if (!website) return;

    editingId = id;
    document.getElementById('contentType').value = 'website';
    toggleContentForm();

    document.getElementById('websiteName').value = website.name;
    document.getElementById('websiteUrl').value = website.url;

    const modalTitle = document.getElementById('modalTitle');
    if (modalTitle) {
        modalTitle.textContent = 'تعديل الموقع';
    }

    showAddModal();
}

async function deleteWebsite(id) {
    if (confirm('هل أنت متأكد من حذف هذا الموقع؟')) {
        try {
            await deleteData('websites', id);
            showMessage('تم حذف الموقع بنجاح!', 'success');
            await loadAllData();
        } catch (error) {
            showMessage('خطأ في حذف الموقع: ' + error.message, 'error');
        }
    }
}

// دوال التعديل والحذف للصور
async function editImage(id) {
    const image = images.find(item => item.id === id);
    if (!image) return;

    editingId = id;
    document.getElementById('contentType').value = 'image';
    toggleContentForm();

    document.getElementById('imageTitle').value = image.title;
    document.getElementById('imageDescription').value = image.description || '';

    // عرض الصورة الحالية
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    if (preview && previewImg) {
        previewImg.src = `image/${image.filename}`;
        preview.style.display = 'block';
    }

    const modalTitle = document.getElementById('modalTitle');
    if (modalTitle) {
        modalTitle.textContent = 'تعديل الصورة';
    }

    showAddModal();
}

async function deleteImage(id) {
    if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
        try {
            await deleteData('images', id);
            showMessage('تم حذف الصورة بنجاح!', 'success');
            await loadAllData();
        } catch (error) {
            showMessage('خطأ في حذف الصورة: ' + error.message, 'error');
        }
    }
}

// عرض رسالة
function showMessage(message, type = 'success') {
    // إزالة الرسائل السابقة
    const existingMessages = document.querySelectorAll('.message');
    existingMessages.forEach(msg => msg.remove());

    // إنشاء رسالة جديدة
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;

    // إضافة الرسالة للصفحة
    document.body.appendChild(messageDiv);

    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

// تسجيل الخروج
function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        localStorage.removeItem('adminLoggedIn');
        window.location.href = 'index.html';
    }
}

// تحميل المهارات
function loadSkills() {
    const grid = document.getElementById('skillsGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (skills.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-code"></i><p>لا توجد مهارات بعد</p></div>';
        return;
    }

    skills.forEach(skill => {
        const card = createSkillCard(skill);
        grid.appendChild(card);
    });
}

// إنشاء كارد المهارة
function createSkillCard(skill) {
    const div = document.createElement('div');
    div.className = 'admin-card';

    const skillsList = Array.isArray(skill.items) ? skill.items.join(', ') : skill.items;

    div.innerHTML = `
        <div class="card-actions">
            <button class="action-btn edit-btn" onclick="editSkill(${skill.id})" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" onclick="deleteSkill(${skill.id})" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="card-content">
            <div class="skill-header">
                <i class="${skill.icon || 'fas fa-code'}"></i>
                <h3>${skill.category}</h3>
            </div>
            <p class="skill-items">${skillsList}</p>
            <p class="skill-description">${skill.description || ''}</p>
            <span class="skill-level level-${skill.level || 'intermediate'}">${getLevelLabel(skill.level)}</span>
        </div>
    `;

    return div;
}

// معالج إضافة المهارات
async function handleSkillSubmit() {
    const category = document.getElementById('skillCategory').value.trim();
    const icon = document.getElementById('skillIcon').value.trim();
    const items = document.getElementById('skillItems').value.trim();
    const level = document.getElementById('skillLevel').value;
    const description = document.getElementById('skillDescription').value.trim();

    if (!category || !icon || !items) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }

    const skillItems = items.split(',').map(item => item.trim()).filter(item => item);

    const data = {
        category: category,
        icon: icon,
        items: skillItems,
        level: level,
        description: description
    };

    console.log('editingId:', editingId);
    console.log('data:', data);

    if (editingId) {
        data.id = editingId;
        console.log('Updating skill with data:', data);
        await updateData('skills', data);
        showMessage('تم تحديث المهارة بنجاح!', 'success');
    } else {
        console.log('Adding new skill with data:', data);
        await addData('skills', data);
        showMessage('تم إضافة المهارة بنجاح!', 'success');
    }

    closeModal();
    await loadAllData();
}

// تعديل مهارة
async function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;

    editingId = id;

    // عرض النافذة أولاً (مع الحفاظ على editingId)
    showAddModal(true);

    // ثم ملء البيانات بعد عرض النافذة
    setTimeout(() => {
        const categoryEl = document.getElementById('skillCategory');
        const iconEl = document.getElementById('skillIcon');
        const itemsEl = document.getElementById('skillItems');
        const levelEl = document.getElementById('skillLevel');
        const descEl = document.getElementById('skillDescription');

        if (categoryEl) categoryEl.value = skill.category;
        if (iconEl) iconEl.value = skill.icon;
        if (itemsEl) itemsEl.value = Array.isArray(skill.items) ? skill.items.join(', ') : skill.items;
        if (levelEl) levelEl.value = skill.level;
        if (descEl) descEl.value = skill.description || '';

        console.log('Skill data loaded:', skill);
    }, 100);
}

// حذف مهارة
async function deleteSkill(id) {
    if (confirm('هل أنت متأكد من حذف هذه المهارة؟')) {
        try {
            await deleteData('skills', id);
            showMessage('تم حذف المهارة بنجاح!', 'success');
            await loadAllData();
        } catch (error) {
            showMessage('خطأ في حذف المهارة: ' + error.message, 'error');
        }
    }
}

// تحديث إحصائيات المهارات
function updateSkillsStats() {
    const totalSkillsEl = document.getElementById('totalSkills');
    const totalCategoriesEl = document.getElementById('totalCategories');
    const expertSkillsEl = document.getElementById('expertSkills');

    if (totalSkillsEl) {
        const totalSkillsCount = skills.reduce((total, skill) => total + (skill.items ? skill.items.length : 0), 0);
        totalSkillsEl.textContent = totalSkillsCount;
    }

    if (totalCategoriesEl) {
        const categories = [...new Set(skills.map(skill => skill.category))];
        totalCategoriesEl.textContent = categories.length;
    }

    if (expertSkillsEl) {
        const expertCount = skills.filter(skill => skill.level === 'expert' || skill.level === 'advanced').length;
        expertSkillsEl.textContent = expertCount;
    }
}

// تحميل الخبرات
function loadExperiences() {
    const grid = document.getElementById('experienceGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (experiences.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-briefcase"></i><p>لا توجد خبرات بعد</p></div>';
        return;
    }

    experiences.forEach(experience => {
        const card = createExperienceCard(experience);
        grid.appendChild(card);
    });
}

// إنشاء كارد الخبرة
function createExperienceCard(experience) {
    const div = document.createElement('div');
    div.className = 'admin-card';

    const tasksList = Array.isArray(experience.tasks) ? experience.tasks.join(', ') : experience.tasks;

    div.innerHTML = `
        <div class="card-actions">
            <button class="action-btn edit-btn" onclick="editExperience(${experience.id})" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" onclick="deleteExperience(${experience.id})" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="card-content">
            <h3>${experience.title}</h3>
            <h4>${experience.company}</h4>
            <p class="experience-period">${experience.period}</p>
            <p class="experience-role">${experience.role}</p>
            <p class="experience-tasks">${tasksList}</p>
            <span class="experience-type">${getExperienceTypeLabel(experience.type)}</span>
        </div>
    `;

    return div;
}

// معالج إضافة الخبرات
async function handleExperienceSubmit() {
    const title = document.getElementById('expTitle').value.trim();
    const company = document.getElementById('expCompany').value.trim();
    const type = document.getElementById('expType').value;
    const role = document.getElementById('expRole').value.trim();
    const period = document.getElementById('expPeriod').value.trim();
    const tasks = document.getElementById('expTasks').value.trim();
    const technologies = document.getElementById('expTechnologies').value.trim();
    const location = document.getElementById('expLocation').value.trim();
    const companyUrl = document.getElementById('expCompanyUrl').value.trim();

    if (!title || !company || !role || !period || !tasks) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }

    const tasksList = tasks.split(',').map(task => task.trim()).filter(task => task);

    const data = {
        title: title,
        company: company,
        type: type,
        role: role,
        period: period,
        tasks: tasksList,
        technologies: technologies,
        location: location,
        companyUrl: companyUrl
    };

    console.log('editingId:', editingId);
    console.log('experience data:', data);

    if (editingId) {
        data.id = editingId;
        console.log('Updating experience with data:', data);
        await updateData('experiences', data);
        showMessage('تم تحديث الخبرة بنجاح!', 'success');
    } else {
        console.log('Adding new experience with data:', data);
        await addData('experiences', data);
        showMessage('تم إضافة الخبرة بنجاح!', 'success');
    }

    closeModal();
    await loadAllData();
}

// تعديل خبرة
async function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;

    editingId = id;

    // عرض النافذة أولاً (مع الحفاظ على editingId)
    showAddModal(true);

    // ثم ملء البيانات بعد عرض النافذة
    setTimeout(() => {
        const titleEl = document.getElementById('expTitle');
        const companyEl = document.getElementById('expCompany');
        const typeEl = document.getElementById('expType');
        const roleEl = document.getElementById('expRole');
        const periodEl = document.getElementById('expPeriod');
        const tasksEl = document.getElementById('expTasks');
        const techEl = document.getElementById('expTechnologies');
        const locationEl = document.getElementById('expLocation');
        const urlEl = document.getElementById('expCompanyUrl');

        if (titleEl) titleEl.value = exp.title;
        if (companyEl) companyEl.value = exp.company;
        if (typeEl) typeEl.value = exp.type;
        if (roleEl) roleEl.value = exp.role;
        if (periodEl) periodEl.value = exp.period;
        if (tasksEl) tasksEl.value = Array.isArray(exp.tasks) ? exp.tasks.join(', ') : exp.tasks;
        if (techEl) techEl.value = exp.technologies || '';
        if (locationEl) locationEl.value = exp.location || '';
        if (urlEl) urlEl.value = exp.companyUrl || '';

        console.log('Experience data loaded:', exp);
    }, 100);
}

// حذف خبرة
async function deleteExperience(id) {
    if (confirm('هل أنت متأكد من حذف هذه الخبرة؟')) {
        try {
            await deleteData('experiences', id);
            showMessage('تم حذف الخبرة بنجاح!', 'success');
            await loadAllData();
        } catch (error) {
            showMessage('خطأ في حذف الخبرة: ' + error.message, 'error');
        }
    }
}

// تحديث إحصائيات الخبرات
function updateExperienceStats() {
    const totalExperiencesEl = document.getElementById('totalExperiences');
    const totalCompaniesEl = document.getElementById('totalCompanies');
    const totalYearsEl = document.getElementById('totalYears');

    if (totalExperiencesEl) {
        totalExperiencesEl.textContent = experiences.length;
    }

    if (totalCompaniesEl) {
        const companies = [...new Set(experiences.map(exp => exp.company))];
        totalCompaniesEl.textContent = companies.length;
    }

    if (totalYearsEl) {
        // حساب تقريبي لسنوات الخبرة
        totalYearsEl.textContent = Math.max(experiences.length, 0);
    }
}

// تحميل الدورات
function loadCourses() {
    const grid = document.getElementById('coursesGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (courses.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-graduation-cap"></i><p>لا توجد دورات بعد</p></div>';
        return;
    }

    courses.forEach(course => {
        const card = createCourseCard(course);
        grid.appendChild(card);
    });
}

// إنشاء كارد الدورة
function createCourseCard(course) {
    const div = document.createElement('div');
    div.className = 'admin-card';

    const skillsList = Array.isArray(course.skills_gained) ? course.skills_gained.join(', ') : course.skills_gained;

    div.innerHTML = `
        <div class="card-actions">
            <button class="action-btn edit-btn" onclick="editCourse(${course.id})" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" onclick="deleteCourse(${course.id})" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="card-content">
            <div class="course-header">
                <i class="${course.icon || 'fas fa-graduation-cap'}"></i>
                <h3>${course.title}</h3>
            </div>
            <p class="course-provider">${course.provider}</p>
            <p class="course-duration">${course.duration}</p>
            <p class="course-description">${course.description}</p>
            <p class="course-skills">${skillsList}</p>
            <span class="course-status status-${course.status}">${course.status}</span>
        </div>
    `;

    return div;
}

// معالج إضافة الدورات
async function handleCourseSubmit() {
    const title = document.getElementById('courseTitle').value.trim();
    const icon = document.getElementById('courseIcon').value.trim();
    const status = document.getElementById('courseStatus').value;
    const description = document.getElementById('courseDescription').value.trim();
    const duration = document.getElementById('courseDuration').value.trim();
    const provider = document.getElementById('courseProvider').value.trim();
    const completionDate = document.getElementById('courseCompletionDate').value;
    const certificateUrl = document.getElementById('courseCertificateUrl').value.trim();
    const skillsGained = document.getElementById('courseSkillsGained').value.trim();
    const rating = document.getElementById('courseRating').value;

    if (!title || !icon || !description || !duration || !provider) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }

    const skillsList = skillsGained.split(',').map(skill => skill.trim()).filter(skill => skill);

    const data = {
        title: title,
        icon: icon,
        status: status,
        description: description,
        duration: duration,
        provider: provider,
        completion_date: completionDate,
        certificate_url: certificateUrl,
        skills_gained: skillsList,
        rating: rating
    };

    console.log('editingId:', editingId);
    console.log('course data:', data);

    if (editingId) {
        data.id = editingId;
        console.log('Updating course with data:', data);
        await updateData('courses', data);
        showMessage('تم تحديث الدورة بنجاح!', 'success');
    } else {
        console.log('Adding new course with data:', data);
        await addData('courses', data);
        showMessage('تم إضافة الدورة بنجاح!', 'success');
    }

    closeModal();
    await loadAllData();
}

// تعديل دورة
async function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;

    editingId = id;

    // عرض النافذة أولاً (مع الحفاظ على editingId)
    showAddModal(true);

    // ثم ملء البيانات بعد عرض النافذة
    setTimeout(() => {
        const titleEl = document.getElementById('courseTitle');
        const iconEl = document.getElementById('courseIcon');
        const statusEl = document.getElementById('courseStatus');
        const descEl = document.getElementById('courseDescription');
        const durationEl = document.getElementById('courseDuration');
        const providerEl = document.getElementById('courseProvider');
        const dateEl = document.getElementById('courseCompletionDate');
        const urlEl = document.getElementById('courseCertificateUrl');
        const skillsEl = document.getElementById('courseSkillsGained');
        const ratingEl = document.getElementById('courseRating');

        if (titleEl) titleEl.value = course.title;
        if (iconEl) iconEl.value = course.icon;
        if (statusEl) statusEl.value = course.status;
        if (descEl) descEl.value = course.description;
        if (durationEl) durationEl.value = course.duration;
        if (providerEl) providerEl.value = course.provider;
        if (dateEl) dateEl.value = course.completion_date;
        if (urlEl) urlEl.value = course.certificate_url || '';
        if (skillsEl) skillsEl.value = Array.isArray(course.skills_gained) ? course.skills_gained.join(', ') : course.skills_gained;
        if (ratingEl) ratingEl.value = course.rating;

        console.log('Course data loaded:', course);
    }, 100);
}

// حذف دورة
async function deleteCourse(id) {
    if (confirm('هل أنت متأكد من حذف هذه الدورة؟')) {
        try {
            await deleteData('courses', id);
            showMessage('تم حذف الدورة بنجاح!', 'success');
            await loadAllData();
        } catch (error) {
            showMessage('خطأ في حذف الدورة: ' + error.message, 'error');
        }
    }
}

// تحديث إحصائيات الدورات
function updateCoursesStats() {
    console.log('updateCoursesStats called with', courses.length, 'courses');

    const totalCoursesEl = document.getElementById('totalCourses');
    const totalHoursEl = document.getElementById('totalHours');
    const completedCoursesEl = document.getElementById('completedCourses');

    console.log('Elements found:', {
        totalCourses: !!totalCoursesEl,
        totalHours: !!totalHoursEl,
        completedCourses: !!completedCoursesEl
    });

    if (totalCoursesEl) {
        totalCoursesEl.textContent = courses.length;
        console.log('Total courses updated to:', courses.length);
    }

    if (completedCoursesEl) {
        const completed = courses.filter(course => course.status === 'معتمدة' || course.status === 'completed').length;
        completedCoursesEl.textContent = completed;
        console.log('Completed courses updated to:', completed);
    }

    if (totalHoursEl) {
        const totalHours = courses.reduce((total, course) => {
            const hours = parseInt(course.duration) || 0;
            return total + hours;
        }, 0);
        totalHoursEl.textContent = totalHours;
        console.log('Total hours updated to:', totalHours);
    }
}

// تحميل الشهادات
function loadCertificates() {
    const grid = document.getElementById('certificatesGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (certificates.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-certificate"></i><p>لا توجد شهادات بعد</p></div>';
        return;
    }

    certificates.forEach(certificate => {
        const card = createCertificateCard(certificate);
        grid.appendChild(card);
    });
}

// إنشاء كارد الشهادة
function createCertificateCard(certificate) {
    const div = document.createElement('div');
    div.className = 'admin-card';

    const typeLabels = {
        degree: 'شهادة جامعية',
        diploma: 'دبلوم',
        certificate: 'شهادة تدريبية',
        professional: 'شهادة مهنية',
        online: 'شهادة أونلاين',
        workshop: 'ورشة عمل'
    };

    const fileExtension = certificate.file ? certificate.file.split('.').pop().toLowerCase() : '';
    const isPDF = fileExtension === 'pdf';

    div.innerHTML = `
        <div class="card-actions">
            <button class="action-btn edit-btn" onclick="editCertificate(${certificate.id})" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" onclick="deleteCertificate(${certificate.id})" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="card-content">
            <div class="certificate-header">
                <i class="fas fa-certificate"></i>
                <h3>${certificate.title}</h3>
            </div>
            <p class="certificate-provider"><strong>الجهة المانحة:</strong> ${certificate.provider}</p>
            <p class="certificate-type"><strong>النوع:</strong> ${typeLabels[certificate.type] || certificate.type}</p>
            ${certificate.file ? `
                <div class="certificate-file">
                    ${isPDF ?
                        `<p><i class="fas fa-file-pdf"></i> <a href="${certificate.file}" target="_blank">عرض الشهادة (PDF)</a></p>` :
                        `<img src="${certificate.file}" alt="${certificate.title}" class="certificate-image" onclick="showFullImage('${certificate.file}')">`
                    }
                </div>
            ` : ''}
        </div>
    `;

    return div;
}

// معالج إضافة الشهادات
async function handleCertificateSubmit() {
    const title = document.getElementById('certTitle').value.trim();
    const provider = document.getElementById('certProvider').value.trim();
    const type = document.getElementById('certType').value;
    const file = document.getElementById('certFile').files[0];

    if (!title || !provider || !type || (!file && !editingId)) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }

    let filePath = '';

    // رفع الملف إذا تم اختياره
    if (file) {
        try {
            const formData = new FormData();
            formData.append('image', file); // نستخدم نفس المعامل في upload-certificate.php

            const uploadResponse = await fetch('upload-certificate.php', {
                method: 'POST',
                body: formData
            });

            const uploadResult = await uploadResponse.json();

            if (uploadResult.success) {
                filePath = uploadResult.filename;
                showMessage('تم رفع الملف بنجاح!', 'success');
            } else {
                throw new Error('فشل في رفع الملف: ' + uploadResult.message);
            }
        } catch (error) {
            throw new Error('خطأ في رفع الملف: ' + error.message);
        }
    }

    const data = {
        title: title,
        provider: provider,
        type: type,
        file: filePath
    };

    console.log('editingId:', editingId);
    console.log('certificate data:', data);

    if (editingId) {
        data.id = editingId;
        // إذا لم يتم رفع ملف جديد، احتفظ بالملف القديم
        if (!filePath) {
            const existingCert = certificates.find(cert => cert.id === editingId);
            if (existingCert) {
                data.file = existingCert.file;
            }
        }
        console.log('Updating certificate with data:', data);
        await updateData('certificates', data);
        showMessage('تم تحديث الشهادة بنجاح!', 'success');
    } else {
        console.log('Adding new certificate with data:', data);
        await addData('certificates', data);
        showMessage('تم إضافة الشهادة بنجاح!', 'success');
    }

    closeModal();
    await loadAllData();
}

// تعديل شهادة
async function editCertificate(id) {
    const certificate = certificates.find(item => item.id === id);
    if (!certificate) return;

    editingId = id;

    // عرض النافذة أولاً (مع الحفاظ على editingId)
    showAddModal(true);

    // ثم ملء البيانات بعد عرض النافذة
    setTimeout(() => {
        const titleEl = document.getElementById('certTitle');
        const providerEl = document.getElementById('certProvider');
        const typeEl = document.getElementById('certType');

        if (titleEl) titleEl.value = certificate.title;
        if (providerEl) providerEl.value = certificate.provider;
        if (typeEl) typeEl.value = certificate.type;

        // عرض الملف الحالي
        if (certificate.file) {
            const previewDiv = document.getElementById('certFilePreview');
            const previewContent = document.getElementById('certPreviewContent');

            if (previewDiv && previewContent) {
                const fileExtension = certificate.file.split('.').pop().toLowerCase();
                const isPDF = fileExtension === 'pdf';

                if (isPDF) {
                    previewContent.innerHTML = `
                        <p><i class="fas fa-file-pdf"></i> الملف الحالي:
                        <a href="${certificate.file}" target="_blank">عرض PDF</a></p>
                    `;
                } else {
                    previewContent.innerHTML = `
                        <p>الصورة الحالية:</p>
                        <img src="${certificate.file}" alt="${certificate.title}" style="max-width: 200px; max-height: 200px; border-radius: 5px;">
                    `;
                }
                previewDiv.style.display = 'block';
            }
        }

        console.log('Certificate data loaded:', certificate);
    }, 100);
}

// حذف شهادة
async function deleteCertificate(id) {
    if (confirm('هل أنت متأكد من حذف هذه الشهادة؟')) {
        try {
            await deleteData('certificates', id);
            showMessage('تم حذف الشهادة بنجاح!', 'success');
            await loadAllData();
        } catch (error) {
            showMessage('خطأ في حذف الشهادة: ' + error.message, 'error');
        }
    }
}

// تحديث إحصائيات الشهادات
function updateCertificatesStats() {
    const totalCertificatesEl = document.getElementById('totalCertificates');
    const totalInstitutionsEl = document.getElementById('totalInstitutions');
    const recentCertificatesEl = document.getElementById('recentCertificates');

    if (totalCertificatesEl) {
        totalCertificatesEl.textContent = certificates.length;
    }

    if (totalInstitutionsEl) {
        const institutions = [...new Set(certificates.map(cert => cert.provider))];
        totalInstitutionsEl.textContent = institutions.length;
    }

    if (recentCertificatesEl) {
        const currentYear = new Date().getFullYear();
        const recent = certificates.filter(cert => {
            const certYear = new Date(cert.issue_date).getFullYear();
            return certYear >= currentYear - 1;
        }).length;
        recentCertificatesEl.textContent = recent;
    }
}

// دوال المساعدة للتسميات
function getLevelLabel(level) {
    const labels = {
        beginner: 'مبتدئ',
        intermediate: 'متوسط',
        advanced: 'متقدم',
        expert: 'خبير'
    };
    return labels[level] || 'متوسط';
}

function getExperienceTypeLabel(type) {
    const labels = {
        fulltime: 'دوام كامل',
        parttime: 'دوام جزئي',
        freelance: 'عمل حر',
        contract: 'عقد مؤقت',
        internship: 'تدريب'
    };
    return labels[type] || 'دوام كامل';
}

// دوال مؤقتة للدورات والشهادات
async function handleCourseSubmit() {
    showMessage('ميزة إدارة الدورات قيد التطوير', 'info');
}
