<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأعمال - طارق الشتيوي</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="admin.css">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-cog"></i> لوحة التحكم</h2>
            </div>
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item active">
                        <a href="admin-portfolio.html">
                            <i class="fas fa-briefcase"></i>
                            <span>إدارة الأعمال</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-skills.html">
                            <i class="fas fa-code"></i>
                            <span>المهارات التقنية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-personal-skills.html">
                            <i class="fas fa-user-cog"></i>
                            <span>المهارات الشخصية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-experience.html">
                            <i class="fas fa-briefcase"></i>
                            <span>إدارة الخبرات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-courses.html">
                            <i class="fas fa-graduation-cap"></i>
                            <span>إدارة الدورات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-certificates.html">
                            <i class="fas fa-certificate"></i>
                            <span>إدارة الشهادات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-profile.html">
                            <i class="fas fa-user"></i>
                            <span>إدارة الملف الشخصي</span>
                        </a>
                    </li>
                    <li class="nav-item logout" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل الخروج</span>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <h1>إدارة الأعمال</h1>
                <div class="header-actions">
                    <button class="btn-primary" onclick="showAddModal()">
                        <i class="fas fa-plus"></i> إضافة عمل جديد
                    </button>
                </div>
            </header>

            <!-- Portfolio Section -->
            <section class="admin-section active">
                <div class="section-content">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <i class="fas fa-globe"></i>
                            <div>
                                <h3 id="totalWebsites">0</h3>
                                <p>المواقع</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-images"></i>
                            <div>
                                <h3 id="totalImages">0</h3>
                                <p>الصور</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fab fa-youtube"></i>
                            <div>
                                <h3 id="totalVideos">0</h3>
                                <p>فيديوهات يوتيوب</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="content-grid" id="portfolioGrid">
                        <!-- سيتم تحميل المحتوى هنا -->
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Add/Edit Modal -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة عمل جديد</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addForm">
                    <!-- نموذج الأعمال -->
                    <div id="portfolioForm" class="form-section active">
                        <div class="form-group">
                            <label>نوع المحتوى:</label>
                            <select id="contentType" onchange="toggleContentForm()">
                                <option value="website">موقع إلكتروني</option>
                                <option value="image">صورة</option>
                                <option value="youtube">فيديو يوتيوب</option>
                            </select>
                        </div>
                        
                        <!-- حقول الموقع -->
                        <div id="websiteFields" class="content-fields">
                            <div class="form-group">
                                <label>اسم الموقع:</label>
                                <input type="text" id="websiteName" required placeholder="مثال: موقع شركة التقنية">
                            </div>
                            <div class="form-group">
                                <label>رابط الموقع:</label>
                                <input type="url" id="websiteUrl" required placeholder="https://example.com">
                            </div>
                        </div>

                        <!-- حقول الصورة -->
                        <div id="imageFields" class="content-fields" style="display: none;">
                            <div class="form-group">
                                <label>عنوان الصورة:</label>
                                <input type="text" id="imageTitle" required placeholder="مثال: تصميم واجهة مستخدم">
                            </div>
                            <div class="form-group">
                                <label>رابط الصورة:</label>
                                <input type="url" id="imageUrl" placeholder="https://example.com/image.jpg">
                                <small>أو ارفع صورة من جهازك أدناه</small>
                            </div>
                            <div class="form-group">
                                <label>أو اختر صورة من جهازك:</label>
                                <input type="file" id="imageFile" accept="image/*">
                                <small>الأنواع المدعومة: JPG, PNG, GIF, WebP (حد أقصى 5MB)</small>
                            </div>
                            <div class="form-group">
                                <label>وصف الصورة:</label>
                                <textarea id="imageDescription" placeholder="وصف مختصر للصورة"></textarea>
                            </div>
                            <div class="image-preview" id="imagePreview" style="display: none;">
                                <img id="previewImg" style="max-width: 200px; max-height: 200px; border-radius: 10px;">
                            </div>
                        </div>

                        <!-- حقول اليوتيوب -->
                        <div id="youtubeFields" class="content-fields" style="display: none;">
                            <div class="form-group">
                                <label>عنوان الفيديو:</label>
                                <input type="text" id="youtubeTitle" required placeholder="مثال: شرح البرمجة">
                            </div>
                            <div class="form-group">
                                <label>رابط اليوتيوب:</label>
                                <input type="url" id="youtubeUrl" required placeholder="https://www.youtube.com/watch?v=...">
                            </div>
                            <div class="form-group">
                                <label>وصف الفيديو:</label>
                                <textarea id="youtubeDescription" placeholder="وصف مختصر للفيديو"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal()">إلغاء</button>
                        <button type="button" class="btn-primary" onclick="saveContent()">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="admin-new.js"></script>
</body>
</html>
