<!DOCTYPE html>
<html>
<head>
    <title>Test API</title>
</head>
<body>
    <h1>Test API</h1>
    <button onclick="testAddExperience()">Test Add Experience</button>
    <div id="result"></div>

    <script>
        async function testAddExperience() {
            const data = {
                title: "مطور ويب",
                company: "شركة تجريبية",
                type: "fulltime",
                role: "تطوير المواقع",
                period: "2024",
                tasks: ["تطوير", "تصميم"],
                technologies: "HTML, CSS, JS",
                location: "الرياض",
                companyUrl: "https://example.com"
            };

            try {
                const response = await fetch('api.php?section=experiences', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                document.getElementById('result').innerHTML = JSON.stringify(result, null, 2);
                console.log('Result:', result);
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
