<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير صحيحة');
    }
    
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('لم يتم استلام بيانات صحيحة');
    }
    
    // مسار ملف البيانات
    $dataDir = __DIR__ . '/data/';
    $profileFile = $dataDir . 'profile.json';
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!is_dir($dataDir)) {
        mkdir($dataDir, 0755, true);
    }
    
    // حفظ البيانات
    $result = file_put_contents($profileFile, json_encode($input, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    if ($result === false) {
        throw new Exception('فشل في حفظ البيانات');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'تم حفظ الملف الشخصي بنجاح'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
