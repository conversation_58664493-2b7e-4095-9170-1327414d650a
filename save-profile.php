<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// تسجيل الأخطاء للتشخيص
error_log("=== بدء معالجة طلب حفظ الملف الشخصي ===");

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير صحيحة');
    }

    // قراءة البيانات المرسلة
    $rawInput = file_get_contents('php://input');
    error_log("البيانات المستلمة: " . $rawInput);

    $input = json_decode($rawInput, true);

    if (!$input) {
        error_log("خطأ في تحليل JSON: " . json_last_error_msg());
        throw new Exception('لم يتم استلام بيانات صحيحة: ' . json_last_error_msg());
    }

    error_log("البيانات المحللة: " . print_r($input, true));

    // مسار ملف البيانات
    $dataDir = __DIR__ . '/data/';
    $profileFile = $dataDir . 'profile.json';

    error_log("مسار الملف: " . $profileFile);

    // إنشاء المجلد إذا لم يكن موجوداً
    if (!is_dir($dataDir)) {
        $created = mkdir($dataDir, 0755, true);
        error_log("إنشاء المجلد: " . ($created ? "نجح" : "فشل"));
    }

    // التحقق من صلاحيات الكتابة
    if (!is_writable($dataDir)) {
        throw new Exception('لا توجد صلاحيات كتابة في مجلد البيانات');
    }

    // حفظ البيانات
    $jsonData = json_encode($input, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    $result = file_put_contents($profileFile, $jsonData);

    error_log("نتيجة الكتابة: " . ($result !== false ? $result . " بايت" : "فشل"));

    if ($result === false) {
        throw new Exception('فشل في حفظ البيانات في الملف');
    }

    // التحقق من أن الملف تم حفظه بشكل صحيح
    if (!file_exists($profileFile)) {
        throw new Exception('الملف لم يتم إنشاؤه');
    }

    $savedData = file_get_contents($profileFile);
    if (empty($savedData)) {
        throw new Exception('الملف فارغ بعد الحفظ');
    }

    error_log("تم حفظ البيانات بنجاح");

    echo json_encode([
        'success' => true,
        'message' => 'تم حفظ الملف الشخصي بنجاح',
        'bytes_written' => $result
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    error_log("خطأ في حفظ الملف الشخصي: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
