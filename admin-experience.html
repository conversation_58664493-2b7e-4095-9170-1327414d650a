<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الخبرات - طارق الشتيوي</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="admin.css">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-cog"></i> لوحة التحكم</h2>
            </div>
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="admin-portfolio.html">
                            <i class="fas fa-briefcase"></i>
                            <span>إدارة الأعمال</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-skills.html">
                            <i class="fas fa-code"></i>
                            <span>إدارة المهارات</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="admin-experience.html">
                            <i class="fas fa-briefcase"></i>
                            <span>إدارة الخبرات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-courses.html">
                            <i class="fas fa-graduation-cap"></i>
                            <span>إدارة الدورات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-certificates.html">
                            <i class="fas fa-certificate"></i>
                            <span>إدارة الشهادات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-profile.html">
                            <i class="fas fa-user"></i>
                            <span>إدارة الملف الشخصي</span>
                        </a>
                    </li>
                    <li class="nav-item logout" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل الخروج</span>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <h1>إدارة الخبرات</h1>
                <div class="header-actions">
                    <button class="btn-primary" onclick="showAddModal()">
                        <i class="fas fa-plus"></i> إضافة خبرة جديدة
                    </button>
                </div>
            </header>

            <!-- Experience Section -->
            <section class="admin-section active">
                <div class="section-content">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <i class="fas fa-briefcase"></i>
                            <div>
                                <h3 id="totalExperiences">0</h3>
                                <p>إجمالي الخبرات</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-building"></i>
                            <div>
                                <h3 id="totalCompanies">0</h3>
                                <p>الشركات</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-calendar"></i>
                            <div>
                                <h3 id="totalYears">0</h3>
                                <p>سنوات الخبرة</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="content-grid" id="experienceGrid">
                        <!-- سيتم تحميل الخبرات هنا -->
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Add/Edit Modal -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة خبرة جديدة</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addForm">
                    <div id="experienceForm" class="form-section">
                        <div class="form-group">
                            <label>المسمى الوظيفي:</label>
                            <select id="expTitle" required>
                                <option value="">اختر المسمى الوظيفي</option>
                                <!-- سيتم تحميل المسميات ديناميكياً -->
                            </select>
                            <small>اختر المسمى الوظيفي من القائمة</small>
                        </div>
                        
                        <div class="form-group">
                            <label>اسم الشركة:</label>
                            <input type="text" id="expCompany" placeholder="شركة التقنية المتقدمة" required>
                        </div>
                        
                        <div class="form-group">
                            <label>نوع العمل:</label>
                            <select id="expType" required>
                                <option value="">اختر نوع العمل</option>
                                <option value="fulltime">دوام كامل</option>
                                <option value="parttime">دوام جزئي</option>
                                <option value="freelance">عمل حر</option>
                                <option value="contract">عقد مؤقت</option>
                                <option value="internship">تدريب</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>الدور والمسؤوليات:</label>
                            <textarea id="expRole" placeholder="وصف الدور والمسؤوليات الرئيسية" required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label>الفترة الزمنية:</label>
                            <input type="text" id="expPeriod" placeholder="يناير 2020 - ديسمبر 2022" required>
                        </div>
                        
                        <div class="form-group">
                            <label>المهام والإنجازات (مفصولة بفاصلة):</label>
                            <textarea id="expTasks" placeholder="تطوير تطبيقات ويب, إدارة قواعد البيانات, تحسين الأداء" required></textarea>
                            <small>اكتب المهام والإنجازات مفصولة بفاصلة</small>
                        </div>
                        
                        <div class="form-group">
                            <label>التقنيات المستخدمة:</label>
                            <input type="text" id="expTechnologies" placeholder="React, Node.js, MongoDB">
                        </div>
                        
                        <div class="form-group">
                            <label>موقع الشركة:</label>
                            <input type="text" id="expLocation" placeholder="الرياض, السعودية">
                        </div>
                        
                        <div class="form-group">
                            <label>رابط الشركة:</label>
                            <input type="url" id="expCompanyUrl" placeholder="https://company.com">
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal()">إلغاء</button>
                        <button type="submit" class="btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="admin-new.js"></script>
</body>
</html>
