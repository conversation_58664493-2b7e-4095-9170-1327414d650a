<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// تحديد مجلد البيانات
$dataDir = __DIR__ . '/data/';

// الحصول على نوع العملية والقسم
$method = $_SERVER['REQUEST_METHOD'];
$section = $_GET['section'] ?? '';
$action = $_GET['action'] ?? '';

// دالة لقراءة ملف JSON
function readJsonFile($filename) {
    global $dataDir;
    $filepath = $dataDir . $filename;
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        return json_decode($content, true) ?: [];
    }
    return [];
}

// دالة لكتابة ملف JSON
function writeJsonFile($filename, $data) {
    global $dataDir;
    $filepath = $dataDir . $filename;
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!is_dir($dataDir)) {
        mkdir($dataDir, 0755, true);
    }
    
    return file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

// دالة للحصول على اسم الملف حسب القسم
function getFilename($section) {
    $files = [
        'websites' => 'websites.json',
        'images' => 'images.json',
        'skills' => 'skills.json',
        'experiences' => 'experiences.json',
        'courses' => 'courses.json',
        'certificates' => 'certificates.json'
    ];
    return $files[$section] ?? null;
}

// معالجة الطلبات
try {
    switch ($method) {
        case 'GET':
            if ($section && $filename = getFilename($section)) {
                $data = readJsonFile($filename);
                
                // ترتيب البيانات حسب الترتيب أو التاريخ
                usort($data, function($a, $b) {
                    if (isset($a['order']) && isset($b['order'])) {
                        return $a['order'] - $b['order'];
                    }
                    return strtotime($b['date'] ?? '0') - strtotime($a['date'] ?? '0');
                });
                
                echo json_encode(['success' => true, 'data' => $data]);
            } else {
                echo json_encode(['success' => false, 'message' => 'قسم غير صحيح']);
            }
            break;
            
        case 'POST':
            if ($section && $filename = getFilename($section)) {
                $input = json_decode(file_get_contents('php://input'), true);
                $data = readJsonFile($filename);
                
                // إضافة ID جديد
                $newId = empty($data) ? 1 : max(array_column($data, 'id')) + 1;
                $input['id'] = $newId;
                $input['date'] = date('Y-m-d');
                
                // إضافة ترتيب إذا لم يكن موجوداً
                if (!isset($input['order'])) {
                    $input['order'] = count($data) + 1;
                }
                
                $data[] = $input;
                
                if (writeJsonFile($filename, $data)) {
                    echo json_encode(['success' => true, 'message' => 'تم الحفظ بنجاح', 'data' => $input]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'خطأ في الحفظ']);
                }
            } else {
                echo json_encode(['success' => false, 'message' => 'قسم غير صحيح']);
            }
            break;
            
        case 'PUT':
            if ($section && $filename = getFilename($section)) {
                $input = json_decode(file_get_contents('php://input'), true);
                $data = readJsonFile($filename);
                $id = $input['id'] ?? 0;
                
                // البحث عن العنصر وتحديثه
                $found = false;
                for ($i = 0; $i < count($data); $i++) {
                    if ($data[$i]['id'] == $id) {
                        $data[$i] = array_merge($data[$i], $input);
                        $found = true;
                        break;
                    }
                }
                
                if ($found && writeJsonFile($filename, $data)) {
                    echo json_encode(['success' => true, 'message' => 'تم التحديث بنجاح']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'خطأ في التحديث']);
                }
            } else {
                echo json_encode(['success' => false, 'message' => 'قسم غير صحيح']);
            }
            break;
            
        case 'DELETE':
            if ($section && $filename = getFilename($section)) {
                $id = $_GET['id'] ?? 0;
                $data = readJsonFile($filename);
                
                // حذف العنصر
                $data = array_filter($data, function($item) use ($id) {
                    return $item['id'] != $id;
                });
                
                // إعادة ترقيم المصفوفة
                $data = array_values($data);
                
                if (writeJsonFile($filename, $data)) {
                    echo json_encode(['success' => true, 'message' => 'تم الحذف بنجاح']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'خطأ في الحذف']);
                }
            } else {
                echo json_encode(['success' => false, 'message' => 'قسم غير صحيح']);
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'طريقة غير مدعومة']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم: ' . $e->getMessage()]);
}
?>
