# نظام إدارة المحتوى المحسن - طارق الشتيوي

## التحسينات الجديدة

### 1. نظام إدارة البيانات بـ JSON
- تم استبدال `localStorage` بملفات JSON منفصلة
- كل قسم له ملف JSON منفصل في مجلد `data/`
- إمكانية النسخ الاحتياطي والاستعادة بسهولة

### 2. نظام رفع الصور المحسن
- مجلد منفصل للصور: `/image`
- رفع الصور مباشرة من لوحة التحكم
- معاينة الصور قبل الرفع
- دعم أنواع متعددة: JPG, PNG, GIF, WebP
- حد أقصى للحجم: 5MB

### 3. تبسيط النماذج
- **المواقع**: اسم الموقع + الرابط فقط
- **الصور**: عنوان + رفع الصورة + وصف اختياري
- إزالة الحقول غير الضرورية

### 4. API محسن
- `api.php`: للتعامل مع البيانات (CRUD)
- `upload.php`: لرفع الصور
- `get-data.php`: لعرض البيانات في الصفحة الرئيسية

## ملفات النظام الجديد

### ملفات البيانات (JSON)
```
data/
├── websites.json      # بيانات المواقع
├── images.json        # بيانات الصور
├── skills.json        # بيانات المهارات
├── experiences.json   # بيانات الخبرات
├── courses.json       # بيانات الدورات
└── certificates.json  # بيانات الشهادات
```

### ملفات JavaScript
- `admin-new.js`: ملف JavaScript محسن للوحة التحكم
- `portfolio-display.js`: لعرض البيانات في الصفحة الرئيسية

### ملفات PHP
- `api.php`: API للتعامل مع البيانات
- `upload.php`: رفع الصور
- `get-data.php`: استرجاع البيانات للعرض

## كيفية الاستخدام

### 1. إضافة موقع جديد
1. اذهب إلى صفحة إدارة الأعمال
2. اضغط "إضافة عمل جديد"
3. اختر "موقع إلكتروني"
4. أدخل اسم الموقع والرابط
5. اضغط "حفظ"

### 2. إضافة صورة جديدة
1. اذهب إلى صفحة إدارة الأعمال
2. اضغط "إضافة عمل جديد"
3. اختر "صورة"
4. أدخل عنوان الصورة
5. اختر الصورة من جهازك
6. أدخل وصف اختياري
7. اضغط "حفظ"

### 3. عرض البيانات في الصفحة الرئيسية
- البيانات تظهر تلقائياً من ملفات JSON
- ترتيب حسب الترتيب المحدد أو التاريخ
- فلترة حسب النوع (مواقع/صور)

## المميزات الجديدة

### لوحة التحكم
- ✅ تصميم محسن وسهل الاستخدام
- ✅ رفع الصور مباشرة
- ✅ معاينة الصور قبل الرفع
- ✅ تعديل وحذف المحتوى
- ✅ إحصائيات محدثة

### الصفحة الرئيسية
- ✅ عرض المحتوى من قاعدة البيانات
- ✅ فلترة المحتوى
- ✅ عرض الصور بملء الشاشة
- ✅ روابط مباشرة للمواقع
- ✅ تصميم متجاوب

## الصفحات المحدثة

### صفحات لوحة التحكم
- `admin-portfolio.html` - إدارة الأعمال (محدثة)
- `admin-skills.html` - إدارة المهارات
- `admin-experience.html` - إدارة الخبرات
- `admin-courses.html` - إدارة الدورات
- `admin-certificates.html` - إدارة الشهادات
- `admin-profile.html` - إدارة الملف الشخصي

### الصفحة الرئيسية
- `index.html` - محدثة لاستخدام النظام الجديد

## متطلبات التشغيل
- خادم ويب يدعم PHP (مثل XAMPP, WAMP, Laragon)
- PHP 7.4 أو أحدث
- دعم رفع الملفات في PHP

## الأمان
- التحقق من نوع الملفات المرفوعة
- حد أقصى لحجم الملفات
- تشفير أسماء الملفات
- حماية من الوصول المباشر للملفات الحساسة

## النسخ الاحتياطي
- نسخ مجلد `data/` للحصول على نسخة احتياطية من البيانات
- نسخ مجلد `image/` للحصول على نسخة احتياطية من الصور

## الدعم والتطوير
- النظام قابل للتوسع بسهولة
- إمكانية إضافة أقسام جديدة
- كود منظم وموثق
- تصميم متجاوب ومتوافق مع جميع الأجهزة
