<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مدعومة']);
    exit;
}

// التحقق من وجود الملف
if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => 'لم يتم رفع أي ملف أو حدث خطأ في الرفع']);
    exit;
}

$file = $_FILES['image'];

// التحقق من نوع الملف
$allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
$fileType = $file['type'];

if (!in_array($fileType, $allowedTypes)) {
    echo json_encode(['success' => false, 'message' => 'نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG أو GIF']);
    exit;
}

// التحقق من حجم الملف (5MB كحد أقصى)
$maxSize = 5 * 1024 * 1024; // 5MB
if ($file['size'] > $maxSize) {
    echo json_encode(['success' => false, 'message' => 'حجم الملف كبير جداً. الحد الأقصى هو 5MB']);
    exit;
}

// إنشاء مجلد image إذا لم يكن موجوداً
$uploadDir = 'image/';
if (!file_exists($uploadDir)) {
    if (!mkdir($uploadDir, 0755, true)) {
        echo json_encode(['success' => false, 'message' => 'فشل في إنشاء مجلد الصور']);
        exit;
    }
}

// إنشاء اسم فريد للملف
$fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
$fileName = 'profile_' . time() . '_' . uniqid() . '.' . $fileExtension;
$filePath = $uploadDir . $fileName;

// رفع الملف
if (move_uploaded_file($file['tmp_name'], $filePath)) {
    // حذف الصور القديمة (اختياري)
    $oldFiles = glob($uploadDir . 'profile_*.{jpg,jpeg,png,gif}', GLOB_BRACE);
    foreach ($oldFiles as $oldFile) {
        if ($oldFile !== $filePath && filemtime($oldFile) < (time() - 3600)) { // حذف الملفات الأقدم من ساعة
            unlink($oldFile);
        }
    }
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم رفع الصورة بنجاح',
        'filename' => $fileName,
        'filepath' => $filePath,
        'url' => $filePath
    ]);
} else {
    echo json_encode(['success' => false, 'message' => 'فشل في حفظ الملف']);
}
?>
