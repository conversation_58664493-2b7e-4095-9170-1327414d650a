// ملف JavaScript لعرض البورتفوليو في الصفحة الرئيسية

// تحميل وعرض البورتفوليو
async function loadPortfolio() {
    try {
        // محاولة قراءة من localStorage أولاً
        const localData = localStorage.getItem('portfolioData');
        if (localData) {
            const portfolioData = JSON.parse(localData);
            console.log('تم تحميل البيانات من localStorage:', portfolioData);
            displayPortfolioItems(portfolioData);
            return;
        }

        console.log('لا توجد بيانات في localStorage، سيتم عرض البيانات الافتراضية');
        // عرض البيانات الافتراضية
        displayDefaultPortfolio();
    } catch (error) {
        console.error('خطأ في تحميل البورتفوليو:', error);
        // عرض البيانات الافتراضية
        displayDefaultPortfolio();
    }
}

// عرض البورتفوليو الافتراضي
function displayDefaultPortfolio() {
    console.log('عرض البورتفوليو الافتراضي...');
    const defaultItems = [
        {
            id: 1,
            type: 'website',
            title: 'موقع شركة التقنية',
            url: 'https://example.com',
            description: 'موقع إلكتروني متكامل لشركة تقنية',
            date: '2025-01-20'
        },
        {
            id: 2,
            type: 'image',
            title: 'تصميم واجهة مستخدم',
            src: 'photo/1.jpg',
            description: 'تصميم واجهة مستخدم حديثة وجذابة',
            date: '2025-01-20'
        },
        {
            id: 3,
            type: 'youtube',
            title: 'شرح البرمجة',
            videoId: 'dQw4w9WgXcQ',
            url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
            description: 'فيديو تعليمي عن أساسيات البرمجة',
            date: '2025-01-20'
        }
    ];

    console.log('البيانات الافتراضية:', defaultItems);
    displayPortfolioItems(defaultItems);
}

// عرض عناصر البورتفوليو
function displayPortfolioItems(items) {
    console.log('displayPortfolioItems called with:', items);
    const container = document.getElementById('portfolio-container');
    if (!container) {
        console.error('لم يتم العثور على portfolio-container');
        return;
    }

    container.innerHTML = '';

    if (!items || items.length === 0) {
        console.log('لا توجد عناصر لعرضها');
        container.innerHTML = '<div class="no-items">لا توجد أعمال لعرضها حالياً</div>';
        return;
    }

    // تجميع العناصر حسب النوع
    const websites = items.filter(item => item.type === 'website');
    const images = items.filter(item => item.type === 'image');
    const videos = items.filter(item => item.type === 'youtube');

    console.log('تجميع العناصر:', {
        websites: websites.length,
        images: images.length,
        videos: videos.length
    });

    // 1. عرض الصور كألبوم أولاً
    if (images.length > 0) {
        console.log('إنشاء ألبوم الصور...');
        const albumElement = createImageAlbum(images);
        container.appendChild(albumElement);
    }

    // 2. عرض المواقع (أول 6 مواقع)
    if (websites.length > 0) {
        console.log('عرض المواقع...');
        const websitesToShow = websites.slice(0, 6);
        websitesToShow.forEach(item => {
            const element = createWebsiteElement(item);
            container.appendChild(element);
        });

        // إضافة زر "عرض المزيد" للمواقع إذا كان هناك أكثر من 6
        if (websites.length > 6) {
            const moreWebsitesBtn = createShowMoreButton('websites', websites.length - 6, 'المواقع');
            container.appendChild(moreWebsitesBtn);
        }
    }

    // 3. عرض الفيديوهات (أول 6 فيديوهات)
    if (videos.length > 0) {
        console.log('عرض الفيديوهات...');
        const videosToShow = videos.slice(0, 6);
        videosToShow.forEach(item => {
            const element = createVideoElement(item);
            container.appendChild(element);
        });

        // إضافة زر "عرض المزيد" للفيديوهات إذا كان هناك أكثر من 6
        if (videos.length > 6) {
            const moreVideosBtn = createShowMoreButton('videos', videos.length - 6, 'الفيديوهات');
            container.appendChild(moreVideosBtn);
        }
    }

    // إعداد الفلاتر
    setupPortfolioFilters(items);

    // حفظ البيانات الكاملة للاستخدام في "عرض المزيد"
    window.portfolioFullData = { websites, images, videos };
}

// إنشاء عنصر موقع إلكتروني
function createWebsiteElement(item) {
    const div = document.createElement('div');
    div.className = 'portfolio-item website-item';
    div.setAttribute('data-filter', 'website');

    div.innerHTML = `
        <div class="portfolio-media">
            <div class="website-card">
                <div class="website-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <div class="website-info">
                    <h4>${item.title}</h4>
                    <p>${item.description}</p>
                    <a href="${item.url}" target="_blank" class="visit-btn">
                        <i class="fas fa-external-link-alt"></i> زيارة الموقع
                    </a>
                </div>
            </div>
        </div>
        <div class="portfolio-info">
            <h3>${item.title}</h3>
            <p>${item.description}</p>
        </div>
    `;

    return div;
}

// إنشاء ألبوم الصور
function createImageAlbum(images) {
    const div = document.createElement('div');
    div.className = 'portfolio-item image-album';
    div.setAttribute('data-filter', 'image');

    const thumbnails = images.slice(0, 4).map(img =>
        `<div class="album-thumb" onclick="openImageModal('${img.src}', '${img.title}')">
            <img src="${img.src}" alt="${img.title}" loading="lazy">
            <div class="image-overlay">
                <i class="fas fa-search-plus"></i>
            </div>
        </div>`
    ).join('');

    div.innerHTML = `
        <div class="portfolio-media">
            <div class="image-album-grid">
                ${thumbnails}
                ${images.length > 4 ? `<div class="more-images">+${images.length - 4}</div>` : ''}
            </div>
        </div>
        <div class="portfolio-info">
            <h3>ألبوم الصور (${images.length} صورة)</h3>
            <p>مجموعة من أعمال التصميم والصور</p>
        </div>
    `;

    return div;
}

// إنشاء عنصر فيديو
function createVideoElement(item) {
    const div = document.createElement('div');
    div.className = 'portfolio-item video-item';
    div.setAttribute('data-filter', 'youtube');

    div.innerHTML = `
        <div class="portfolio-media">
            <div class="youtube-card" onclick="openYouTubeModal('${item.videoId}', '${item.title}')">
                <img src="${item.thumbnail}" alt="${item.title}" loading="lazy">
                <div class="youtube-play">
                    <i class="fab fa-youtube"></i>
                </div>
                <div class="video-duration">فيديو</div>
            </div>
        </div>
        <div class="portfolio-info">
            <h3>${item.title}</h3>
            <p>${item.description}</p>
        </div>
    `;

    return div;
}

// إعداد فلاتر البورتفوليو
function setupPortfolioFilters(items) {
    const filterButtons = document.querySelectorAll('.filter-btn');

    filterButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');

            // تحديث الأزرار
            filterButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // فلترة العناصر
            filterPortfolioItems(filter);
        });
    });

    // تطبيق الفلتر الافتراضي (الصور)
    filterPortfolioItems('image');
}

// فلترة عناصر البورتفوليو
function filterPortfolioItems(filter) {
    const portfolioItems = document.querySelectorAll('.portfolio-item');

    portfolioItems.forEach(item => {
        const itemFilter = item.getAttribute('data-filter');

        if (itemFilter === filter) {
            item.style.display = 'block';
            item.style.animation = 'fadeInUp 0.6s ease forwards';
        } else {
            item.style.display = 'none';
        }
    });
}

// فتح نافذة الصورة
function openImageModal(src, title) {
    let modal = document.getElementById('imageModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'imageModal';
        modal.className = 'image-modal';
        modal.innerHTML = `
            <div class="image-modal-content">
                <span class="close-modal" onclick="closeImageModal()">&times;</span>
                <img id="modalImage" src="" alt="">
                <h3 id="modalImageTitle"></h3>
            </div>
        `;
        document.body.appendChild(modal);
    }

    document.getElementById('modalImage').src = src;
    document.getElementById('modalImageTitle').textContent = title;
    modal.style.display = 'block';
}

// إغلاق نافذة الصورة
function closeImageModal() {
    const modal = document.getElementById('imageModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// فتح نافذة يوتيوب (موجودة مسبقاً في script.js)
function openYouTubeModal(videoId, title) {
    let modal = document.getElementById('youtubeModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'youtubeModal';
        modal.className = 'youtube-modal';
        modal.innerHTML = `
            <div class="youtube-modal-content">
                <span class="youtube-close" onclick="closeYouTubeModal()">&times;</span>
                <iframe id="youtubeIframe" class="youtube-iframe" src="" allowfullscreen></iframe>
            </div>
        `;
        document.body.appendChild(modal);
    }

    const iframe = document.getElementById('youtubeIframe');
    iframe.src = `https://www.youtube.com/embed/${videoId}?autoplay=1`;
    modal.style.display = 'block';
}

// إغلاق نافذة يوتيوب
function closeYouTubeModal() {
    const modal = document.getElementById('youtubeModal');
    const iframe = document.getElementById('youtubeIframe');

    if (modal) {
        modal.style.display = 'none';
        if (iframe) {
            iframe.src = '';
        }
    }
}

// إنشاء زر "عرض المزيد"
function createShowMoreButton(type, remainingCount, typeName) {
    const div = document.createElement('div');
    div.className = 'portfolio-item show-more-btn';
    div.setAttribute('data-filter', type === 'websites' ? 'website' : 'youtube');

    div.innerHTML = `
        <div class="show-more-content">
            <div class="show-more-icon">
                <i class="fas fa-plus"></i>
            </div>
            <h3>عرض المزيد من ${typeName}</h3>
            <p>يوجد ${remainingCount} عنصر إضافي</p>
            <button class="show-more-button" onclick="showMoreItems('${type}')">
                <i class="fas fa-eye"></i> عرض الكل
            </button>
        </div>
    `;

    return div;
}

// عرض المزيد من العناصر
function showMoreItems(type) {
    const container = document.getElementById('portfolio-container');
    if (!container || !window.portfolioFullData) return;

    const data = window.portfolioFullData;
    let itemsToAdd = [];

    if (type === 'websites') {
        itemsToAdd = data.websites.slice(6);
    } else if (type === 'videos') {
        itemsToAdd = data.videos.slice(6);
    }

    // إزالة زر "عرض المزيد"
    const showMoreBtn = container.querySelector(`[onclick="showMoreItems('${type}')"]`).closest('.portfolio-item');
    if (showMoreBtn) {
        showMoreBtn.remove();
    }

    // إضافة العناصر الجديدة
    itemsToAdd.forEach(item => {
        let element;
        if (item.type === 'website') {
            element = createWebsiteElement(item);
        } else if (item.type === 'youtube') {
            element = createVideoElement(item);
        }

        if (element) {
            element.style.animation = 'fadeInUp 0.6s ease forwards';
            container.appendChild(element);
        }
    });
}

// مراقبة تغييرات localStorage في نفس النافذة
function watchLocalStorageChanges() {
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function(key, value) {
        originalSetItem.apply(this, arguments);
        if (key === 'portfolioData') {
            console.log('تم تحديث بيانات البورتفوليو في localStorage');
            setTimeout(() => {
                loadPortfolio();
            }, 100);
        }
    };
}

// دالة لاختبار البيانات
function testPortfolioData() {
    const data = localStorage.getItem('portfolioData');
    console.log('البيانات الخام في localStorage:', data);

    if (data) {
        try {
            const parsed = JSON.parse(data);
            console.log('البيانات المحللة:', parsed);
            return parsed;
        } catch (e) {
            console.error('خطأ في تحليل البيانات:', e);
        }
    }
    return null;
}

// دالة لإضافة بيانات تجريبية
function addTestData() {
    const testData = [
        {
            id: Date.now(),
            type: 'website',
            title: 'موقع الاختبار',
            url: 'https://example.com',
            description: 'موقع تجريبي للاختبار',
            date: new Date().toISOString().split('T')[0]
        },
        {
            id: Date.now() + 1,
            type: 'image',
            title: 'صورة تجريبية',
            src: 'photo/1.jpg',
            description: 'صورة للاختبار',
            date: new Date().toISOString().split('T')[0]
        },
        {
            id: Date.now() + 2,
            type: 'youtube',
            title: 'فيديو تجريبي',
            videoId: 'dQw4w9WgXcQ',
            url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
            description: 'فيديو للاختبار',
            date: new Date().toISOString().split('T')[0]
        }
    ];

    localStorage.setItem('portfolioData', JSON.stringify(testData));
    console.log('تم إضافة بيانات تجريبية:', testData);
    return testData;
}

// تحميل البورتفوليو عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تحميل صفحة البورتفوليو...');

    // اختبار البيانات أولاً
    testPortfolioData();

    // إجبار إعادة تحميل البيانات كل ثانية (للاختبار)
    loadPortfolio();

    // إضافة أزرار للاختبار
    setTimeout(() => {
        const container = document.getElementById('portfolio-container');
        if (container) {
            console.log('إضافة أزرار الاختبار...');

            const buttonsDiv = document.createElement('div');
            buttonsDiv.style.cssText = 'text-align: center; margin: 20px; display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;';

            // زر إعادة التحميل
            const reloadBtn = document.createElement('button');
            reloadBtn.textContent = 'إعادة تحميل البيانات';
            reloadBtn.style.cssText = 'padding: 10px 20px; background: #4f46e5; color: white; border: none; border-radius: 5px; cursor: pointer;';
            reloadBtn.onclick = () => {
                console.log('إعادة تحميل يدوي...');
                loadPortfolio();
            };

            // زر إضافة بيانات تجريبية
            const testBtn = document.createElement('button');
            testBtn.textContent = 'إضافة بيانات تجريبية';
            testBtn.style.cssText = 'padding: 10px 20px; background: #10b981; color: white; border: none; border-radius: 5px; cursor: pointer;';
            testBtn.onclick = () => {
                console.log('إضافة بيانات تجريبية...');
                addTestData();
                loadPortfolio();
            };

            // زر مسح البيانات
            const clearBtn = document.createElement('button');
            clearBtn.textContent = 'مسح البيانات';
            clearBtn.style.cssText = 'padding: 10px 20px; background: #ef4444; color: white; border: none; border-radius: 5px; cursor: pointer;';
            clearBtn.onclick = () => {
                console.log('مسح البيانات...');
                localStorage.removeItem('portfolioData');
                loadPortfolio();
            };

            buttonsDiv.appendChild(reloadBtn);
            buttonsDiv.appendChild(testBtn);
            buttonsDiv.appendChild(clearBtn);
            container.appendChild(buttonsDiv);
        }
    }, 1000);
    watchLocalStorageChanges();

    // مراقبة تغييرات localStorage من نوافذ أخرى
    window.addEventListener('storage', function(e) {
        if (e.key === 'portfolioData') {
            console.log('تم تحديث البيانات من نافذة أخرى');
            loadPortfolio();
        }
    });
});
                    <h3>${item.name}</h3>
                    <p>${item.description || 'موقع إلكتروني'}</p>
                    <a href="${item.url}" target="_blank" class="website-link">
                        <i class="fas fa-external-link-alt"></i>
                        زيارة الموقع
                    </a>
                </div>
            </div>
        `;
    } else if (item.type === 'image') {
        content = `
            <div class="portfolio-image">
                <img src="${item.src}" alt="${item.title}" onclick="openImageModal('${item.src}', '${item.title}')">
                <div class="image-overlay">
                    <h3>${item.title}</h3>
                    <p>${item.description || 'عمل تصميمي'}</p>
                </div>
            </div>
        `;
    }
    
    div.innerHTML = content;
    return div;
}

// فتح نافذة عرض الصورة
function openImageModal(src, title) {
    const modal = document.createElement('div');
    modal.className = 'image-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <span class="close-modal" onclick="this.parentElement.parentElement.remove()">&times;</span>
            <img src="${src}" alt="${title}">
            <h3>${title}</h3>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // إغلاق النافذة عند النقر خارجها
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// تحميل وعرض المهارات
async function loadSkills() {
    try {
        const response = await fetch('get-data.php?type=skills');
        const result = await response.json();

        if (result.success) {
            displaySkills(result.data);
        } else {
            console.error('Error loading skills:', result.message);
            displaySkills([]); // عرض رسالة فارغة
        }
    } catch (error) {
        console.error('Error loading skills:', error);
        displaySkills([]); // عرض رسالة فارغة
    }
}

// عرض المهارات
function displaySkills(skills) {
    const container = document.getElementById('skills-container');
    if (!container) return;
    
    container.innerHTML = '';
    
    skills.forEach(skill => {
        const element = createSkillElement(skill);
        container.appendChild(element);
    });
}

// إنشاء عنصر المهارة
function createSkillElement(skill) {
    const div = document.createElement('div');
    div.className = 'skill-item';
    
    const skillsList = Array.isArray(skill.items) ? skill.items.join(', ') : skill.items;
    
    div.innerHTML = `
        <div class="skill-header">
            <i class="${skill.icon || 'fas fa-code'}"></i>
            <h3>${skill.category}</h3>
        </div>
        <p class="skill-description">${skill.description || ''}</p>
        <div class="skill-items">${skillsList}</div>
        <span class="skill-level level-${skill.level || 'intermediate'}">${getLevelLabel(skill.level)}</span>
    `;
    
    return div;
}

// تحميل وعرض الخبرات
async function loadExperiences() {
    try {
        const response = await fetch('get-data.php?type=experiences');
        const result = await response.json();

        if (result.success) {
            displayExperiences(result.data);
        } else {
            console.error('Error loading experiences:', result.message);
            displayExperiences([]); // عرض رسالة فارغة
        }
    } catch (error) {
        console.error('Error loading experiences:', error);
        displayExperiences([]); // عرض رسالة فارغة
    }
}

// تحميل وعرض الدورات
async function loadCourses() {
    try {
        const response = await fetch('get-data.php?type=courses');
        const result = await response.json();

        if (result.success) {
            displayCourses(result.data);
        } else {
            console.error('Error loading courses:', result.message);
            displayCourses([]); // عرض رسالة فارغة
        }
    } catch (error) {
        console.error('Error loading courses:', error);
        displayCourses([]); // عرض رسالة فارغة
    }
}

// عرض الخبرات
function displayExperiences(experiences) {
    const container = document.getElementById('experiences-container');
    if (!container) return;
    
    container.innerHTML = '';
    
    experiences.forEach(experience => {
        const element = createExperienceElement(experience);
        container.appendChild(element);
    });
}

// إنشاء عنصر الخبرة
function createExperienceElement(experience) {
    const div = document.createElement('div');
    div.className = 'experience-item';
    
    const tasksList = Array.isArray(experience.tasks) ? 
        experience.tasks.map(task => `<li>${task}</li>`).join('') : 
        `<li>${experience.tasks}</li>`;
    
    div.innerHTML = `
        <div class="experience-header">
            <h3>${experience.title}</h3>
            <span class="experience-period">${experience.period}</span>
        </div>
        <h4>${experience.company}</h4>
        <p class="experience-role">${experience.role}</p>
        <ul class="experience-tasks">${tasksList}</ul>
        ${experience.technologies ? `<div class="experience-tech">${experience.technologies}</div>` : ''}
    `;
    
    return div;
}

// دوال مساعدة
function getLevelLabel(level) {
    const labels = {
        beginner: 'مبتدئ',
        intermediate: 'متوسط',
        advanced: 'متقدم',
        expert: 'خبير'
    };
    return labels[level] || 'متوسط';
}

// تحميل الإحصائيات
async function loadStats() {
    try {
        const response = await fetch('get-data.php?type=stats');
        const result = await response.json();
        
        if (result.success) {
            updateStatsDisplay(result.data);
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

// تحديث عرض الإحصائيات
function updateStatsDisplay(stats) {
    const elements = {
        'total-websites': stats.websites,
        'total-images': stats.images,
        'total-skills': stats.skills,
        'total-experiences': stats.experiences,
        'total-courses': stats.courses,
        'total-certificates': stats.certificates,
        'total-portfolio': stats.total_portfolio
    };
    
    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = elements[id];
        }
    });
}

// تحميل بيانات الملف الشخصي
async function loadProfile() {
    try {
        const response = await fetch('get-data.php?type=profile');
        const result = await response.json();

        if (result.success) {
            updateProfileData(result.data);
        } else {
            console.error('Error loading profile:', result.message);
        }
    } catch (error) {
        console.error('Error loading profile:', error);
    }
}

// تحديث بيانات الملف الشخصي في الصفحة
function updateProfileData(profile) {
    // تحديث المعلومات الشخصية في قسم Hero
    const personalInfo = profile.personal_info || {};

    updateElementText('hero-name', personalInfo.name);
    updateElementText('hero-subtitle', personalInfo.subtitle);
    updateElementText('hero-location', `<i class="fas fa-map-marker-alt"></i> ${personalInfo.location}`);
    updateElementText('hero-email', personalInfo.email);
    updateElementText('hero-phone', personalInfo.phone);
    updateElementText('hero-education', personalInfo.education);
    updateElementText('hero-gpa', personalInfo.gpa);

    // تحديث صورة الملف الشخصي
    const profileImage = document.getElementById('hero-profile-image');
    if (profileImage && personalInfo.profile_image) {
        profileImage.src = personalInfo.profile_image;
    }

    // تحديث قسم نبذة عني
    const aboutSection = profile.about_section || {};
    updateElementText('about-title', aboutSection.title);
    updateElementText('about-paragraph-1', aboutSection.paragraphs?.[0]);
    updateElementText('about-paragraph-2', aboutSection.paragraphs?.[1]);

    // تحديث قسم اللغات
    const languagesSection = profile.languages_section || {};
    updateElementText('languages-title', languagesSection.title);
    displayLanguages(languagesSection.languages || []);

    // تحديث أزرار Hero
    const heroButtons = profile.hero_buttons || {};
    updateElementText('hero-contact-btn', heroButtons.contact_text);
    updateElementText('hero-about-btn', heroButtons.about_text);
}

// دالة مساعدة لتحديث النص
function updateElementText(elementId, text) {
    const element = document.getElementById(elementId);
    if (element && text) {
        element.innerHTML = text;
    }
}

// عرض اللغات
function displayLanguages(languages) {
    const container = document.getElementById('languages-container');
    if (!container) return;

    container.innerHTML = '';

    languages.forEach(language => {
        const languageDiv = document.createElement('div');
        languageDiv.className = 'language-item';
        languageDiv.innerHTML = `
            <h3>${language.name}</h3>
            <p>${language.level}</p>
            <div class="progress-bar">
                <div class="progress" style="width: ${language.percentage}%"></div>
            </div>
        `;
        container.appendChild(languageDiv);
    });
}

// عرض المهارات من JSON
function displaySkills(skills) {
    const container = document.getElementById('skills-container');
    if (!container) return;

    container.innerHTML = '';

    if (skills.length === 0) {
        container.innerHTML = '<div class="no-items">لا توجد مهارات لعرضها حالياً</div>';
        return;
    }

    skills.forEach(skill => {
        const skillDiv = document.createElement('div');
        skillDiv.className = 'skill-category';

        const skillsList = Array.isArray(skill.items) ?
            skill.items.map(item => `<li>${item}</li>`).join('') :
            `<li>${skill.items}</li>`;

        skillDiv.innerHTML = `
            <h3><i class="${skill.icon || 'fas fa-code'}"></i> ${skill.category}</h3>
            <ul class="skill-list">
                ${skillsList}
            </ul>
        `;
        container.appendChild(skillDiv);
    });
}

// عرض الخبرات من JSON
function displayExperiences(experiences) {
    const container = document.getElementById('experiences-container');
    if (!container) return;

    container.innerHTML = '';

    if (experiences.length === 0) {
        container.innerHTML = '<div class="no-items">لا توجد خبرات لعرضها حالياً</div>';
        return;
    }

    experiences.forEach(experience => {
        const experienceDiv = document.createElement('div');
        experienceDiv.className = 'timeline-item';

        const tasksList = Array.isArray(experience.tasks) ?
            experience.tasks.map(task => `<li>${task}</li>`).join('') :
            `<li>${experience.tasks}</li>`;

        experienceDiv.innerHTML = `
            <div class="timeline-content">
                <div class="timeline-marker"></div>
                <div class="timeline-card">
                    <h3>${experience.title}</h3>
                    <h4>${experience.company}</h4>
                    <p class="role">${experience.role}</p>
                    <ul>
                        ${tasksList}
                    </ul>
                </div>
            </div>
        `;
        container.appendChild(experienceDiv);
    });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل البيانات حسب الصفحة الحالية
    const currentPage = window.location.pathname.split('/').pop();

    if (currentPage === 'index.html' || currentPage === '') {
        loadProfile();
        loadPortfolio();
        loadStats();
    }

    // تحميل المهارات والخبرات والدورات والشهادات
    loadSkills();
    loadExperiences();
    loadCourses();
    loadCertificates();
});

// عرض الدورات من JSON
function displayCourses(courses) {
    const container = document.getElementById('courses-container');
    if (!container) return;

    container.innerHTML = '';

    if (courses.length === 0) {
        container.innerHTML = '<div class="no-items">لا توجد دورات لعرضها حالياً</div>';
        return;
    }

    courses.forEach(course => {
        const courseDiv = document.createElement('div');
        courseDiv.className = 'course-card';

        courseDiv.innerHTML = `
            <div class="course-icon">
                <i class="${course.icon || 'fas fa-graduation-cap'}"></i>
            </div>
            <h3>${course.title}</h3>
            <p class="course-duration">${course.status}</p>
            <p>${course.description}</p>
        `;
        container.appendChild(courseDiv);
    });
}

// تحميل وعرض الشهادات
async function loadCertificates() {
    try {
        const response = await fetch('get-data.php?type=certificates');
        const result = await response.json();

        if (result.success) {
            displayCertificates(result.data);
        } else {
            console.error('Error loading certificates:', result.message);
            displayCertificates([]); // عرض رسالة فارغة
        }
    } catch (error) {
        console.error('Error loading certificates:', error);
        displayCertificates([]); // عرض رسالة فارغة
    }
}

// عرض الشهادات من JSON
function displayCertificates(certificates) {
    const container = document.getElementById('certificatesGrid');
    if (!container) return;

    container.innerHTML = '';

    if (certificates.length === 0) {
        container.innerHTML = '<div class="no-items">لا توجد شهادات لعرضها حالياً</div>';
        return;
    }

    const typeLabels = {
        degree: 'شهادة جامعية',
        diploma: 'دبلوم',
        certificate: 'شهادة تدريبية',
        professional: 'شهادة مهنية',
        online: 'شهادة أونلاين',
        workshop: 'ورشة عمل'
    };

    certificates.forEach(certificate => {
        const certDiv = document.createElement('div');
        certDiv.className = 'certificate-card';

        const fileExtension = certificate.file ? certificate.file.split('.').pop().toLowerCase() : '';
        const isPDF = fileExtension === 'pdf';

        certDiv.innerHTML = `
            <div class="certificate-image">
                ${isPDF ?
                    `<div class="pdf-preview-container">
                        <iframe src="${certificate.file}#toolbar=0&navpanes=0&scrollbar=0&view=FitH"
                                frameborder="0"
                                scrolling="no"
                                class="pdf-iframe"
                                title="${certificate.title}"
                                loading="lazy"
                                onerror="this.style.display='none'; this.nextElementSibling.innerHTML='<div class=\\'pdf-fallback\\'><i class=\\'fas fa-file-pdf\\'></i><span>PDF</span><p>انقر للعرض</p></div>'">
                        </iframe>
                        <div class="pdf-overlay" onclick="window.open('${certificate.file}', '_blank')">
                            <i class="fas fa-expand"></i>
                            <span>فتح بحجم كامل</span>
                        </div>
                    </div>` :
                    `<div class="image-container" onclick="window.open('${certificate.file}', '_blank')" style="cursor: pointer;">
                        <img src="${certificate.file || 'photo/7.jpg'}" alt="${certificate.title}" onerror="this.src='photo/7.jpg'">
                        <div class="image-overlay">
                            <i class="fas fa-eye"></i>
                            <span>عرض الشهادة</span>
                        </div>
                    </div>`
                }
            </div>
            <div class="certificate-info">
                <h3>${certificate.title}</h3>
                <p class="certificate-issuer">${certificate.provider}</p>
                <p class="certificate-type">${typeLabels[certificate.type] || certificate.type}</p>
            </div>
        `;
        container.appendChild(certDiv);
    });
}

// تحديث البيانات كل 30 ثانية (اختياري)
setInterval(() => {
    const currentPage = window.location.pathname.split('/').pop();
    if (currentPage === 'index.html' || currentPage === '') {
        loadStats();
    }
}, 30000);
