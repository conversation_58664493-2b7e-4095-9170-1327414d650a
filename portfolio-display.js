// ملف JavaScript لعرض البيانات في الصفحة الرئيسية

// تحميل وعرض البورتفوليو
async function loadPortfolio() {
    try {
        const response = await fetch('get-data.php?type=portfolio');
        const result = await response.json();
        
        if (result.success) {
            displayPortfolioItems(result.data);
        } else {
            console.error('Error loading portfolio:', result.message);
        }
    } catch (error) {
        console.error('Error loading portfolio:', error);
    }
}

// عرض عناصر البورتفوليو
function displayPortfolioItems(items) {
    const container = document.getElementById('portfolio-container');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (items.length === 0) {
        container.innerHTML = '<p class="no-items">لا توجد أعمال لعرضها حالياً</p>';
        return;
    }
    
    items.forEach(item => {
        const element = createPortfolioElement(item);
        container.appendChild(element);
    });
}

// إنشاء عنصر البورتفوليو
function createPortfolioElement(item) {
    const div = document.createElement('div');
    div.className = 'portfolio-item';
    
    let content = '';
    
    if (item.type === 'website') {
        content = `
            <div class="portfolio-website">
                <div class="website-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <div class="website-info">
                    <h3>${item.name}</h3>
                    <p>${item.description || 'موقع إلكتروني'}</p>
                    <a href="${item.url}" target="_blank" class="website-link">
                        <i class="fas fa-external-link-alt"></i>
                        زيارة الموقع
                    </a>
                </div>
            </div>
        `;
    } else if (item.type === 'image') {
        content = `
            <div class="portfolio-image">
                <img src="${item.src}" alt="${item.title}" onclick="openImageModal('${item.src}', '${item.title}')">
                <div class="image-overlay">
                    <h3>${item.title}</h3>
                    <p>${item.description || 'عمل تصميمي'}</p>
                </div>
            </div>
        `;
    }
    
    div.innerHTML = content;
    return div;
}

// فتح نافذة عرض الصورة
function openImageModal(src, title) {
    const modal = document.createElement('div');
    modal.className = 'image-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <span class="close-modal" onclick="this.parentElement.parentElement.remove()">&times;</span>
            <img src="${src}" alt="${title}">
            <h3>${title}</h3>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // إغلاق النافذة عند النقر خارجها
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// تحميل وعرض المهارات
async function loadSkills() {
    try {
        const response = await fetch('get-data.php?type=skills');
        const result = await response.json();

        if (result.success) {
            displaySkills(result.data);
        } else {
            console.error('Error loading skills:', result.message);
            displaySkills([]); // عرض رسالة فارغة
        }
    } catch (error) {
        console.error('Error loading skills:', error);
        displaySkills([]); // عرض رسالة فارغة
    }
}

// عرض المهارات
function displaySkills(skills) {
    const container = document.getElementById('skills-container');
    if (!container) return;
    
    container.innerHTML = '';
    
    skills.forEach(skill => {
        const element = createSkillElement(skill);
        container.appendChild(element);
    });
}

// إنشاء عنصر المهارة
function createSkillElement(skill) {
    const div = document.createElement('div');
    div.className = 'skill-item';
    
    const skillsList = Array.isArray(skill.items) ? skill.items.join(', ') : skill.items;
    
    div.innerHTML = `
        <div class="skill-header">
            <i class="${skill.icon || 'fas fa-code'}"></i>
            <h3>${skill.category}</h3>
        </div>
        <p class="skill-description">${skill.description || ''}</p>
        <div class="skill-items">${skillsList}</div>
        <span class="skill-level level-${skill.level || 'intermediate'}">${getLevelLabel(skill.level)}</span>
    `;
    
    return div;
}

// تحميل وعرض الخبرات
async function loadExperiences() {
    try {
        const response = await fetch('get-data.php?type=experiences');
        const result = await response.json();

        if (result.success) {
            displayExperiences(result.data);
        } else {
            console.error('Error loading experiences:', result.message);
            displayExperiences([]); // عرض رسالة فارغة
        }
    } catch (error) {
        console.error('Error loading experiences:', error);
        displayExperiences([]); // عرض رسالة فارغة
    }
}

// تحميل وعرض الدورات
async function loadCourses() {
    try {
        const response = await fetch('get-data.php?type=courses');
        const result = await response.json();

        if (result.success) {
            displayCourses(result.data);
        } else {
            console.error('Error loading courses:', result.message);
            displayCourses([]); // عرض رسالة فارغة
        }
    } catch (error) {
        console.error('Error loading courses:', error);
        displayCourses([]); // عرض رسالة فارغة
    }
}

// عرض الخبرات
function displayExperiences(experiences) {
    const container = document.getElementById('experiences-container');
    if (!container) return;
    
    container.innerHTML = '';
    
    experiences.forEach(experience => {
        const element = createExperienceElement(experience);
        container.appendChild(element);
    });
}

// إنشاء عنصر الخبرة
function createExperienceElement(experience) {
    const div = document.createElement('div');
    div.className = 'experience-item';
    
    const tasksList = Array.isArray(experience.tasks) ? 
        experience.tasks.map(task => `<li>${task}</li>`).join('') : 
        `<li>${experience.tasks}</li>`;
    
    div.innerHTML = `
        <div class="experience-header">
            <h3>${experience.title}</h3>
            <span class="experience-period">${experience.period}</span>
        </div>
        <h4>${experience.company}</h4>
        <p class="experience-role">${experience.role}</p>
        <ul class="experience-tasks">${tasksList}</ul>
        ${experience.technologies ? `<div class="experience-tech">${experience.technologies}</div>` : ''}
    `;
    
    return div;
}

// دوال مساعدة
function getLevelLabel(level) {
    const labels = {
        beginner: 'مبتدئ',
        intermediate: 'متوسط',
        advanced: 'متقدم',
        expert: 'خبير'
    };
    return labels[level] || 'متوسط';
}

// تحميل الإحصائيات
async function loadStats() {
    try {
        const response = await fetch('get-data.php?type=stats');
        const result = await response.json();
        
        if (result.success) {
            updateStatsDisplay(result.data);
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

// تحديث عرض الإحصائيات
function updateStatsDisplay(stats) {
    const elements = {
        'total-websites': stats.websites,
        'total-images': stats.images,
        'total-skills': stats.skills,
        'total-experiences': stats.experiences,
        'total-courses': stats.courses,
        'total-certificates': stats.certificates,
        'total-portfolio': stats.total_portfolio
    };
    
    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = elements[id];
        }
    });
}

// تحميل بيانات الملف الشخصي
async function loadProfile() {
    try {
        const response = await fetch('get-data.php?type=profile');
        const result = await response.json();

        if (result.success) {
            updateProfileData(result.data);
        } else {
            console.error('Error loading profile:', result.message);
        }
    } catch (error) {
        console.error('Error loading profile:', error);
    }
}

// تحديث بيانات الملف الشخصي في الصفحة
function updateProfileData(profile) {
    // تحديث المعلومات الشخصية في قسم Hero
    const personalInfo = profile.personal_info || {};

    updateElementText('hero-name', personalInfo.name);
    updateElementText('hero-subtitle', personalInfo.subtitle);
    updateElementText('hero-location', `<i class="fas fa-map-marker-alt"></i> ${personalInfo.location}`);
    updateElementText('hero-email', personalInfo.email);
    updateElementText('hero-phone', personalInfo.phone);
    updateElementText('hero-education', personalInfo.education);
    updateElementText('hero-gpa', personalInfo.gpa);

    // تحديث صورة الملف الشخصي
    const profileImage = document.getElementById('hero-profile-image');
    if (profileImage && personalInfo.profile_image) {
        profileImage.src = personalInfo.profile_image;
    }

    // تحديث قسم نبذة عني
    const aboutSection = profile.about_section || {};
    updateElementText('about-title', aboutSection.title);
    updateElementText('about-paragraph-1', aboutSection.paragraphs?.[0]);
    updateElementText('about-paragraph-2', aboutSection.paragraphs?.[1]);

    // تحديث قسم اللغات
    const languagesSection = profile.languages_section || {};
    updateElementText('languages-title', languagesSection.title);
    displayLanguages(languagesSection.languages || []);

    // تحديث أزرار Hero
    const heroButtons = profile.hero_buttons || {};
    updateElementText('hero-contact-btn', heroButtons.contact_text);
    updateElementText('hero-about-btn', heroButtons.about_text);
}

// دالة مساعدة لتحديث النص
function updateElementText(elementId, text) {
    const element = document.getElementById(elementId);
    if (element && text) {
        element.innerHTML = text;
    }
}

// عرض اللغات
function displayLanguages(languages) {
    const container = document.getElementById('languages-container');
    if (!container) return;

    container.innerHTML = '';

    languages.forEach(language => {
        const languageDiv = document.createElement('div');
        languageDiv.className = 'language-item';
        languageDiv.innerHTML = `
            <h3>${language.name}</h3>
            <p>${language.level}</p>
            <div class="progress-bar">
                <div class="progress" style="width: ${language.percentage}%"></div>
            </div>
        `;
        container.appendChild(languageDiv);
    });
}

// عرض المهارات من JSON
function displaySkills(skills) {
    const container = document.getElementById('skills-container');
    if (!container) return;

    container.innerHTML = '';

    if (skills.length === 0) {
        container.innerHTML = '<div class="no-items">لا توجد مهارات لعرضها حالياً</div>';
        return;
    }

    skills.forEach(skill => {
        const skillDiv = document.createElement('div');
        skillDiv.className = 'skill-category';

        const skillsList = Array.isArray(skill.items) ?
            skill.items.map(item => `<li>${item}</li>`).join('') :
            `<li>${skill.items}</li>`;

        skillDiv.innerHTML = `
            <h3><i class="${skill.icon || 'fas fa-code'}"></i> ${skill.category}</h3>
            <ul class="skill-list">
                ${skillsList}
            </ul>
        `;
        container.appendChild(skillDiv);
    });
}

// عرض الخبرات من JSON
function displayExperiences(experiences) {
    const container = document.getElementById('experiences-container');
    if (!container) return;

    container.innerHTML = '';

    if (experiences.length === 0) {
        container.innerHTML = '<div class="no-items">لا توجد خبرات لعرضها حالياً</div>';
        return;
    }

    experiences.forEach(experience => {
        const experienceDiv = document.createElement('div');
        experienceDiv.className = 'timeline-item';

        const tasksList = Array.isArray(experience.tasks) ?
            experience.tasks.map(task => `<li>${task}</li>`).join('') :
            `<li>${experience.tasks}</li>`;

        experienceDiv.innerHTML = `
            <div class="timeline-content">
                <div class="timeline-marker"></div>
                <div class="timeline-card">
                    <h3>${experience.title}</h3>
                    <h4>${experience.company}</h4>
                    <p class="role">${experience.role}</p>
                    <ul>
                        ${tasksList}
                    </ul>
                </div>
            </div>
        `;
        container.appendChild(experienceDiv);
    });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل البيانات حسب الصفحة الحالية
    const currentPage = window.location.pathname.split('/').pop();

    if (currentPage === 'index.html' || currentPage === '') {
        loadProfile();
        loadPortfolio();
        loadStats();
    }

    // تحميل المهارات والخبرات والدورات والشهادات
    loadSkills();
    loadExperiences();
    loadCourses();
    loadCertificates();
});

// عرض الدورات من JSON
function displayCourses(courses) {
    const container = document.getElementById('courses-container');
    if (!container) return;

    container.innerHTML = '';

    if (courses.length === 0) {
        container.innerHTML = '<div class="no-items">لا توجد دورات لعرضها حالياً</div>';
        return;
    }

    courses.forEach(course => {
        const courseDiv = document.createElement('div');
        courseDiv.className = 'course-card';

        courseDiv.innerHTML = `
            <div class="course-icon">
                <i class="${course.icon || 'fas fa-graduation-cap'}"></i>
            </div>
            <h3>${course.title}</h3>
            <p class="course-duration">${course.status}</p>
            <p>${course.description}</p>
        `;
        container.appendChild(courseDiv);
    });
}

// تحميل وعرض الشهادات
async function loadCertificates() {
    try {
        const response = await fetch('get-data.php?type=certificates');
        const result = await response.json();

        if (result.success) {
            displayCertificates(result.data);
        } else {
            console.error('Error loading certificates:', result.message);
            displayCertificates([]); // عرض رسالة فارغة
        }
    } catch (error) {
        console.error('Error loading certificates:', error);
        displayCertificates([]); // عرض رسالة فارغة
    }
}

// عرض الشهادات من JSON
function displayCertificates(certificates) {
    const container = document.getElementById('certificatesGrid');
    if (!container) return;

    container.innerHTML = '';

    if (certificates.length === 0) {
        container.innerHTML = '<div class="no-items">لا توجد شهادات لعرضها حالياً</div>';
        return;
    }

    const typeLabels = {
        degree: 'شهادة جامعية',
        diploma: 'دبلوم',
        certificate: 'شهادة تدريبية',
        professional: 'شهادة مهنية',
        online: 'شهادة أونلاين',
        workshop: 'ورشة عمل'
    };

    certificates.forEach(certificate => {
        const certDiv = document.createElement('div');
        certDiv.className = 'certificate-card';

        const fileExtension = certificate.file ? certificate.file.split('.').pop().toLowerCase() : '';
        const isPDF = fileExtension === 'pdf';

        certDiv.innerHTML = `
            <div class="certificate-image">
                ${isPDF ?
                    `<div class="pdf-preview-container">
                        <iframe src="${certificate.file}#toolbar=0&navpanes=0&scrollbar=0&view=FitH"
                                frameborder="0"
                                scrolling="no"
                                class="pdf-iframe"
                                title="${certificate.title}"
                                loading="lazy"
                                onerror="this.style.display='none'; this.nextElementSibling.innerHTML='<div class=\\'pdf-fallback\\'><i class=\\'fas fa-file-pdf\\'></i><span>PDF</span><p>انقر للعرض</p></div>'">
                        </iframe>
                        <div class="pdf-overlay" onclick="window.open('${certificate.file}', '_blank')">
                            <i class="fas fa-expand"></i>
                            <span>فتح بحجم كامل</span>
                        </div>
                    </div>` :
                    `<div class="image-container" onclick="window.open('${certificate.file}', '_blank')" style="cursor: pointer;">
                        <img src="${certificate.file || 'photo/7.jpg'}" alt="${certificate.title}" onerror="this.src='photo/7.jpg'">
                        <div class="image-overlay">
                            <i class="fas fa-eye"></i>
                            <span>عرض الشهادة</span>
                        </div>
                    </div>`
                }
            </div>
            <div class="certificate-info">
                <h3>${certificate.title}</h3>
                <p class="certificate-issuer">${certificate.provider}</p>
                <p class="certificate-type">${typeLabels[certificate.type] || certificate.type}</p>
            </div>
        `;
        container.appendChild(certDiv);
    });
}

// تحديث البيانات كل 30 ثانية (اختياري)
setInterval(() => {
    const currentPage = window.location.pathname.split('/').pop();
    if (currentPage === 'index.html' || currentPage === '') {
        loadStats();
    }
}, 30000);
