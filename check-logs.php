<?php
header('Content-Type: text/plain; charset=utf-8');

echo "=== فحص سجل الأخطاء ===\n\n";

// مسارات محتملة لسجل الأخطاء
$logPaths = [
    ini_get('error_log'),
    '/var/log/apache2/error.log',
    '/var/log/httpd/error_log',
    'C:/xampp/apache/logs/error.log',
    'C:/laragon/data/logs/apache_error.log',
    __DIR__ . '/error.log'
];

foreach ($logPaths as $path) {
    if ($path && file_exists($path)) {
        echo "سجل الأخطاء موجود في: $path\n";
        echo "آخر 20 سطر:\n";
        echo "================\n";
        
        $lines = file($path);
        $lastLines = array_slice($lines, -20);
        
        foreach ($lastLines as $line) {
            if (strpos($line, 'upload') !== false || strpos($line, 'رفع') !== false) {
                echo $line;
            }
        }
        echo "\n================\n\n";
        break;
    }
}

// معلومات إضافية
echo "معلومات PHP:\n";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";
echo "max_execution_time: " . ini_get('max_execution_time') . "\n";
echo "memory_limit: " . ini_get('memory_limit') . "\n";

echo "\nصلاحيات المجلدات:\n";
echo "مجلد image: " . (is_writable(__DIR__ . '/image/') ? "قابل للكتابة" : "غير قابل للكتابة") . "\n";
echo "مجلد data: " . (is_writable(__DIR__ . '/data/') ? "قابل للكتابة" : "غير قابل للكتابة") . "\n";
?>
