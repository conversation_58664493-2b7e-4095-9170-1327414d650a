# نظام إدارة المحتوى المتكامل - طارق الشتيوي

## ✨ التحديث الجديد: ربط الصفحة الرئيسية بلوحة التحكم

تم تطوير النظام ليصبح **متكاملاً بالكامل** - الآن يمكن تحرير جميع محتويات الصفحة الرئيسية من لوحة التحكم!

## 🎯 المميزات الجديدة

### 1. **إدارة المحتوى الديناميكي**
- ✅ جميع النصوص في الصفحة الرئيسية قابلة للتحرير
- ✅ تحديث فوري للمحتوى بعد الحفظ
- ✅ نظام JSON منظم لكل قسم

### 2. **الأقسام القابلة للإدارة**

#### 🏠 **القسم الرئيسي (Hero Section)**
- الاسم الكامل
- العنوان الفرعي  
- الموقع الجغرافي
- البريد الإلكتروني والهاتف
- التعليم والمعدل التراكمي
- صورة الملف الشخصي

#### 📝 **قسم نبذة عني**
- عنوان القسم
- فقرتين قابلتين للتحرير
- محتوى ديناميكي بالكامل

#### 🌐 **قسم اللغات**
- إضافة/حذف/تعديل اللغات
- تحديد مستوى الإتقان
- نسبة مئوية لكل لغة

#### 💼 **قسم المهارات**
- تصنيفات المهارات
- أيقونات مخصصة
- قوائم المهارات التفصيلية

#### 🏢 **قسم الخبرات**
- تفاصيل الوظائف
- المهام والمسؤوليات
- التقنيات المستخدمة

## 🗂️ **هيكل الملفات الجديد**

```
tareq_cv/
├── data/                          # ملفات البيانات JSON
│   ├── profile.json              # المعلومات الشخصية والأقسام الرئيسية
│   ├── websites.json             # بيانات المواقع
│   ├── images.json               # بيانات الصور
│   ├── skills.json               # بيانات المهارات
│   ├── experiences.json          # بيانات الخبرات
│   ├── courses.json              # بيانات الدورات
│   └── certificates.json         # بيانات الشهادات
├── image/                        # مجلد الصور المرفوعة
├── admin-portfolio.html          # إدارة الأعمال
├── admin-skills.html             # إدارة المهارات  
├── admin-experience.html         # إدارة الخبرات
├── admin-courses.html            # إدارة الدورات
├── admin-certificates.html       # إدارة الشهادات
├── admin-profile.html            # إدارة الملف الشخصي ⭐ جديد
├── api.php                       # API للبيانات
├── upload.php                    # رفع الصور
├── get-data.php                  # استرجاع البيانات
├── save-profile.php              # حفظ الملف الشخصي ⭐ جديد
├── admin-new.js                  # JavaScript لوحة التحكم
├── admin-profile.js              # JavaScript إدارة الملف الشخصي ⭐ جديد
└── portfolio-display.js          # عرض البيانات في الصفحة الرئيسية ⭐ محدث
```

## 🚀 **كيفية الاستخدام**

### 1. **تحرير المعلومات الشخصية**
```
http://localhost/tareq_cv/admin-profile.html
```
- تحرير الاسم والعنوان الفرعي
- تحديث معلومات الاتصال
- تعديل نصوص قسم "نبذة عني"
- إدارة اللغات (إضافة/حذف/تعديل)

### 2. **إدارة المهارات**
```
http://localhost/tareq_cv/admin-skills.html
```
- إضافة تصنيفات مهارات جديدة
- تحديد الأيقونات المناسبة
- إدارة قوائم المهارات التفصيلية

### 3. **إدارة الخبرات**
```
http://localhost/tareq_cv/admin-experience.html
```
- إضافة خبرات عملية جديدة
- تحديد المهام والمسؤوليات
- ربط التقنيات المستخدمة

### 4. **إدارة الأعمال**
```
http://localhost/tareq_cv/admin-portfolio.html
```
- إضافة مواقع إلكترونية (اسم + رابط)
- رفع صور الأعمال مباشرة
- تنظيم البورتفوليو

## 🔄 **تدفق البيانات**

```
لوحة التحكم → ملفات JSON → الصفحة الرئيسية
     ↓              ↓              ↓
  تحرير المحتوى → حفظ البيانات → عرض محدث
```

## 📊 **مثال على ملف البيانات**

### `data/profile.json`
```json
{
  "personal_info": {
    "name": "طارق محمد الشتيوي",
    "subtitle": "شاب متحمس ومحب لتخصص الحاسب الالي",
    "location": "القصيم، بريدة",
    "email": "<EMAIL>",
    "phone": "0503839769"
  },
  "about_section": {
    "title": "نبذة عني",
    "paragraphs": [
      "الفقرة الأولى...",
      "الفقرة الثانية..."
    ]
  },
  "languages_section": {
    "title": "اللغات",
    "languages": [
      {
        "name": "العربية",
        "level": "اللغة الأم", 
        "percentage": 100
      }
    ]
  }
}
```

## 🛠️ **التقنيات المستخدمة**

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: PHP 7.4+
- **البيانات**: JSON Files
- **الصور**: نظام رفع محلي
- **التصميم**: CSS Grid, Flexbox, Responsive Design

## 🔒 **الأمان والحماية**

- ✅ التحقق من صحة البيانات
- ✅ تشفير أسماء الملفات المرفوعة
- ✅ فلترة أنواع الملفات المسموحة
- ✅ حماية من الوصول المباشر للملفات الحساسة

## 📱 **التوافق والاستجابة**

- ✅ متوافق مع جميع المتصفحات الحديثة
- ✅ تصميم متجاوب (Mobile-First)
- ✅ سرعة تحميل محسنة
- ✅ تجربة مستخدم سلسة

## 🎨 **المميزات البصرية**

- 🌙 وضع ليلي/نهاري
- 🎭 انتقالات سلسة
- 📱 تصميم متجاوب
- 🎯 واجهة مستخدم بديهية

## 🔄 **التحديثات المستقبلية**

- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] إدارة الدورات والشهادات
- [ ] تصدير البيانات (PDF/Word)
- [ ] نظام التعليقات والمراجعات
- [ ] تحليلات الزوار

---

**تم التطوير بواسطة**: نظام إدارة المحتوى المتكامل  
**التاريخ**: 2024  
**الإصدار**: 2.0 - النسخة المتكاملة
