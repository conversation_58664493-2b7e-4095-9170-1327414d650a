<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إضافة صورة - البورتفوليو</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; border-radius: 5px; }
        button:hover { background: #005a8b; }
        .result { margin-top: 20px; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .preview { margin-top: 10px; }
        .preview img { max-width: 200px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 اختبار إضافة صورة للبورتفوليو</h1>
    
    <form id="portfolioForm">
        <div class="form-group">
            <label>نوع المحتوى:</label>
            <select id="contentType">
                <option value="website">موقع إلكتروني</option>
                <option value="image" selected>صورة</option>
                <option value="youtube">فيديو يوتيوب</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>عنوان الصورة:</label>
            <input type="text" id="imageTitle" placeholder="مثال: تصميم واجهة مستخدم" required>
        </div>
        
        <div class="form-group">
            <label>وصف الصورة:</label>
            <textarea id="imageDescription" placeholder="وصف مختصر للصورة"></textarea>
        </div>
        
        <div class="form-group">
            <label>اختر الصورة:</label>
            <input type="file" id="imageFile" accept="image/*" required>
        </div>
        
        <div id="imagePreview" class="preview" style="display: none;">
            <img id="previewImg">
        </div>
        
        <button type="submit">حفظ الصورة</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        // متغيرات
        let currentSection = 'portfolio';
        
        // معاينة الصورة
        document.getElementById('imageFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('imagePreview');
                    const img = document.getElementById('previewImg');
                    img.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
        
        // معالج النموذج
        document.getElementById('portfolioForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            const title = document.getElementById('imageTitle').value.trim();
            const description = document.getElementById('imageDescription').value.trim();
            const fileInput = document.getElementById('imageFile');
            
            console.log('Form submitted!');
            console.log('Title:', title);
            console.log('Description:', description);
            console.log('File:', fileInput.files[0]);
            
            if (!title) {
                resultDiv.innerHTML = '<div class="result error">يرجى إدخال عنوان للصورة</div>';
                return;
            }
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="result error">يرجى اختيار صورة</div>';
                return;
            }
            
            try {
                resultDiv.innerHTML = '<div class="result">جاري رفع الصورة...</div>';
                
                // رفع الصورة
                const formData = new FormData();
                formData.append('image', fileInput.files[0]);
                formData.append('title', title);
                formData.append('description', description);
                
                console.log('Uploading to upload.php...');
                
                const response = await fetch('upload.php', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const responseText = await response.text();
                console.log('Response text:', responseText);
                
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    throw new Error('خطأ في تحليل استجابة الخادم: ' + responseText);
                }
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ تم رفع الصورة بنجاح!</h3>
                            <p><strong>العنوان:</strong> ${result.data.title}</p>
                            <p><strong>الوصف:</strong> ${result.data.description}</p>
                            <p><strong>اسم الملف:</strong> ${result.data.filename}</p>
                            <p><strong>الرابط:</strong> ${result.url}</p>
                            <img src="${result.url}" style="max-width: 200px; margin-top: 10px; border-radius: 5px;">
                        </div>
                    `;
                    
                    // مسح النموذج
                    document.getElementById('portfolioForm').reset();
                    document.getElementById('imagePreview').style.display = 'none';
                    
                } else {
                    throw new Error(result.message || 'فشل في رفع الصورة');
                }
                
            } catch (error) {
                console.error('Upload error:', error);
                resultDiv.innerHTML = `<div class="result error">❌ خطأ: ${error.message}</div>`;
            }
        });
    </script>
</body>
</html>
