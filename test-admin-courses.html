<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إدارة الدورات</title>
    <link rel="stylesheet" href="admin.css">
    <style>
        .debug-info {
            background: #f0f0f0;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <h1>اختبار إدارة الدورات</h1>
        
        <div class="debug-info">
            <h3>معلومات التشخيص:</h3>
            <p>القسم الحالي: <span id="current-section-display">غير محدد</span></p>
            <p>عدد الدورات: <span id="courses-count">0</span></p>
            <p>حالة التحميل: <span id="loading-status">لم يبدأ</span></p>
        </div>
        
        <button onclick="manualLoadCourses()" class="btn-primary">تحميل الدورات يدوياً</button>
        <button onclick="showDebugInfo()" class="btn-secondary">عرض معلومات التشخيص</button>
        
        <div class="content-grid" id="coursesGrid">
            <!-- سيتم تحميل الدورات هنا -->
        </div>
    </div>

    <script>
        // تعيين القسم الحالي
        let currentSection = 'courses';
        let courses = [];
        
        // تحديث العرض
        function updateDisplay() {
            document.getElementById('current-section-display').textContent = currentSection;
            document.getElementById('courses-count').textContent = courses.length;
        }
        
        // تحميل الدورات يدوياً
        async function manualLoadCourses() {
            document.getElementById('loading-status').textContent = 'جاري التحميل...';
            
            try {
                console.log('Manual loading courses...');
                const response = await fetch('api.php?section=courses');
                const result = await response.json();
                
                console.log('API Response:', result);
                
                if (result.success) {
                    courses = result.data;
                    console.log('Courses loaded:', courses);
                    document.getElementById('loading-status').textContent = 'تم التحميل بنجاح';
                    loadCourses();
                } else {
                    document.getElementById('loading-status').textContent = 'خطأ: ' + result.message;
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('loading-status').textContent = 'خطأ في الاتصال: ' + error.message;
            }
            
            updateDisplay();
        }
        
        // عرض الدورات
        function loadCourses() {
            console.log('loadCourses called with', courses.length, 'courses');
            
            const grid = document.getElementById('coursesGrid');
            if (!grid) {
                console.error('coursesGrid not found');
                return;
            }
            
            grid.innerHTML = '';
            
            if (courses.length === 0) {
                grid.innerHTML = '<div class="empty-state"><i class="fas fa-graduation-cap"></i><p>لا توجد دورات بعد</p></div>';
                return;
            }
            
            courses.forEach(course => {
                const card = document.createElement('div');
                card.className = 'admin-card';
                card.innerHTML = `
                    <div class="card-content">
                        <div class="course-header">
                            <i class="${course.icon || 'fas fa-graduation-cap'}"></i>
                            <h3>${course.title}</h3>
                        </div>
                        <p class="course-provider">${course.provider}</p>
                        <p class="course-duration">${course.duration}</p>
                        <p class="course-description">${course.description}</p>
                        <span class="course-status">${course.status}</span>
                    </div>
                `;
                grid.appendChild(card);
            });
        }
        
        // عرض معلومات التشخيص
        function showDebugInfo() {
            console.log('=== معلومات التشخيص ===');
            console.log('currentSection:', currentSection);
            console.log('courses:', courses);
            console.log('courses.length:', courses.length);
            console.log('coursesGrid element:', document.getElementById('coursesGrid'));
            
            alert(`
معلومات التشخيص:
- القسم الحالي: ${currentSection}
- عدد الدورات: ${courses.length}
- عنصر coursesGrid: ${document.getElementById('coursesGrid') ? 'موجود' : 'غير موجود'}
            `);
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, currentSection:', currentSection);
            updateDisplay();
            manualLoadCourses();
        });
    </script>
</body>
</html>
