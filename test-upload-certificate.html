<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع ملفات الشهادات</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        button { padding: 10px 20px; margin: 5px; background: #4f46e5; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="file"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .preview { margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 اختبار رفع ملفات الشهادات</h1>
    
    <div class="test-section">
        <h2>1. اختبار رفع ملف شهادة</h2>
        <form id="uploadForm">
            <div class="form-group">
                <label>اختر ملف الشهادة (صورة أو PDF):</label>
                <input type="file" id="fileInput" accept="image/*,.pdf" required>
                <small>الأنواع المدعومة: JPG, PNG, GIF, WebP, PDF (حتى 10MB)</small>
            </div>
            
            <div id="filePreview" class="preview" style="display: none;">
                <div id="previewContent"></div>
            </div>
            
            <button type="submit">رفع الملف</button>
        </form>
        
        <div id="uploadResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. اختبار مباشر للـ API</h2>
        <button onclick="testAPI()">اختبار upload-certificate.php</button>
        <div id="apiResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. الملفات المرفوعة</h2>
        <button onclick="listFiles()">عرض الملفات في مجلد image</button>
        <div id="filesList"></div>
    </div>

    <script>
        // معاينة الملف
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('filePreview');
            const previewContent = document.getElementById('previewContent');
            
            if (file) {
                const fileType = file.type;
                const fileName = file.name;
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                
                if (fileType.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        previewContent.innerHTML = `
                            <p><strong>ملف صورة:</strong> ${fileName} (${fileSize} MB)</p>
                            <img src="${e.target.result}" style="max-width: 300px; max-height: 200px; border-radius: 5px;">
                        `;
                    };
                    reader.readAsDataURL(file);
                } else if (fileType === 'application/pdf') {
                    previewContent.innerHTML = `
                        <p><strong>ملف PDF:</strong> ${fileName} (${fileSize} MB)</p>
                        <p><i class="fas fa-file-pdf"></i> سيتم رفع ملف PDF</p>
                    `;
                } else {
                    previewContent.innerHTML = `
                        <p style="color: red;"><strong>نوع ملف غير مدعوم:</strong> ${fileName}</p>
                    `;
                }
                
                preview.style.display = 'block';
            } else {
                preview.style.display = 'none';
            }
        });
        
        // رفع الملف
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('uploadResult');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<p class="error">يرجى اختيار ملف</p>';
                return;
            }
            
            const formData = new FormData();
            formData.append('image', fileInput.files[0]);
            
            resultDiv.innerHTML = '<p>جاري الرفع...</p>';
            
            try {
                const response = await fetch('upload-certificate.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <p class="success">✅ تم رفع الملف بنجاح!</p>
                        <p><strong>اسم الملف:</strong> ${result.filename}</p>
                        <p><strong>النوع:</strong> ${result.type}</p>
                        <p><strong>الحجم:</strong> ${(result.size / 1024).toFixed(2)} KB</p>
                        ${result.type === 'image' ? 
                            `<img src="${result.filename}" style="max-width: 200px; margin-top: 10px; border-radius: 5px;">` :
                            `<p><a href="${result.filename}" target="_blank">عرض ملف PDF</a></p>`
                        }
                    `;
                } else {
                    resultDiv.innerHTML = `<p class="error">❌ فشل في الرفع: ${result.message}</p>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ: ${error.message}</p>`;
            }
        });
        
        // اختبار API
        async function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            
            try {
                const response = await fetch('upload-certificate.php', {
                    method: 'GET'
                });
                
                const text = await response.text();
                
                if (text.includes('طريقة الطلب غير صحيحة')) {
                    resultDiv.innerHTML = '<p class="success">✅ API يعمل بشكل صحيح</p>';
                } else {
                    resultDiv.innerHTML = '<p class="error">❌ API لا يعمل كما متوقع</p>';
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ في الاتصال: ${error.message}</p>`;
            }
        }
        
        // عرض الملفات
        async function listFiles() {
            const resultDiv = document.getElementById('filesList');
            resultDiv.innerHTML = '<p>هذه الميزة تحتاج إلى API إضافي لعرض محتويات المجلد</p>';
        }
    </script>
</body>
</html>
