// ملف JavaScript لإدارة البورتفوليو
let portfolioData = [];
let editingId = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تسجيل الدخول
    if (!checkAuth()) return;
    
    loadPortfolioData();
    setupEventListeners();
});

// التحقق من المصادقة
function checkAuth() {
    const isLoggedIn = localStorage.getItem('adminLoggedIn');
    if (!isLoggedIn || isLoggedIn !== 'true') {
        alert('يجب تسجيل الدخول أولاً');
        window.location.href = 'index.html';
        return false;
    }
    return true;
}

// تحميل بيانات البورتفوليو
async function loadPortfolioData() {
    try {
        // محاولة قراءة من localStorage أولاً
        const localData = localStorage.getItem('portfolioData');
        if (localData) {
            portfolioData = JSON.parse(localData);
            renderPortfolio();
            updateStats();
            return;
        }
        
        // إذا لم توجد بيانات، تحميل البيانات الافتراضية
        loadDefaultPortfolioData();
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        loadDefaultPortfolioData();
    }
}

// تحميل البيانات الافتراضية
function loadDefaultPortfolioData() {
    portfolioData = [
        {
            id: 1,
            type: 'website',
            title: 'موقع شركة التقنية',
            url: 'https://example.com',
            description: 'موقع إلكتروني متكامل لشركة تقنية',
            date: new Date().toISOString().split('T')[0]
        },
        {
            id: 2,
            type: 'image',
            title: 'تصميم واجهة مستخدم',
            src: 'photo/1.jpg',
            description: 'تصميم واجهة مستخدم حديثة وجذابة',
            date: new Date().toISOString().split('T')[0]
        },
        {
            id: 3,
            type: 'youtube',
            title: 'شرح البرمجة',
            videoId: 'dQw4w9WgXcQ',
            url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
            description: 'فيديو تعليمي عن أساسيات البرمجة',
            date: new Date().toISOString().split('T')[0]
        }
    ];
    
    savePortfolioData();
    renderPortfolio();
    updateStats();
}

// حفظ البيانات
function savePortfolioData() {
    localStorage.setItem('portfolioData', JSON.stringify(portfolioData));
}

// عرض البورتفوليو
function renderPortfolio() {
    const grid = document.getElementById('portfolioGrid');
    if (!grid) return;
    
    grid.innerHTML = '';
    
    if (portfolioData.length === 0) {
        grid.innerHTML = '<div class="no-items">لا توجد أعمال لعرضها</div>';
        return;
    }
    
    portfolioData.forEach(item => {
        const itemElement = createPortfolioCard(item);
        grid.appendChild(itemElement);
    });
}

// إنشاء كارد البورتفوليو
function createPortfolioCard(item) {
    const div = document.createElement('div');
    div.className = 'admin-card portfolio-card';
    
    let typeIcon = '';
    let typeLabel = '';
    
    switch(item.type) {
        case 'website':
            typeIcon = 'fas fa-globe';
            typeLabel = 'موقع إلكتروني';
            break;
        case 'image':
            typeIcon = 'fas fa-image';
            typeLabel = 'صورة';
            break;
        case 'youtube':
            typeIcon = 'fab fa-youtube';
            typeLabel = 'فيديو يوتيوب';
            break;
    }
    
    div.innerHTML = `
        <div class="card-header">
            <div class="card-actions">
                <button class="action-btn edit-btn" onclick="editPortfolioItem(${item.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deletePortfolioItem(${item.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="card-icon">
                <i class="${typeIcon}"></i>
            </div>
        </div>
        <div class="card-content">
            <div class="portfolio-type">${typeLabel}</div>
            <h3>${item.title}</h3>
            <p class="portfolio-description">${item.description || 'لا يوجد وصف'}</p>
            ${item.type === 'website' ? `<div class="portfolio-url"><i class="fas fa-link"></i> ${item.url}</div>` : ''}
            ${item.type === 'image' ? `<div class="portfolio-image"><img src="${item.src}" alt="${item.title}" style="max-width: 100%; height: 100px; object-fit: cover; border-radius: 5px;"></div>` : ''}
            ${item.type === 'youtube' ? `<div class="portfolio-video"><img src="${item.thumbnail}" alt="${item.title}" style="max-width: 100%; height: 100px; object-fit: cover; border-radius: 5px;"></div>` : ''}
            <div class="card-date">
                <i class="fas fa-calendar"></i>
                ${item.date || 'غير محدد'}
            </div>
        </div>
    `;
    
    return div;
}

// تحديث الإحصائيات
function updateStats() {
    const websites = portfolioData.filter(item => item.type === 'website').length;
    const images = portfolioData.filter(item => item.type === 'image').length;
    const videos = portfolioData.filter(item => item.type === 'youtube').length;
    
    document.getElementById('totalWebsites').textContent = websites;
    document.getElementById('totalImages').textContent = images;
    document.getElementById('totalVideos').textContent = videos;
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // نموذج الإضافة
    const form = document.getElementById('addForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
    
    // تغيير نوع المحتوى
    const contentType = document.getElementById('contentType');
    if (contentType) {
        contentType.addEventListener('change', toggleContentForm);
    }
    
    // معاينة الصورة
    const imageFile = document.getElementById('imageFile');
    if (imageFile) {
        imageFile.addEventListener('change', previewImage);
    }
}

// تبديل نموذج المحتوى
function toggleContentForm() {
    const contentType = document.getElementById('contentType').value;
    
    // إخفاء جميع الحقول
    document.getElementById('websiteFields').style.display = 'none';
    document.getElementById('imageFields').style.display = 'none';
    document.getElementById('youtubeFields').style.display = 'none';
    
    // إظهار الحقول المناسبة
    document.getElementById(contentType + 'Fields').style.display = 'block';
}

// معاينة الصورة
function previewImage(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImg').src = e.target.result;
            document.getElementById('imagePreview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
}

// إظهار نافذة الإضافة
function showAddModal() {
    editingId = null;
    document.getElementById('modalTitle').textContent = 'إضافة عمل جديد';
    document.getElementById('addForm').reset();
    document.getElementById('imagePreview').style.display = 'none';
    toggleContentForm();
    document.getElementById('addModal').style.display = 'block';
}

// إغلاق النافذة
function closeModal() {
    document.getElementById('addModal').style.display = 'none';
    editingId = null;
}

// معالجة إرسال النموذج
function handleFormSubmit(e) {
    e.preventDefault();
    
    const contentType = document.getElementById('contentType').value;
    let itemData = {
        id: editingId || Date.now(),
        type: contentType,
        date: new Date().toISOString().split('T')[0]
    };
    
    // جمع البيانات حسب النوع
    if (contentType === 'website') {
        itemData.title = document.getElementById('websiteName').value;
        itemData.url = document.getElementById('websiteUrl').value;
        itemData.description = `موقع إلكتروني: ${itemData.title}`;
    } else if (contentType === 'image') {
        itemData.title = document.getElementById('imageTitle').value;
        itemData.description = document.getElementById('imageDescription').value;
        
        // معالجة الصورة
        const fileInput = document.getElementById('imageFile');
        if (fileInput.files[0]) {
            // في التطبيق الحقيقي، يجب رفع الصورة للخادم
            // هنا سنستخدم URL مؤقت للمعاينة
            itemData.src = URL.createObjectURL(fileInput.files[0]);
        }
    } else if (contentType === 'youtube') {
        itemData.title = document.getElementById('youtubeTitle').value;
        itemData.url = document.getElementById('youtubeUrl').value;
        itemData.description = document.getElementById('youtubeDescription').value;
        
        // استخراج معرف الفيديو من الرابط
        const videoId = extractYouTubeVideoId(itemData.url);
        if (videoId) {
            itemData.videoId = videoId;
            itemData.thumbnail = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
        }
    }
    
    // إضافة أو تحديث العنصر
    if (editingId) {
        const index = portfolioData.findIndex(item => item.id === editingId);
        if (index !== -1) {
            portfolioData[index] = itemData;
        }
    } else {
        portfolioData.push(itemData);
    }
    
    savePortfolioData();
    renderPortfolio();
    updateStats();
    closeModal();
    
    showMessage('تم حفظ العمل بنجاح!', 'success');
}

// استخراج معرف فيديو يوتيوب
function extractYouTubeVideoId(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
}

// تعديل عنصر
function editPortfolioItem(id) {
    const item = portfolioData.find(item => item.id === id);
    if (!item) return;
    
    editingId = id;
    document.getElementById('modalTitle').textContent = 'تعديل العمل';
    
    // تعبئة النموذج
    document.getElementById('contentType').value = item.type;
    toggleContentForm();
    
    if (item.type === 'website') {
        document.getElementById('websiteName').value = item.title;
        document.getElementById('websiteUrl').value = item.url;
    } else if (item.type === 'image') {
        document.getElementById('imageTitle').value = item.title;
        document.getElementById('imageDescription').value = item.description;
    } else if (item.type === 'youtube') {
        document.getElementById('youtubeTitle').value = item.title;
        document.getElementById('youtubeUrl').value = item.url;
        document.getElementById('youtubeDescription').value = item.description;
    }
    
    document.getElementById('addModal').style.display = 'block';
}

// حذف عنصر
function deletePortfolioItem(id) {
    if (confirm('هل أنت متأكد من حذف هذا العمل؟')) {
        portfolioData = portfolioData.filter(item => item.id !== id);
        savePortfolioData();
        renderPortfolio();
        updateStats();
        showMessage('تم حذف العمل بنجاح!', 'success');
    }
}

// إظهار رسالة
function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : '#ef4444'};
        color: white;
        padding: 1rem 2rem;
        border-radius: 8px;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

// تسجيل الخروج
function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        localStorage.removeItem('adminLoggedIn');
        window.location.href = 'index.html';
    }
}
