<!DOCTYPE html>
<html>
<head>
    <title>Test Video API</title>
</head>
<body>
    <h1>Test Video API</h1>
    <button onclick="testAddVideo()">Test Add Video</button>
    <div id="result"></div>

    <script>
        async function testAddVideo() {
            const data = {
                name: "مونتاج",
                title: "مونتاج",
                url: "https://youtu.be/Hy4wGmaNlQg?si=suw5_V0ctoKSfSvP",
                videoId: "Hy4wGmaNlQg",
                thumbnail: "https://img.youtube.com/vi/Hy4wGmaNlQg/maxresdefault.jpg",
                description: "وصف مختصر للفيديو",
                type: "youtube",
                platform: "youtube"
            };

            try {
                console.log('Sending data:', data);
                
                const response = await fetch('api.php?section=websites', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                console.log('Response status:', response.status);
                const result = await response.json();
                console.log('Response result:', result);
                
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
