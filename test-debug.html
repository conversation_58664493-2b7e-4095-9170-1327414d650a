<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مفصل</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        button { padding: 10px 20px; margin: 5px; background: #4f46e5; color: white; border: none; border-radius: 5px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>تشخيص مفصل لمشكلة الدورات</h1>
    
    <div class="test-section">
        <h2>1. اختبار دالة readJsonFile</h2>
        <button onclick="testReadJsonFile()">اختبار دالة القراءة</button>
        <div id="readjson-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. اختبار get-data.php مع تفاصيل</h2>
        <button onclick="testGetDataDetailed()">اختبار مفصل</button>
        <div id="getdata-detailed-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. اختبار مباشر للملف</h2>
        <button onclick="testDirectFile()">اختبار مباشر</button>
        <div id="direct-result"></div>
    </div>

    <script>
        async function testReadJsonFile() {
            const resultDiv = document.getElementById('readjson-result');
            try {
                const response = await fetch('debug-readjson.php');
                const text = await response.text();
                resultDiv.innerHTML = `<pre>${text}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ: ${error.message}</p>`;
            }
        }
        
        async function testGetDataDetailed() {
            const resultDiv = document.getElementById('getdata-detailed-result');
            try {
                const response = await fetch('get-data.php?type=courses');
                const result = await response.json();
                
                resultDiv.innerHTML = `
                    <p>Response status: ${response.status}</p>
                    <p>Response headers: ${response.headers.get('content-type')}</p>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ: ${error.message}</p>`;
            }
        }
        
        async function testDirectFile() {
            const resultDiv = document.getElementById('direct-result');
            try {
                const response = await fetch('data/courses.json');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <p class="success">✅ الملف يعمل مباشرة</p>
                    <p>عدد العناصر: ${data.length}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ: ${error.message}</p>`;
            }
        }
        
        // تشغيل تلقائي
        document.addEventListener('DOMContentLoaded', function() {
            testReadJsonFile();
            setTimeout(testGetDataDetailed, 500);
            setTimeout(testDirectFile, 1000);
        });
    </script>
</body>
</html>
