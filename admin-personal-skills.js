// إدارة المهارات الشخصية
let personalSkills = [];
let editingSkillId = null;

// تحميل المهارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadPersonalSkills();
});

// تحميل المهارات من JSON
async function loadPersonalSkills() {
    try {
        const response = await fetch('data/personal-skills.json');
        if (response.ok) {
            personalSkills = await response.json();
        } else {
            // إذا لم يوجد الملف، استخدم البيانات الافتراضية
            personalSkills = getDefaultPersonalSkills();
        }
        
        // حفظ في localStorage
        localStorage.setItem('personalSkills', JSON.stringify(personalSkills));
        
        displayPersonalSkills();
        updateStats();
    } catch (error) {
        console.error('خطأ في تحميل المهارات:', error);
        // تحميل من localStorage كبديل
        const savedSkills = localStorage.getItem('personalSkills');
        if (savedSkills) {
            personalSkills = JSON.parse(savedSkills);
            displayPersonalSkills();
            updateStats();
        } else {
            personalSkills = getDefaultPersonalSkills();
            displayPersonalSkills();
            updateStats();
        }
    }
}

// البيانات الافتراضية
function getDefaultPersonalSkills() {
    return [
        {
            id: 1,
            title: "القيادة واتخاذ القرارات",
            icon: "fas fa-users",
            description: "القدرة على قيادة الفرق واتخاذ القرارات الصحيحة في الوقت المناسب",
            level: "متقدم",
            order: 1,
            date: new Date().toISOString().split('T')[0]
        },
        {
            id: 2,
            title: "مهارات التواصل الفعّال",
            icon: "fas fa-comments",
            description: "التواصل بوضوح وفعالية مع الآخرين في بيئات مختلفة",
            level: "متقدم",
            order: 2,
            date: new Date().toISOString().split('T')[0]
        }
    ];
}

// عرض المهارات
function displayPersonalSkills() {
    const grid = document.getElementById('skillsGrid');
    grid.innerHTML = '';

    if (personalSkills.length === 0) {
        grid.innerHTML = '<div class="no-items">لا توجد مهارات شخصية لعرضها</div>';
        return;
    }

    // ترتيب المهارات حسب الترتيب
    const sortedSkills = [...personalSkills].sort((a, b) => (a.order || 0) - (b.order || 0));

    sortedSkills.forEach(skill => {
        const skillCard = document.createElement('div');
        skillCard.className = 'admin-card';
        skillCard.innerHTML = `
            <div class="card-header">
                <div class="card-actions">
                    <button class="action-btn edit-btn" onclick="editSkill(${skill.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" onclick="deleteSkill(${skill.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="card-icon">
                    <i class="${skill.icon}"></i>
                </div>
            </div>
            <div class="card-content">
                <h3>${skill.title}</h3>
                <p class="skill-description">${skill.description || 'لا يوجد وصف'}</p>
                <div class="skill-meta">
                    <span class="skill-level level-${skill.level}">${skill.level}</span>
                    <span class="skill-order">ترتيب: ${skill.order}</span>
                </div>
                <div class="card-date">
                    <i class="fas fa-calendar"></i>
                    ${skill.date || 'غير محدد'}
                </div>
            </div>
        `;
        grid.appendChild(skillCard);
    });
}

// تحديث الإحصائيات
function updateStats() {
    const totalSkills = personalSkills.length;
    const advancedSkills = personalSkills.filter(skill => skill.level === 'متقدم' || skill.level === 'خبير').length;
    const intermediateSkills = personalSkills.filter(skill => skill.level === 'متوسط').length;

    document.getElementById('totalSkills').textContent = totalSkills;
    document.getElementById('advancedSkills').textContent = advancedSkills;
    document.getElementById('intermediateSkills').textContent = intermediateSkills;
}

// فتح نافذة الإضافة
function showAddModal() {
    editingSkillId = null;
    document.getElementById('modalTitle').textContent = 'إضافة مهارة جديدة';
    document.getElementById('addForm').reset();
    document.getElementById('skillId').value = '';

    // تعيين الترتيب التالي
    const maxOrder = Math.max(...personalSkills.map(s => s.order || 0), 0);
    document.getElementById('skillOrder').value = maxOrder + 1;

    document.getElementById('addModal').style.display = 'block';
}

// تعديل مهارة
function editSkill(id) {
    const skill = personalSkills.find(s => s.id === id);
    if (!skill) return;

    editingSkillId = id;
    document.getElementById('modalTitle').textContent = 'تعديل المهارة';
    document.getElementById('skillId').value = skill.id;
    document.getElementById('skillTitle').value = skill.title;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillDescription').value = skill.description || '';
    document.getElementById('skillLevel').value = skill.level;
    document.getElementById('skillOrder').value = skill.order;

    document.getElementById('addModal').style.display = 'block';
}

// حذف مهارة
function deleteSkill(id) {
    if (confirm('هل أنت متأكد من حذف هذه المهارة؟')) {
        personalSkills = personalSkills.filter(skill => skill.id !== id);
        savePersonalSkills();
        displayPersonalSkills();
        updateStats();
    }
}

// إغلاق النافذة المنبثقة
function closeModal() {
    document.getElementById('addModal').style.display = 'none';
    editingSkillId = null;
}

// حفظ المهارات
function savePersonalSkills() {
    localStorage.setItem('personalSkills', JSON.stringify(personalSkills));
    
    // محاولة حفظ في ملف JSON (يتطلب خادم)
    try {
        const dataStr = JSON.stringify(personalSkills, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        // إنشاء رابط تحميل مخفي
        const a = document.createElement('a');
        a.href = url;
        a.download = 'personal-skills.json';
        a.style.display = 'none';
        document.body.appendChild(a);
        
        console.log('تم حفظ المهارات في localStorage');
    } catch (error) {
        console.error('خطأ في حفظ الملف:', error);
    }
}

// حفظ المهارة الشخصية
function savePersonalSkill() {
    const formData = {
        title: document.getElementById('skillTitle').value.trim(),
        icon: document.getElementById('skillIcon').value,
        description: document.getElementById('skillDescription').value.trim(),
        level: document.getElementById('skillLevel').value,
        order: parseInt(document.getElementById('skillOrder').value),
        date: new Date().toISOString().split('T')[0]
    };

    if (editingSkillId) {
        // تعديل مهارة موجودة
        const index = personalSkills.findIndex(s => s.id === editingSkillId);
        if (index !== -1) {
            personalSkills[index] = { ...personalSkills[index], ...formData };
        }
    } else {
        // إضافة مهارة جديدة
        const newId = Math.max(...personalSkills.map(s => s.id || 0), 0) + 1;
        formData.id = newId;
        personalSkills.push(formData);
    }

    savePersonalSkills();
    displayPersonalSkills();
    updateStats();
    closeModal();
}

// إغلاق النافذة عند النقر خارجها
window.onclick = function(event) {
    const modal = document.getElementById('addModal');
    if (event.target === modal) {
        closeModal();
    }
}
