/* ===== JavaScript للشريط الجديد ===== */

class ModernNavbar {
    constructor() {
        this.navbar = document.querySelector('.modern-navbar');
        this.mobileToggle = document.querySelector('.mobile-toggle');
        this.mobileMenu = document.querySelector('.mobile-menu');
        this.mobileOverlay = document.querySelector('.mobile-overlay');
        this.themeBtn = document.querySelector('.theme-btn');
        this.mobileLinks = document.querySelectorAll('.mobile-menu-link');

        // عناصر نظام المستخدم
        this.loginBtn = document.getElementById('loginBtn');
        this.userMenu = document.getElementById('userMenu');
        this.userToggle = document.getElementById('userToggle');
        this.userDropdown = document.getElementById('userDropdown');
        this.logoutBtn = document.getElementById('logoutBtn');

        this.init();
    }
    
    init() {
        this.setupScrollEffect();
        this.setupMobileMenu();
        this.setupThemeToggle();
        this.setupSmoothScroll();
        this.setupActiveLink();
        this.setupUserSystem();
        this.handleLoginForm();
    }
    
    // تأثير التمرير
    setupScrollEffect() {
        let lastScrollY = window.scrollY;
        
        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            
            // إضافة كلاس عند التمرير
            if (currentScrollY > 50) {
                this.navbar.classList.add('scrolled');
            } else {
                this.navbar.classList.remove('scrolled');
            }
            
            // إخفاء/إظهار الشريط عند التمرير السريع
            if (currentScrollY > lastScrollY && currentScrollY > 100) {
                this.navbar.style.transform = 'translateY(-100%)';
            } else {
                this.navbar.style.transform = 'translateY(0)';
            }
            
            lastScrollY = currentScrollY;
        });
    }
    
    // إعداد القائمة المحمولة
    setupMobileMenu() {
        // التأكد من وجود العناصر
        if (!this.mobileToggle) {
            this.mobileToggle = document.querySelector('.mobile-toggle');
        }
        if (!this.mobileMenu) {
            this.mobileMenu = document.querySelector('.mobile-menu');
        }
        if (!this.mobileOverlay) {
            this.mobileOverlay = document.querySelector('.mobile-overlay');
        }

        if (this.mobileToggle) {
            this.mobileToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleMobileMenu();
            });

            // إضافة تأثير اللمس للأجهزة المحمولة
            this.mobileToggle.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.toggleMobileMenu();
            });
        }

        if (this.mobileOverlay) {
            this.mobileOverlay.addEventListener('click', () => {
                this.closeMobileMenu();
            });
        }

        // إغلاق القائمة عند النقر على الروابط
        const mobileLinks = document.querySelectorAll('.mobile-menu-link');
        mobileLinks.forEach(link => {
            link.addEventListener('click', () => {
                this.closeMobileMenu();
            });
        });

        // إغلاق القائمة عند تغيير حجم الشاشة
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                this.closeMobileMenu();
            }
        });

        // إغلاق القائمة بمفتاح Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.mobileMenu?.classList.contains('active')) {
                this.closeMobileMenu();
            }
        });
    }
    
    toggleMobileMenu() {
        const isActive = this.mobileMenu?.classList.contains('active');

        if (isActive) {
            this.closeMobileMenu();
        } else {
            this.openMobileMenu();
        }
    }

    openMobileMenu() {
        if (this.mobileToggle) this.mobileToggle.classList.add('active');
        if (this.mobileMenu) this.mobileMenu.classList.add('active');
        if (this.mobileOverlay) this.mobileOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeMobileMenu() {
        if (this.mobileToggle) this.mobileToggle.classList.remove('active');
        if (this.mobileMenu) this.mobileMenu.classList.remove('active');
        if (this.mobileOverlay) this.mobileOverlay.classList.remove('active');
        document.body.style.overflow = 'auto';
    }
    
    // إعداد تبديل الثيم
    setupThemeToggle() {
        if (this.themeBtn) {
            this.themeBtn.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // تحميل الثيم المحفوظ
        this.loadSavedTheme();

        // مزامنة مع الثيم الموجود
        this.syncWithExistingTheme();
    }

    toggleTheme() {
        // استخدام نفس منطق الثيم الموجود
        if (typeof toggleTheme === 'function') {
            toggleTheme();
        } else {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }

        // تحديث أيقونة الثيم
        this.updateThemeIcon();
    }

    loadSavedTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        this.updateThemeIcon();
    }

    updateThemeIcon() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const icon = this.themeBtn.querySelector('i');
        if (icon) {
            icon.className = currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }
    }

    syncWithExistingTheme() {
        // مراقبة تغييرات الثيم من مصادر أخرى
        const observer = new MutationObserver(() => {
            this.updateThemeIcon();
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['data-theme']
        });
    }
    
    // التمرير السلس
    setupSmoothScroll() {
        const links = document.querySelectorAll('.navbar-link, .mobile-menu-link');
        
        links.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                
                if (href && href.startsWith('#')) {
                    e.preventDefault();
                    const target = document.querySelector(href);
                    
                    if (target) {
                        const offsetTop = target.offsetTop - 80; // مساحة للشريط الثابت
                        
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                }
            });
        });
    }
    
    // تفعيل الرابط النشط
    setupActiveLink() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.navbar-link');
        
        window.addEventListener('scroll', () => {
            let current = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                const sectionHeight = section.clientHeight;
                
                if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        });
    }

    // إعداد نظام المستخدم
    setupUserSystem() {
        // تحقق من حالة تسجيل الدخول
        this.checkLoginStatus();

        // إعداد أحداث تسجيل الدخول
        if (this.loginBtn) {
            this.loginBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showLoginModal();
            });
        }

        // إعداد القائمة المنسدلة
        if (this.userToggle) {
            this.userToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleUserDropdown();
            });
        }

        // إعداد تسجيل الخروج
        if (this.logoutBtn) {
            this.logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!this.userToggle?.contains(e.target) && !this.userDropdown?.contains(e.target)) {
                this.closeUserDropdown();
            }
        });
    }

    // تحقق من حالة تسجيل الدخول
    checkLoginStatus() {
        const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
        const userData = JSON.parse(localStorage.getItem('userData') || '{}');

        if (isLoggedIn && userData.name) {
            this.showUserMenu(userData);
        } else {
            this.showLoginButton();
        }
    }

    // إظهار زر تسجيل الدخول
    showLoginButton() {
        if (this.loginBtn) this.loginBtn.style.display = 'flex';
        if (this.userMenu) this.userMenu.classList.remove('active');
    }

    // إظهار قائمة المستخدم
    showUserMenu(userData) {
        if (this.loginBtn) this.loginBtn.style.display = 'none';
        if (this.userMenu) this.userMenu.classList.add('active');

        // تحديث بيانات المستخدم
        this.updateUserInfo(userData);

        // إعادة تحديد العناصر بعد إظهار القائمة
        this.refreshUserElements();
    }

    // تحديث معلومات المستخدم
    updateUserInfo(userData) {
        const userName = userData.name || 'المستخدم';
        const userEmail = userData.email || '<EMAIL>';
        const userInitial = userName.charAt(0).toUpperCase();

        // تحديث الأفاتار والاسم في الشريط
        const userAvatar = document.getElementById('userAvatar');
        const userNameEl = document.getElementById('userName');

        if (userAvatar) userAvatar.textContent = userInitial;
        if (userNameEl) userNameEl.textContent = userName;

        // تحديث القائمة المنسدلة
        const dropdownAvatar = document.getElementById('dropdownAvatar');
        const dropdownUserName = document.getElementById('dropdownUserName');
        const dropdownUserEmail = document.getElementById('dropdownUserEmail');

        if (dropdownAvatar) dropdownAvatar.textContent = userInitial;
        if (dropdownUserName) dropdownUserName.textContent = userName;
        if (dropdownUserEmail) dropdownUserEmail.textContent = userEmail;
    }

    // إعادة تحديد عناصر المستخدم
    refreshUserElements() {
        // إعادة تحديد العناصر
        this.userToggle = document.getElementById('userToggle');
        this.userDropdown = document.getElementById('userDropdown');
        this.logoutBtn = document.getElementById('logoutBtn');

        // إعادة إعداد الأحداث للقائمة المنسدلة
        if (this.userToggle) {
            // إزالة المعالجات القديمة وإضافة جديدة
            this.userToggle.removeEventListener('click', this.userToggleHandler);
            this.userToggleHandler = (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleUserDropdown();
            };
            this.userToggle.addEventListener('click', this.userToggleHandler);
        }

        // إعادة إعداد تسجيل الخروج
        if (this.logoutBtn) {
            this.logoutBtn.removeEventListener('click', this.logoutHandler);
            this.logoutHandler = (e) => {
                e.preventDefault();
                this.logout();
            };
            this.logoutBtn.addEventListener('click', this.logoutHandler);
        }
    }

    // تبديل القائمة المنسدلة
    toggleUserDropdown() {
        // إعادة تحديد العناصر للتأكد
        const userDropdown = document.getElementById('userDropdown');
        const userToggle = document.getElementById('userToggle');

        if (userDropdown && userToggle) {
            const isActive = userDropdown.classList.contains('active');

            if (isActive) {
                this.closeUserDropdown();
            } else {
                this.openUserDropdown();
            }
        }
    }

    // فتح القائمة المنسدلة
    openUserDropdown() {
        const userDropdown = document.getElementById('userDropdown');
        const userToggle = document.getElementById('userToggle');

        if (userDropdown) {
            userDropdown.classList.add('active');
        }
        if (userToggle) {
            userToggle.classList.add('active');
        }
    }

    // إغلاق القائمة المنسدلة
    closeUserDropdown() {
        const userDropdown = document.getElementById('userDropdown');
        const userToggle = document.getElementById('userToggle');

        if (userDropdown) {
            userDropdown.classList.remove('active');
        }
        if (userToggle) {
            userToggle.classList.remove('active');
        }
    }

    // إظهار نافذة تسجيل الدخول
    showLoginModal() {
        const loginOverlay = document.getElementById('loginOverlay');
        if (loginOverlay) {
            loginOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';

            // التركيز على حقل اسم المستخدم
            setTimeout(() => {
                const usernameInput = document.getElementById('username');
                if (usernameInput) usernameInput.focus();
            }, 300);
        }
    }

    // إخفاء نافذة تسجيل الدخول
    hideLoginModal() {
        const loginOverlay = document.getElementById('loginOverlay');
        if (loginOverlay) {
            loginOverlay.classList.remove('active');
            document.body.style.overflow = 'auto';

            // مسح الحقول
            const form = document.getElementById('loginForm');
            if (form) form.reset();

            // إخفاء رسالة الخطأ
            const errorMessage = document.getElementById('errorMessage');
            if (errorMessage) errorMessage.classList.remove('show');
        }
    }

    // معالجة نموذج تسجيل الدخول
    handleLoginForm() {
        const form = document.getElementById('loginForm');
        const submitBtn = document.getElementById('loginSubmit');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        const modal = document.querySelector('.login-modal');

        if (form) {
            form.addEventListener('submit', async (e) => {
                e.preventDefault();

                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();

                // التحقق من الحقول الفارغة
                if (!username || !password) {
                    this.showError('يرجى ملء جميع الحقول');
                    return;
                }

                // إظهار حالة التحميل
                submitBtn.classList.add('loading');
                submitBtn.disabled = true;
                submitBtn.querySelector('span').textContent = 'جاري التحقق...';
                errorMessage.classList.remove('show');
                successMessage.classList.remove('show');

                // محاكاة طلب تسجيل الدخول
                setTimeout(() => {
                    // بيانات تسجيل الدخول الصحيحة
                    const validCredentials = [
                        { username: 'admin', password: 'admin123' },
                        { username: 'tareq', password: 'tareq123' },
                        { username: 'مدير', password: '123456' }
                    ];

                    const isValid = validCredentials.some(cred =>
                        cred.username === username && cred.password === password
                    );

                    if (isValid) {
                        // نجح تسجيل الدخول
                        const userData = {
                            name: username === 'admin' ? 'طارق الشتيوي' : 'المستخدم',
                            email: username === 'admin' ? '<EMAIL>' : '<EMAIL>',
                            role: username === 'admin' ? 'مدير' : 'مستخدم'
                        };

                        this.showSuccess('تم تسجيل الدخول بنجاح!');
                        modal.classList.add('success');

                        setTimeout(() => {
                            this.login(userData);
                            this.hideLoginModal();
                            modal.classList.remove('success');
                        }, 1500);

                    } else {
                        // فشل تسجيل الدخول
                        this.showError('اسم المستخدم أو كلمة المرور غير صحيحة');
                        modal.classList.add('shake');
                        setTimeout(() => modal.classList.remove('shake'), 500);
                    }

                    // إخفاء حالة التحميل
                    submitBtn.classList.remove('loading');
                    submitBtn.disabled = false;
                    submitBtn.querySelector('span').textContent = 'تسجيل الدخول';
                }, 2000);
            });
        }

        // إغلاق النافذة
        const closeBtn = document.getElementById('loginClose');
        const overlay = document.getElementById('loginOverlay');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideLoginModal();
            });
        }

        if (overlay) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.hideLoginModal();
                }
            });
        }

        // إغلاق بمفتاح Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && overlay?.classList.contains('active')) {
                this.hideLoginModal();
            }
        });
    }

    // تسجيل الدخول
    login(userData) {
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('userData', JSON.stringify(userData));

        this.showUserMenu(userData);

        // إشعار نجح تسجيل الدخول
        this.showNotification('تم تسجيل الدخول بنجاح', 'success');
    }

    // تسجيل الخروج
    logout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('userData');

            this.showLoginButton();
            this.closeUserDropdown();

            // إشعار تسجيل الخروج
            this.showNotification('تم تسجيل الخروج بنجاح', 'info');
        }
    }

    // إظهار الإشعارات
    showNotification(message, type = 'info') {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `navbar-notification ${type}`;
        notification.textContent = message;

        // إضافة الأنماط
        Object.assign(notification.style, {
            position: 'fixed',
            top: '90px',
            right: '20px',
            background: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6',
            color: 'white',
            padding: '1rem 1.5rem',
            borderRadius: '8px',
            boxShadow: '0 10px 25px rgba(0,0,0,0.2)',
            zIndex: '10000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });

        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // إخفاء الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // إظهار رسالة خطأ
    showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        if (successMessage) successMessage.classList.remove('show');

        if (errorMessage) {
            errorMessage.textContent = message;
            errorMessage.classList.add('show');

            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(() => {
                errorMessage.classList.remove('show');
            }, 5000);
        }
    }

    // إظهار رسالة نجاح
    showSuccess(message) {
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        if (errorMessage) errorMessage.classList.remove('show');

        if (successMessage) {
            successMessage.textContent = message;
            successMessage.classList.add('show');

            // إخفاء الرسالة بعد 3 ثوان
            setTimeout(() => {
                successMessage.classList.remove('show');
            }, 3000);
        }
    }
}

// تشغيل الشريط عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // التأكد من تحميل جميع العناصر
    setTimeout(() => {
        new ModernNavbar();
    }, 100);
});

// إضافة معالج إضافي للتأكد
window.addEventListener('load', () => {
    // التحقق من وجود الشريط
    if (!window.modernNavbarInstance) {
        window.modernNavbarInstance = new ModernNavbar();
    }
});

// تأثيرات إضافية
document.addEventListener('DOMContentLoaded', () => {
    // تأثير الكتابة للشعار
    const logo = document.querySelector('.navbar-logo');
    if (logo) {
        logo.addEventListener('mouseenter', () => {
            logo.style.animation = 'pulse 0.6s ease-in-out';
        });
        
        logo.addEventListener('animationend', () => {
            logo.style.animation = '';
        });
    }
    
    // تأثير الموجة للروابط
    const links = document.querySelectorAll('.navbar-link');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .navbar-link {
        position: relative;
        overflow: hidden;
    }
    
    .navbar-link.active {
        color: var(--navbar-text-hover) !important;
        background: rgba(79, 70, 229, 0.15) !important;
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(79, 70, 229, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
