<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الفوتر مباشرة</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div style="min-height: 80vh; padding: 20px;">
        <h1>اختبار الفوتر</h1>
        <p>هذه صفحة لاختبار الفوتر مباشرة</p>
        
        <button onclick="testLoadSocialLinks()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 10px;">
            تحميل الروابط من الخادم
        </button>
        
        <button onclick="testAddManualLinks()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 10px;">
            إضافة روابط يدوياً
        </button>
        
        <button onclick="clearFooter()" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 10px;">
            مسح الفوتر
        </button>
        
        <div id="log" style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2025 طارق محمد الشتيوي. جميع الحقوق محفوظة.</p>
                </div>
                <div class="footer-social" id="footerSocial">
                    <!-- سيتم تحميل روابط التواصل الاجتماعي هنا ديناميكياً -->
                </div>
            </div>
        </div>
    </footer>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // نسخ دالة عرض الروابط من script.js
        function displaySocialLinks(socialLinks) {
            log('🎯 عرض الروابط: ' + JSON.stringify(socialLinks));
            const container = document.getElementById('footerSocial');
            if (!container) {
                log('❌ لم يتم العثور على عنصر footerSocial');
                return;
            }

            container.innerHTML = '';

            // تعريف الأيقونات والعناوين
            const socialConfig = {
                email: { icon: 'fas fa-envelope', title: 'البريد الإلكتروني', prefix: 'mailto:' },
                phone: { icon: 'fas fa-phone', title: 'رقم الهاتف', prefix: 'tel:' },
                linkedin: { icon: 'fab fa-linkedin', title: 'LinkedIn', prefix: '' },
                github: { icon: 'fab fa-github', title: 'GitHub', prefix: '' },
                twitter: { icon: 'fab fa-twitter', title: 'Twitter', prefix: '' },
                instagram: { icon: 'fab fa-instagram', title: 'Instagram', prefix: '' },
                youtube: { icon: 'fab fa-youtube', title: 'YouTube', prefix: '' }
            };

            let linksAdded = 0;

            // إضافة الروابط الموجودة فقط
            Object.keys(socialConfig).forEach(key => {
                const value = socialLinks[key];
                if (value && value.trim()) {
                    log(`➕ إضافة رابط ${key}: ${value}`);
                    const config = socialConfig[key];
                    const link = document.createElement('a');
                    link.href = config.prefix + value;
                    link.className = 'social-link';
                    link.title = config.title;
                    if (!config.prefix.startsWith('mailto:') && !config.prefix.startsWith('tel:')) {
                        link.target = '_blank';
                    }
                    link.innerHTML = `<i class="${config.icon}"></i>`;
                    container.appendChild(link);
                    linksAdded++;
                } else {
                    log(`⏭️ تخطي رابط ${key}: فارغ`);
                }
            });

            log(`✅ تم إضافة ${linksAdded} رابط إلى الفوتر`);

            // إذا لم توجد روابط، عرض رسالة
            if (linksAdded === 0) {
                container.innerHTML = '<p style="color: #666; text-align: center;">لا توجد روابط للعرض</p>';
                log('ℹ️ لا توجد روابط للعرض');
            }
        }

        async function testLoadSocialLinks() {
            log('🔄 تحميل الروابط من الخادم...');
            try {
                const response = await fetch('get-data.php?type=social-links');
                const result = await response.json();
                log('📡 استجابة الخادم: ' + JSON.stringify(result));
                
                if (result.success && result.data) {
                    displaySocialLinks(result.data);
                } else {
                    log('❌ لا توجد بيانات في الاستجابة');
                    displaySocialLinks({});
                }
            } catch (error) {
                log('❌ خطأ في تحميل البيانات: ' + error.message);
            }
        }

        function testAddManualLinks() {
            log('🔧 إضافة روابط يدوياً للاختبار...');
            const testLinks = {
                email: '<EMAIL>',
                phone: '1234567890',
                github: 'https://github.com/test',
                twitter: 'https://twitter.com/test',
                linkedin: 'https://linkedin.com/in/test',
                youtube: 'https://youtube.com/test'
            };
            displaySocialLinks(testLinks);
        }

        function clearFooter() {
            log('🗑️ مسح الفوتر...');
            const container = document.getElementById('footerSocial');
            if (container) {
                container.innerHTML = '';
                log('✅ تم مسح الفوتر');
            }
        }

        // تحميل البيانات عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 تم تحميل الصفحة');
            log('🔍 فحص عنصر الفوتر...');
            const container = document.getElementById('footerSocial');
            if (container) {
                log('✅ تم العثور على عنصر footerSocial');
            } else {
                log('❌ لم يتم العثور على عنصر footerSocial');
            }
            
            // تحميل البيانات تلقائياً
            testLoadSocialLinks();
        });
    </script>
</body>
</html>
