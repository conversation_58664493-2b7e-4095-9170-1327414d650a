<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الدورات</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body { padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .test-btn { margin: 10px; padding: 10px 20px; background: #4f46e5; color: white; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>اختبار عرض الدورات</h1>
    
    <div class="test-section">
        <h2>الدورات من JSON</h2>
        <button class="test-btn" onclick="loadTestCourses()">تحميل الدورات</button>
        <div id="courses-result"></div>
    </div>
    
    <div class="test-section">
        <h2>عرض الدورات</h2>
        <div class="courses-grid" id="courses-container">
            <!-- سيتم عرض الدورات هنا -->
        </div>
    </div>

    <script>
        async function loadTestCourses() {
            try {
                const response = await fetch('get-data.php?type=courses');
                const result = await response.json();
                
                const resultDiv = document.getElementById('courses-result');
                if (result.success) {
                    resultDiv.innerHTML = `<p style="color: green;">✅ تم تحميل ${result.data.length} دورة</p>`;
                    displayCourses(result.data);
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">❌ خطأ: ${result.message}</p>`;
                }
            } catch (error) {
                document.getElementById('courses-result').innerHTML = 
                    `<p style="color: red;">❌ خطأ في الاتصال: ${error.message}</p>`;
            }
        }
        
        function displayCourses(courses) {
            const container = document.getElementById('courses-container');
            if (!container) return;
            
            container.innerHTML = '';
            
            if (courses.length === 0) {
                container.innerHTML = '<div class="no-items">لا توجد دورات لعرضها حالياً</div>';
                return;
            }
            
            courses.forEach(course => {
                const courseDiv = document.createElement('div');
                courseDiv.className = 'course-card';
                courseDiv.style.cssText = `
                    border: 1px solid #ddd;
                    padding: 20px;
                    margin: 10px;
                    border-radius: 10px;
                    background: white;
                `;
                
                courseDiv.innerHTML = `
                    <div class="course-icon" style="text-align: center; margin-bottom: 10px;">
                        <i class="${course.icon || 'fas fa-graduation-cap'}" style="font-size: 2rem; color: #4f46e5;"></i>
                    </div>
                    <h3 style="text-align: center; margin: 10px 0;">${course.title}</h3>
                    <p class="course-duration" style="text-align: center; background: #4f46e5; color: white; padding: 5px 10px; border-radius: 15px; display: inline-block; margin: 5px 0;">${course.status}</p>
                    <p style="text-align: center; color: #666; margin: 10px 0;">${course.description}</p>
                    <p style="font-size: 0.9rem; color: #888;">المدة: ${course.duration}</p>
                    <p style="font-size: 0.9rem; color: #888;">المقدم: ${course.provider}</p>
                `;
                container.appendChild(courseDiv);
            });
        }
        
        // تحميل تلقائي عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', loadTestCourses);
    </script>
</body>
</html>
