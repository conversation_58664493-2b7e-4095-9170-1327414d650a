/* ===== شريط التنقل الجديد - متوافق مع جميع الشاشات ===== */

/* متغيرات الألوان */
:root {
    --navbar-bg: rgba(17, 24, 39, 0.95);
    --navbar-bg-solid: #111827;
    --navbar-text: #f9fafb;
    --navbar-text-hover: #4f46e5;
    --navbar-border: rgba(75, 85, 99, 0.3);
    --navbar-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    --navbar-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* متغيرات الثيم الفاتح */
[data-theme="light"] {
    --navbar-bg: rgba(255, 255, 255, 0.95);
    --navbar-bg-solid: #ffffff;
    --navbar-text: #1f2937;
    --navbar-text-hover: #4f46e5;
    --navbar-border: rgba(229, 231, 235, 0.8);
    --navbar-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* متغيرات الثيم الداكن */
[data-theme="dark"] {
    --navbar-bg: rgba(17, 24, 39, 0.95);
    --navbar-bg-solid: #111827;
    --navbar-text: #f9fafb;
    --navbar-text-hover: #4f46e5;
    --navbar-border: rgba(75, 85, 99, 0.3);
    --navbar-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* الشريط الأساسي */
.modern-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--navbar-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--navbar-border);
    box-shadow: var(--navbar-shadow);
    transition: var(--navbar-transition);
    height: 70px;
}

/* عند التمرير */
.modern-navbar.scrolled {
    background: var(--navbar-bg-solid);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
}

/* الحاوي الداخلي */
.navbar-container {
    width: 100%;
    margin: 0;
    padding: 0 2rem 0 2rem;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* الشعار */
.navbar-logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--navbar-text);
    text-decoration: none;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: var(--navbar-transition);
}

.navbar-logo:hover {
    transform: scale(1.05);
}

/* القائمة الرئيسية */
.navbar-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2rem;
}

.navbar-item {
    position: relative;
}

.navbar-link {
    color: var(--navbar-text);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.6rem 1rem;
    border-radius: 8px;
    transition: var(--navbar-transition);
    position: relative;
    margin: 0 0.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #4f46e5, #7c3aed);
    transition: all 0.3s ease;
    transform: translateX(-50%);
    border-radius: 2px 2px 0 0;
}

.navbar-link:hover::before {
    width: 80%;
}

.navbar-link:hover {
    color: var(--navbar-accent);
    background: rgba(79, 70, 229, 0.05);
}

/* تنسيق أيقونات الناف بار */
.navbar-link i {
    color: var(--navbar-accent);
    font-size: 0.9rem;
    width: 16px;
    text-align: center;
    flex-shrink: 0;
}

.navbar-link span {
    flex: 1;
}

.navbar-link:hover i {
    color: var(--navbar-accent);
    transform: scale(1.1);
}

/* الأزرار الجانبية */
.navbar-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* نظام المستخدم */
.user-system {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* زر تسجيل الدخول */
.login-btn {
    background: rgba(79, 70, 229, 0.1);
    color: var(--navbar-accent);
    border: none;
    padding: 0.6rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--navbar-transition);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

.login-btn::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #4f46e5, #7c3aed);
    transition: all 0.3s ease;
    transform: translateX(-50%);
    border-radius: 2px 2px 0 0;
}

.login-btn:hover::before {
    width: 80%;
}

.login-btn:hover {
    background: rgba(79, 70, 229, 0.15);
    color: var(--navbar-accent);
}

/* قائمة المستخدم */
.user-menu {
    position: relative;
    display: none;
}

.user-menu.active {
    display: block;
}

.user-toggle {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    background: rgba(79, 70, 229, 0.1);
    border: 1px solid rgba(79, 70, 229, 0.2);
    border-radius: 12px;
    padding: 0.6rem 1rem;
    cursor: pointer;
    transition: var(--navbar-transition);
    color: var(--navbar-text);
    text-decoration: none;
}

.user-toggle:hover {
    background: rgba(79, 70, 229, 0.2);
    border-color: rgba(79, 70, 229, 0.4);
    transform: translateY(-1px);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--navbar-text);
    line-height: 1.2;
}

.user-role {
    font-size: 0.75rem;
    color: var(--navbar-text-hover);
    opacity: 0.8;
}

.dropdown-arrow {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
    color: var(--navbar-text);
}

.user-toggle.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* القائمة المنسدلة */
.user-dropdown {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background: var(--navbar-bg-solid);
    border: 1px solid var(--navbar-border);
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--navbar-transition);
    z-index: 1001;
}

.user-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px;
    background: var(--navbar-bg-solid);
    border: 1px solid var(--navbar-border);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
}

.dropdown-header {
    padding: 1rem;
    border-bottom: 1px solid var(--navbar-border);
    text-align: center;
}

.dropdown-user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
    margin: 0 auto 0.5rem;
}

.dropdown-user-name {
    font-weight: 600;
    color: var(--navbar-text);
    margin-bottom: 0.2rem;
}

.dropdown-user-email {
    font-size: 0.8rem;
    color: var(--navbar-text);
    opacity: 0.7;
}

.dropdown-menu {
    list-style: none;
    margin: 0;
    padding: 0.5rem 0;
}

.dropdown-item {
    margin: 0;
}

.dropdown-link {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem 1rem;
    color: var(--navbar-text);
    text-decoration: none;
    transition: var(--navbar-transition);
    font-size: 0.9rem;
}

.dropdown-link:hover {
    background: rgba(79, 70, 229, 0.1);
    color: var(--navbar-text-hover);
}

.dropdown-link i {
    width: 16px;
    text-align: center;
    opacity: 0.8;
}

.dropdown-divider {
    height: 1px;
    background: var(--navbar-border);
    margin: 0.5rem 0;
}

.logout-link {
    color: #ef4444 !important;
}

.logout-link:hover {
    background: rgba(239, 68, 68, 0.1) !important;
}

/* زر تبديل الثيم */
.theme-btn {
    width: 45px;
    height: 45px;
    border: 1px solid rgba(79, 70, 229, 0.2);
    border-radius: 8px;
    background: rgba(79, 70, 229, 0.1);
    color: var(--navbar-accent);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: var(--navbar-transition);
    position: relative;
}

.theme-btn::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    transform: scaleY(0);
    transition: transform 0.3s ease;
    border-radius: 0 4px 4px 0;
}

.theme-btn:hover::before {
    transform: scaleY(1);
}

.theme-btn:hover {
    background: rgba(79, 70, 229, 0.15);
    border-color: rgba(79, 70, 229, 0.3);
    color: var(--navbar-accent);
}

/* زر القائمة المحمولة */
.mobile-toggle {
    display: none;
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 12px;
    background: rgba(79, 70, 229, 0.15);
    color: var(--navbar-text);
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: var(--navbar-transition);
    position: relative;
    z-index: 1002;
    border: 1px solid rgba(79, 70, 229, 0.3);
}

.mobile-toggle span {
    width: 15px;
    height: 2px;
    background: var(--navbar-text);
    border-radius: 2px;
    transition: var(--navbar-transition);
}

.mobile-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* القائمة المحمولة */
.mobile-menu {
    position: fixed;
    top: 70px;
    right: -320px;
    width: 260px;
    max-width: 85vw;
    height: auto;
    max-height: calc(100vh - 90px);
    background: var(--navbar-bg-solid);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-left: 1px solid var(--navbar-border);
    border-top: 1px solid var(--navbar-border);
    border-bottom: 1px solid var(--navbar-border);
    border-radius: 0 0 0 15px;
    transition: right 0.3s ease;
    overflow-y: auto;
    box-shadow: -10px 0 30px rgba(0, 0, 0, 0.15);
    z-index: 1000;
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-list {
    list-style: none;
    margin: 0;
    padding: 1rem 0;
}

.mobile-menu-item {
    margin: 0;
}

.mobile-menu-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--navbar-text);
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    padding: 0.5rem 0.5rem;
    border-bottom: 1px solid rgba(75, 85, 99, 0.2);
    transition: var(--navbar-transition);
    position: relative;
    margin: 0 1rem;
    border-radius: 8px;
    direction: rtl;
}

.mobile-menu-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.mobile-menu-link:hover::before {
    transform: scaleY(1);
}

/* تنسيق أيقونات القائمة المحمولة */
.mobile-menu-link i {
    color: var(--navbar-accent);
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
    margin-left: 0.5rem;
}

.mobile-menu-link span {
    flex: 1;
}

.mobile-menu-link:hover i {
    color: var(--navbar-accent);
    transform: scale(1.1);
}

/* قسم المستخدم في أسفل القائمة المحمولة */
.mobile-user-section {
    padding: 0;
    background: transparent;
    border-top: 1px solid var(--navbar-border);
}

/* زر المستخدم القابل للطي */
.mobile-user-toggle {
    width: 100%;
    background: transparent;
    border: none;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Tajawal', sans-serif;
    text-align: right;
}

.mobile-user-toggle:hover {
    background: rgba(79, 70, 229, 0.05);
}

.mobile-user-toggle:active {
    background: rgba(79, 70, 229, 0.1);
}

/* أفاتار المستخدم */
.mobile-user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6b7280, #9ca3af);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.mobile-user-avatar.logged {
    background: linear-gradient(135deg, #10b981, #059669);
    font-size: 1rem;
    font-weight: 700;
}

/* معلومات المستخدم */
.mobile-user-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.2rem;
}

.mobile-user-name {
    color: var(--navbar-text);
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.2;
}

.mobile-user-status {
    color: var(--navbar-text);
    opacity: 0.7;
    font-size: 0.85rem;
    line-height: 1.2;
}

/* أيقونة السهم والدخول */
.mobile-user-icon,
.mobile-user-arrow {
    color: var(--navbar-text);
    opacity: 0.6;
    font-size: 1rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.mobile-user-arrow.rotated {
    transform: rotate(180deg);
}

/* القائمة المنسدلة للمستخدم */
.mobile-user-dropdown {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: rgba(79, 70, 229, 0.02);
    border-top: 1px solid transparent;
}

.mobile-user-dropdown.open {
    max-height: 300px;
    border-top-color: var(--navbar-border);
}

/* روابط قائمة المستخدم */
.mobile-user-link {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem 1rem;
    color: var(--navbar-text);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    border: 1px solid transparent;
}

.mobile-user-link:hover {
    background: rgba(79, 70, 229, 0.1);
    border-color: rgba(79, 70, 229, 0.2);
    color: var(--navbar-text-hover);
    transform: translateX(5px);
}

.mobile-user-link.logout {
    color: #ef4444;
    border-color: rgba(239, 68, 68, 0.2);
}

.mobile-user-link.logout:hover {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #dc2626;
}

.mobile-user-link i {
    width: 16px;
    text-align: center;
    opacity: 0.8;
}

/* فاصل القائمة */
.mobile-menu-divider {
    height: 1px;
    background: var(--navbar-border);
    margin: 0 1rem;
}

.mobile-menu-link:hover {
    background: rgba(79, 70, 229, 0.1);
    color: var(--navbar-text-hover);
    padding-left: 2.5rem;
    border-bottom-color: transparent;
}



/* أفاتار المستخدم */
.mobile-user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6b7280, #9ca3af);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.mobile-user-avatar.logged {
    background: linear-gradient(135deg, #10b981, #059669);
    font-size: 1rem;
    font-weight: 700;
}

/* معلومات المستخدم */
.mobile-user-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.2rem;
}

.mobile-user-name {
    color: var(--navbar-text);
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.2;
}

.mobile-user-status {
    color: var(--navbar-text);
    opacity: 0.7;
    font-size: 0.85rem;
    line-height: 1.2;
}

/* أيقونة السهم والدخول */
.mobile-user-icon,
.mobile-user-arrow {
    color: var(--navbar-text);
    opacity: 0.6;
    font-size: 1rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.mobile-user-arrow.rotated {
    transform: rotate(180deg);
}

/* القائمة المنسدلة للمستخدم */
.mobile-user-dropdown {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: rgba(79, 70, 229, 0.02);
    border-top: 1px solid transparent;
}

.mobile-user-dropdown.open {
    max-height: 300px;
    border-top-color: var(--navbar-border);
}

/* روابط قائمة المستخدم */

.mobile-user-link {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem 1rem;
    color: var(--navbar-text);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    border: 1px solid transparent;
}

.mobile-user-link:hover {
    background: rgba(79, 70, 229, 0.1);
    border-color: rgba(79, 70, 229, 0.2);
    color: var(--navbar-text-hover);
    transform: translateX(5px);
}

.mobile-user-link.logout {
    color: #ef4444;
    border-color: rgba(239, 68, 68, 0.2);
}

.mobile-user-link.logout:hover {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #dc2626;
}

.mobile-user-link i {
    width: 16px;
    text-align: center;
    opacity: 0.8;
}

/* فاصل القائمة */
.mobile-menu-divider {
    height: 1px;
    background: var(--navbar-border);
    margin: 0 1rem;
}

/* تأثير الخلفية للقائمة المحمولة */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    visibility: hidden;
    transition: var(--navbar-transition);
    z-index: 999;
    backdrop-filter: blur(2px);
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* تعديل المحتوى ليبدأ بعد الشريط */
body {
    padding-top: 70px;
}

/* إخفاء الشريط القديم */
.header,
.nav,
.nav-menu,
.nav-controls,
.theme-toggle:not(.theme-btn),
.mobile-menu-toggle:not(.mobile-toggle) {
    display: none !important;
}

/* تحسينات إضافية */
.modern-navbar * {
    box-sizing: border-box;
}

/* تأثيرات التحميل */
.modern-navbar {
    animation: slideDown 0.5s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* تحسين الخطوط */
.navbar-logo,
.navbar-link,
.mobile-menu-link {
    font-family: 'Tajawal', sans-serif;
}

/* تحسين الأداء */
.modern-navbar,
.mobile-menu,
.mobile-overlay {
    will-change: transform, opacity;
}

/* تحسين إمكانية الوصول */
.navbar-link:focus,
.mobile-menu-link:focus,
.theme-btn:focus,
.mobile-toggle:focus {
    outline: 2px solid var(--navbar-text-hover);
    outline-offset: 2px;
}

/* تحسين للطباعة */
@media print {
    .modern-navbar,
    .mobile-menu,
    .mobile-overlay {
        display: none !important;
    }

    body {
        padding-top: 0 !important;
    }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
    .navbar-container {
        padding: 0 3rem 0 3rem;
    }
}

@media (min-width: 1200px) and (max-width: 1399px) {
    .navbar-container {
        padding: 0 2.5rem 0 2.5rem;
    }
}

/* تحسينات للشاشات المختلفة */
@media (max-width: 1024px) {
    .navbar-container {
        padding: 0 1.5rem 0 1.5rem;
    }

    .navbar-menu {
        gap: 1.5rem;
    }

    .navbar-link {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }
}

@media (max-width: 768px) {
    .navbar-menu {
        display: none !important;
    }

    .mobile-toggle {
        display: flex !important;
        z-index: 1002 !important;
        position: relative !important;
        background: rgba(79, 70, 229, 0.2) !important;
        border: 2px solid rgba(79, 70, 229, 0.4) !important;
    }

    .mobile-toggle:hover {
        background: rgba(79, 70, 229, 0.3) !important;
        border-color: rgba(79, 70, 229, 0.6) !important;
        transform: scale(1.05);
    }

    /* إخفاء نظام المستخدم من الناف بار في النسخة المحمولة */
    .user-system {
        display: none !important;
    }

    .navbar-container {
        padding: 0 1.2rem 0 1.2rem;
    }

    .navbar-logo {
        font-size: 1.6rem;
    }

    /* تحسين نظام المستخدم للشاشات الصغيرة */
    .user-info {
        display: none;
    }

    .user-toggle {
        padding: 0.5rem;
        min-width: auto;
    }

    .user-dropdown {
        right: -10px;
        min-width: 180px;
    }

    .login-btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }

    /* التأكد من ظهور القائمة المحمولة */
    .mobile-menu {
        display: block;
        z-index: 1000;
    }

    .mobile-overlay {
        display: block;
        z-index: 999;
    }
}

@media (max-width: 480px) {
    .navbar-container {
        padding: 0 1rem 0 1rem;
    }

    .navbar-logo {
        font-size: 1.4rem;
    }

    .theme-btn,
    .mobile-toggle {
        width: 40px;
        height: 40px;
    }

    .mobile-toggle {
        display: flex !important;
        background: rgba(79, 70, 229, 0.25) !important;
        border: 2px solid rgba(79, 70, 229, 0.5) !important;
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3) !important;
    }

    .mobile-toggle:hover {
        background: rgba(79, 70, 229, 0.35) !important;
        border-color: rgba(79, 70, 229, 0.7) !important;
        transform: scale(1.1) !important;
    }

    .mobile-toggle span {
        background: var(--navbar-text) !important;
        width: 22px !important;
        height: 3px !important;
    }

    .mobile-menu-link {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
        margin: 0 0.5rem;
    }

    /* تحسين قسم المستخدم للشاشات الصغيرة */
    .mobile-user-section {
        padding: 1rem;
    }

    .mobile-user-avatar {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .mobile-user-info h3 {
        font-size: 1rem;
    }

    .mobile-user-info p {
        font-size: 0.85rem;
    }

    .mobile-login-btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.9rem;
    }

    .mobile-user-link {
        padding: 0.7rem 0.8rem;
        font-size: 0.85rem;
    }

    .navbar-actions {
        gap: 0.5rem;
    }

    /* تحسين القائمة للشاشات الصغيرة جداً */
    .mobile-menu {
        width: 260px;
        max-width: 90vw;
    }
}

/* تأكيد إضافي لإظهار زر القائمة المحمولة */
@media screen and (max-width: 768px) {
    .mobile-toggle {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
}

@media screen and (max-width: 480px) {
    .mobile-toggle {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        background: #4f46e5 !important;
        color: white !important;
    }

    .mobile-toggle span {
        background: white !important;
    }
}

/* تحسين إضافي للشاشات الصغيرة جداً */
@media screen and (max-width: 320px) {
    .mobile-menu {
        width: 240px;
        max-width: 95vw;
    }

    .mobile-user-toggle {
        padding: 0.8rem 1rem;
    }

    .mobile-user-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .mobile-user-name {
        font-size: 0.9rem;
    }

    .mobile-user-status {
        font-size: 0.8rem;
    }

    .mobile-menu-link {
        padding: 0.7rem 1.2rem;
        font-size: 0.9rem;
    }
}
