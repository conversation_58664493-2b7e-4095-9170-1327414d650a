/* ===== شريط التنقل الجديد - متوافق مع جميع الشاشات ===== */

/* متغيرات الألوان */
:root {
    --navbar-bg: rgba(17, 24, 39, 0.95);
    --navbar-bg-solid: #111827;
    --navbar-text: #f9fafb;
    --navbar-text-hover: #4f46e5;
    --navbar-border: rgba(75, 85, 99, 0.3);
    --navbar-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    --navbar-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* متغيرات الثيم الفاتح */
[data-theme="light"] {
    --navbar-bg: rgba(255, 255, 255, 0.95);
    --navbar-bg-solid: #ffffff;
    --navbar-text: #1f2937;
    --navbar-text-hover: #4f46e5;
    --navbar-border: rgba(229, 231, 235, 0.8);
    --navbar-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* متغيرات الثيم الداكن */
[data-theme="dark"] {
    --navbar-bg: rgba(17, 24, 39, 0.95);
    --navbar-bg-solid: #111827;
    --navbar-text: #f9fafb;
    --navbar-text-hover: #4f46e5;
    --navbar-border: rgba(75, 85, 99, 0.3);
    --navbar-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* الشريط الأساسي */
.modern-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--navbar-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--navbar-border);
    box-shadow: var(--navbar-shadow);
    transition: var(--navbar-transition);
    height: 70px;
}

/* عند التمرير */
.modern-navbar.scrolled {
    background: var(--navbar-bg-solid);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
}

/* الحاوي الداخلي */
.navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* الشعار */
.navbar-logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--navbar-text);
    text-decoration: none;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: var(--navbar-transition);
}

.navbar-logo:hover {
    transform: scale(1.05);
}

/* القائمة الرئيسية */
.navbar-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2rem;
}

.navbar-item {
    position: relative;
}

.navbar-link {
    color: var(--navbar-text);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: var(--navbar-transition);
    position: relative;
    overflow: hidden;
}

.navbar-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);
    transition: left 0.5s ease;
}

.navbar-link:hover::before {
    left: 100%;
}

.navbar-link:hover {
    color: var(--navbar-text-hover);
    background: rgba(79, 70, 229, 0.1);
    transform: translateY(-2px);
}

/* الأزرار الجانبية */
.navbar-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* زر تبديل الثيم */
.theme-btn {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 12px;
    background: rgba(79, 70, 229, 0.1);
    color: var(--navbar-text);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: var(--navbar-transition);
    position: relative;
    overflow: hidden;
}

.theme-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(79, 70, 229, 0.2);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

.theme-btn:hover::before {
    width: 100px;
    height: 100px;
}

.theme-btn:hover {
    background: rgba(79, 70, 229, 0.2);
    transform: rotate(180deg);
}

/* زر القائمة المحمولة */
.mobile-toggle {
    display: none;
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 12px;
    background: rgba(79, 70, 229, 0.1);
    color: var(--navbar-text);
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: var(--navbar-transition);
}

.mobile-toggle span {
    width: 20px;
    height: 2px;
    background: var(--navbar-text);
    border-radius: 2px;
    transition: var(--navbar-transition);
}

.mobile-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* القائمة المحمولة */
.mobile-menu {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: var(--navbar-bg-solid);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--navbar-border);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: var(--navbar-transition);
    max-height: calc(100vh - 70px);
    overflow-y: auto;
}

.mobile-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.mobile-menu-list {
    list-style: none;
    margin: 0;
    padding: 2rem 0;
}

.mobile-menu-item {
    margin: 0;
}

.mobile-menu-link {
    display: block;
    color: var(--navbar-text);
    text-decoration: none;
    font-weight: 500;
    font-size: 1.1rem;
    padding: 1rem 2rem;
    border-bottom: 1px solid rgba(75, 85, 99, 0.2);
    transition: var(--navbar-transition);
    position: relative;
}

.mobile-menu-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.mobile-menu-link:hover::before {
    transform: scaleY(1);
}

.mobile-menu-link:hover {
    background: rgba(79, 70, 229, 0.1);
    color: var(--navbar-text-hover);
    padding-right: 3rem;
}

/* تأثير الخلفية للقائمة المحمولة */
.mobile-overlay {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: var(--navbar-transition);
    z-index: 999;
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* تعديل المحتوى ليبدأ بعد الشريط */
body {
    padding-top: 70px;
}

/* إخفاء الشريط القديم */
.header,
.nav,
.nav-menu,
.nav-controls,
.theme-toggle:not(.theme-btn),
.mobile-menu-toggle:not(.mobile-toggle) {
    display: none !important;
}

/* تحسينات إضافية */
.modern-navbar * {
    box-sizing: border-box;
}

/* تأثيرات التحميل */
.modern-navbar {
    animation: slideDown 0.5s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* تحسين الخطوط */
.navbar-logo,
.navbar-link,
.mobile-menu-link {
    font-family: 'Tajawal', sans-serif;
}

/* تحسين الأداء */
.modern-navbar,
.mobile-menu,
.mobile-overlay {
    will-change: transform, opacity;
}

/* تحسين إمكانية الوصول */
.navbar-link:focus,
.mobile-menu-link:focus,
.theme-btn:focus,
.mobile-toggle:focus {
    outline: 2px solid var(--navbar-text-hover);
    outline-offset: 2px;
}

/* تحسين للطباعة */
@media print {
    .modern-navbar,
    .mobile-menu,
    .mobile-overlay {
        display: none !important;
    }

    body {
        padding-top: 0 !important;
    }
}

/* تحسينات للشاشات المختلفة */
@media (max-width: 1024px) {
    .navbar-container {
        padding: 0 15px;
    }
    
    .navbar-menu {
        gap: 1.5rem;
    }
    
    .navbar-link {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }
}

@media (max-width: 768px) {
    .navbar-menu {
        display: none;
    }
    
    .mobile-toggle {
        display: flex;
    }
    
    .navbar-container {
        padding: 0 15px;
    }
    
    .navbar-logo {
        font-size: 1.6rem;
    }
}

@media (max-width: 480px) {
    .navbar-container {
        padding: 0 10px;
    }
    
    .navbar-logo {
        font-size: 1.4rem;
    }
    
    .theme-btn,
    .mobile-toggle {
        width: 40px;
        height: 40px;
    }
    
    .mobile-menu-link {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
}
