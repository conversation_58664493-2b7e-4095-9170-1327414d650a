<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاح</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .success { color: green; }
        .error { color: red; }
        button { padding: 10px 20px; margin: 5px; background: #4f46e5; color: white; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>اختبار الإصلاح</h1>
    
    <button onclick="testAll()">اختبار جميع المكونات</button>
    
    <div id="results"></div>

    <script>
        async function testAll() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>جاري الاختبار...</h2>';
            
            let results = '';
            
            // اختبار 1: الملف مباشرة
            try {
                const response = await fetch('data/courses.json');
                const data = await response.json();
                results += `<p class="success">✅ ملف courses.json: ${data.length} دورة</p>`;
            } catch (error) {
                results += `<p class="error">❌ ملف courses.json: ${error.message}</p>`;
            }
            
            // اختبار 2: get-data.php
            try {
                const response = await fetch('get-data.php?type=courses');
                const result = await response.json();
                if (result.success && result.data.length > 0) {
                    results += `<p class="success">✅ get-data.php: ${result.data.length} دورة</p>`;
                } else {
                    results += `<p class="error">❌ get-data.php: ${result.data.length} دورة</p>`;
                }
            } catch (error) {
                results += `<p class="error">❌ get-data.php: ${error.message}</p>`;
            }
            
            // اختبار 3: api.php
            try {
                const response = await fetch('api.php?section=courses');
                const result = await response.json();
                if (result.success && result.data.length > 0) {
                    results += `<p class="success">✅ api.php: ${result.data.length} دورة</p>`;
                } else {
                    results += `<p class="error">❌ api.php: ${result.data.length} دورة</p>`;
                }
            } catch (error) {
                results += `<p class="error">❌ api.php: ${error.message}</p>`;
            }
            
            resultsDiv.innerHTML = '<h2>نتائج الاختبار:</h2>' + results;
        }
        
        // تشغيل تلقائي
        document.addEventListener('DOMContentLoaded', testAll);
    </script>
</body>
</html>
