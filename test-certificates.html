<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الشهادات</title>
    <link rel="stylesheet" href="admin.css">
    <style>
        body { padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        button { padding: 10px 20px; margin: 5px; background: #4f46e5; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .certificates-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 20px 0; }
        .certificate-card { border: 1px solid #ddd; padding: 15px; border-radius: 10px; background: white; }
        .certificate-image img { max-width: 100%; height: auto; border-radius: 5px; }
        .certificate-info h3 { margin: 10px 0; color: #333; }
        .certificate-issuer { color: #666; font-weight: 500; }
        .certificate-date { background: #4f46e5; color: white; padding: 5px 10px; border-radius: 15px; display: inline-block; font-size: 0.9rem; }
    </style>
</head>
<body>
    <h1>🏆 اختبار نظام الشهادات</h1>
    
    <div class="test-section">
        <h2>1. اختبار تحميل البيانات</h2>
        <button onclick="testDataLoad()">اختبار تحميل البيانات</button>
        <div id="data-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. عرض الشهادات</h2>
        <div class="certificates-grid" id="certificatesGrid">
            <!-- سيتم عرض الشهادات هنا -->
        </div>
    </div>
    
    <div class="test-section">
        <h2>3. اختبار صفحة الإدارة</h2>
        <p><a href="admin-certificates.html" target="_blank">فتح صفحة إدارة الشهادات</a></p>
        <p><a href="index.html#certificates" target="_blank">فتح قسم الشهادات في الصفحة الرئيسية</a></p>
    </div>

    <script>
        async function testDataLoad() {
            const resultDiv = document.getElementById('data-result');
            resultDiv.innerHTML = '<p>جاري الاختبار...</p>';
            
            try {
                // اختبار 1: ملف JSON مباشرة
                const directResponse = await fetch('data/certificates.json');
                const directData = await directResponse.json();
                
                // اختبار 2: get-data.php
                const getDataResponse = await fetch('get-data.php?type=certificates');
                const getDataResult = await getDataResponse.json();
                
                // اختبار 3: api.php
                const apiResponse = await fetch('api.php?section=certificates');
                const apiResult = await apiResponse.json();
                
                let results = '';
                
                if (directData.length > 0) {
                    results += `<p class="success">✅ ملف JSON: ${directData.length} شهادة</p>`;
                } else {
                    results += `<p class="error">❌ ملف JSON: فارغ</p>`;
                }
                
                if (getDataResult.success && getDataResult.data.length > 0) {
                    results += `<p class="success">✅ get-data.php: ${getDataResult.data.length} شهادة</p>`;
                } else {
                    results += `<p class="error">❌ get-data.php: ${getDataResult.data?.length || 0} شهادة</p>`;
                }
                
                if (apiResult.success && apiResult.data.length > 0) {
                    results += `<p class="success">✅ api.php: ${apiResult.data.length} شهادة</p>`;
                    
                    // عرض الشهادات
                    displayCertificates(apiResult.data);
                } else {
                    results += `<p class="error">❌ api.php: ${apiResult.data?.length || 0} شهادة</p>`;
                }
                
                resultDiv.innerHTML = results;
                
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ: ${error.message}</p>`;
            }
        }
        
        function displayCertificates(certificates) {
            const container = document.getElementById('certificatesGrid');
            container.innerHTML = '';
            
            if (certificates.length === 0) {
                container.innerHTML = '<p>لا توجد شهادات لعرضها</p>';
                return;
            }
            
            certificates.forEach(certificate => {
                const certDiv = document.createElement('div');
                certDiv.className = 'certificate-card';
                
                const issueYear = new Date(certificate.issue_date).getFullYear();
                
                certDiv.innerHTML = `
                    <div class="certificate-image">
                        <img src="${certificate.image || 'photo/7.jpg'}" alt="${certificate.title}" onerror="this.src='photo/7.jpg'">
                    </div>
                    <div class="certificate-info">
                        <h3>${certificate.title}</h3>
                        <p class="certificate-issuer">${certificate.provider}</p>
                        <p class="certificate-date">${issueYear}</p>
                        <p style="margin-top: 10px; color: #666; font-size: 0.9rem;">${certificate.description}</p>
                        ${certificate.skills ? `<p style="margin-top: 5px; color: #888; font-size: 0.8rem;">المهارات: ${Array.isArray(certificate.skills) ? certificate.skills.join(', ') : certificate.skills}</p>` : ''}
                    </div>
                `;
                container.appendChild(certDiv);
            });
        }
        
        // تحميل تلقائي
        document.addEventListener('DOMContentLoaded', testDataLoad);
    </script>
</body>
</html>
