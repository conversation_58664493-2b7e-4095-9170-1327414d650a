<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع الصور</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; }
        input, textarea { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
        .result { margin-top: 20px; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🧪 اختبار رفع الصور</h1>
    
    <form id="uploadForm">
        <div class="form-group">
            <label>عنوان الصورة:</label>
            <input type="text" id="title" value="صورة تجريبية" required>
        </div>
        
        <div class="form-group">
            <label>وصف الصورة:</label>
            <textarea id="description">وصف تجريبي للصورة</textarea>
        </div>
        
        <div class="form-group">
            <label>اختر الصورة:</label>
            <input type="file" id="imageFile" accept="image/*" required>
        </div>
        
        <button type="submit">رفع الصورة</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            const title = document.getElementById('title').value;
            const description = document.getElementById('description').value;
            const file = document.getElementById('imageFile').files[0];
            
            if (!file) {
                resultDiv.innerHTML = '<div class="result error">يرجى اختيار صورة</div>';
                return;
            }
            
            const formData = new FormData();
            formData.append('image', file);
            formData.append('title', title);
            formData.append('description', description);
            
            try {
                resultDiv.innerHTML = '<div class="result">جاري رفع الصورة...</div>';
                
                const response = await fetch('upload.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ تم رفع الصورة بنجاح!</h3>
                            <p><strong>الرسالة:</strong> ${result.message}</p>
                            <p><strong>اسم الملف:</strong> ${result.data.filename}</p>
                            <p><strong>الرابط:</strong> ${result.url}</p>
                            <img src="${result.url}" style="max-width: 200px; margin-top: 10px;">
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ خطأ: ${result.message}</div>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ خطأ في الشبكة: ${error.message}</div>`;
            }
        });
    </script>
</body>
</html>
