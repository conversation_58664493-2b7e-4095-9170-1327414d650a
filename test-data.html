<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البيانات</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>اختبار تحميل البيانات</h1>
    
    <div class="section">
        <h2>المهارات</h2>
        <div id="skills-test"></div>
    </div>
    
    <div class="section">
        <h2>الخبرات</h2>
        <div id="experiences-test"></div>
    </div>
    
    <div class="section">
        <h2>الملف الشخصي</h2>
        <div id="profile-test"></div>
    </div>

    <script>
        async function testData() {
            // اختبار المهارات
            try {
                const skillsResponse = await fetch('get-data.php?type=skills');
                const skillsResult = await skillsResponse.json();
                document.getElementById('skills-test').innerHTML = 
                    skillsResult.success ? 
                    `<span class="success">✅ تم تحميل ${skillsResult.data.length} مهارة</span>` :
                    `<span class="error">❌ خطأ: ${skillsResult.message}</span>`;
            } catch (error) {
                document.getElementById('skills-test').innerHTML = 
                    `<span class="error">❌ خطأ في الاتصال: ${error.message}</span>`;
            }
            
            // اختبار الخبرات
            try {
                const expResponse = await fetch('get-data.php?type=experiences');
                const expResult = await expResponse.json();
                document.getElementById('experiences-test').innerHTML = 
                    expResult.success ? 
                    `<span class="success">✅ تم تحميل ${expResult.data.length} خبرة</span>` :
                    `<span class="error">❌ خطأ: ${expResult.message}</span>`;
            } catch (error) {
                document.getElementById('experiences-test').innerHTML = 
                    `<span class="error">❌ خطأ في الاتصال: ${error.message}</span>`;
            }
            
            // اختبار الملف الشخصي
            try {
                const profileResponse = await fetch('get-data.php?type=profile');
                const profileResult = await profileResponse.json();
                document.getElementById('profile-test').innerHTML = 
                    profileResult.success ? 
                    `<span class="success">✅ تم تحميل الملف الشخصي</span>` :
                    `<span class="error">❌ خطأ: ${profileResult.message}</span>`;
            } catch (error) {
                document.getElementById('profile-test').innerHTML = 
                    `<span class="error">❌ خطأ في الاتصال: ${error.message}</span>`;
            }
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', testData);
    </script>
</body>
</html>
