<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#4F46E5">
    <title>طارق محمد الشتيوي - السيرة الذاتية</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="navbar.css">
    <link rel="stylesheet" href="login-modal.css">
</head>
<body>
    <!-- شريط التنقل الجديد -->
    <nav class="modern-navbar">
        <div class="navbar-container">
            <!-- الشعار -->
            <a href="#home" class="navbar-logo">طارق الشتيوي</a>

            <!-- القائمة الرئيسية -->
            <ul class="navbar-menu">
                <li class="navbar-item">
                    <a href="#home" class="navbar-link">
                        <i class="fas fa-home"></i>
                        <span>الرئيسية</span>
                    </a>
                </li>
                <li class="navbar-item">
                    <a href="#about" class="navbar-link">
                        <i class="fas fa-user"></i>
                        <span>نبذة عني</span>
                    </a>
                </li>
                <li class="navbar-item">
                    <a href="#skills" class="navbar-link">
                        <i class="fas fa-code"></i>
                        <span>المهارات التقنية</span>
                    </a>
                </li>
                <li class="navbar-item">
                    <a href="#personal-skills" class="navbar-link">
                        <i class="fas fa-lightbulb"></i>
                        <span>المهارات الشخصية</span>
                    </a>
                </li>
                <li class="navbar-item">
                    <a href="#experience" class="navbar-link">
                        <i class="fas fa-briefcase"></i>
                        <span>الخبرات</span>
                    </a>
                </li>
                <li class="navbar-item">
                    <a href="#portfolio" class="navbar-link">
                        <i class="fas fa-project-diagram"></i>
                        <span>أعمالي</span>
                    </a>
                </li>
                <li class="navbar-item">
                    <a href="#courses" class="navbar-link">
                        <i class="fas fa-graduation-cap"></i>
                        <span>الدورات</span>
                    </a>
                </li>
                <li class="navbar-item">
                    <a href="#certificates" class="navbar-link">
                        <i class="fas fa-certificate"></i>
                        <span>الشهادات</span>
                    </a>
                </li>
            </ul>

            <!-- الأزرار الجانبية -->
            <div class="navbar-actions">
                <!-- نظام المستخدم -->
                <div class="user-system">
                    <!-- زر تسجيل الدخول (يظهر عند عدم تسجيل الدخول) -->
                    <a href="#" class="login-btn" id="loginBtn">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>تسجيل الدخول</span>
                    </a>

                    <!-- قائمة المستخدم (تظهر بعد تسجيل الدخول) -->
                    <div class="user-menu" id="userMenu">
                        <div class="user-toggle" id="userToggle" onclick="toggleUserDropdownDirect()">
                            <div class="user-avatar" id="userAvatar">ط</div>
                            <div class="user-info">
                                <div class="user-name" id="userName">طارق الشتيوي</div>
                                <div class="user-role">مدير</div>
                            </div>
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </div>

                        <!-- القائمة المنسدلة -->
                        <div class="user-dropdown" id="userDropdown">
                            <div class="dropdown-header">
                                <div class="dropdown-user-avatar" id="dropdownAvatar">ط</div>
                                <div class="dropdown-user-name" id="dropdownUserName">طارق الشتيوي</div>
                                <div class="dropdown-user-email" id="dropdownUserEmail"><EMAIL></div>
                            </div>

                            <ul class="dropdown-menu">
                                <li class="dropdown-item">
                                    <a href="#" class="dropdown-link" id="profileLink">
                                        <i class="fas fa-user"></i>
                                        <span>الملف الشخصي</span>
                                    </a>
                                </li>
                                <li class="dropdown-item">
                                    <a href="#" class="dropdown-link" onclick="handleAdminAccess()">
                                        <i class="fas fa-cog"></i>
                                        <span>لوحة التحكم</span>
                                    </a>
                                </li>
                                <li class="dropdown-item">
                                    <a href="#" class="dropdown-link">
                                        <i class="fas fa-bell"></i>
                                        <span>الإشعارات</span>
                                    </a>
                                </li>
                                <div class="dropdown-divider"></div>
                                <li class="dropdown-item">
                                    <a href="#" class="dropdown-link logout-link" id="logoutBtn">
                                        <i class="fas fa-sign-out-alt"></i>
                                        <span>تسجيل الخروج</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <button class="theme-btn" id="modernThemeToggle">
                    <i class="fas fa-moon"></i>
                </button>
                <button class="mobile-toggle" id="mobileToggleBtn" onclick="toggleMobileMenuDirect()"
                        style="display: none;">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- القائمة المحمولة الجديدة -->
    <div class="mobile-overlay"></div>
    <div class="mobile-menu">
        <!-- قائمة التنقل -->
        <ul class="mobile-menu-list">
            <li class="mobile-menu-item">
                <a href="#home" class="mobile-menu-link">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            <li class="mobile-menu-item">
                <a href="#about" class="mobile-menu-link">
                    <i class="fas fa-user"></i>
                    <span>نبذة عني</span>
                </a>
            </li>
            <li class="mobile-menu-item">
                <a href="#skills" class="mobile-menu-link">
                    <i class="fas fa-code"></i>
                    <span>المهارات التقنية</span>
                </a>
            </li>
            <li class="mobile-menu-item">
                <a href="#personal-skills" class="mobile-menu-link">
                    <i class="fas fa-lightbulb"></i>
                    <span>المهارات الشخصية</span>
                </a>
            </li>
            <li class="mobile-menu-item">
                <a href="#experience" class="mobile-menu-link">
                    <i class="fas fa-briefcase"></i>
                    <span>الخبرات</span>
                </a>
            </li>
            <li class="mobile-menu-item">
                <a href="#portfolio" class="mobile-menu-link">
                    <i class="fas fa-project-diagram"></i>
                    <span>أعمالي</span>
                </a>
            </li>
            <li class="mobile-menu-item">
                <a href="#courses" class="mobile-menu-link">
                    <i class="fas fa-graduation-cap"></i>
                    <span>الدورات</span>
                </a>
            </li>
            <li class="mobile-menu-item">
                <a href="#certificates" class="mobile-menu-link">
                    <i class="fas fa-certificate"></i>
                    <span>الشهادات</span>
                </a>
            </li>
        </ul>

        <!-- فاصل -->
        <div class="mobile-menu-divider"></div>

        <!-- قسم المستخدم في أسفل القائمة المحمولة -->
        <div class="mobile-user-section" id="mobileUserSection">
            <!-- عندما لا يكون مسجل دخول -->
            <div class="mobile-login-section" id="mobileLoginSection">
                <button class="mobile-user-toggle" onclick="document.getElementById('loginBtn').click()">
                    <div class="mobile-user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="mobile-user-info">
                        <span class="mobile-user-name">تسجيل الدخول</span>
                        <span class="mobile-user-status">انقر للدخول</span>
                    </div>
                    <i class="fas fa-sign-in-alt mobile-user-icon"></i>
                </button>
            </div>

            <!-- عندما يكون مسجل دخول -->
            <div class="mobile-user-logged" id="mobileUserLogged" style="display: none;">
                <!-- زر المستخدم القابل للطي -->
                <button class="mobile-user-toggle" onclick="toggleMobileUserMenu()">
                    <div class="mobile-user-avatar logged" id="mobileUserAvatar">ط</div>
                    <div class="mobile-user-info">
                        <span class="mobile-user-name" id="mobileUserName">طارق الشتيوي</span>
                        <span class="mobile-user-status" id="mobileUserRole">مدير</span>
                    </div>
                    <i class="fas fa-chevron-down mobile-user-arrow" id="mobileUserArrow"></i>
                </button>

                <!-- قائمة خيارات المستخدم المطوية -->
                <div class="mobile-user-dropdown" id="mobileUserDropdown">
                    <a href="#" class="mobile-user-link">
                        <i class="fas fa-user"></i>
                        <span>الملف الشخصي</span>
                    </a>
                    <a href="#" class="mobile-user-link" onclick="handleAdminAccess()">
                        <i class="fas fa-cog"></i>
                        <span>لوحة التحكم</span>
                    </a>
                    <a href="#" class="mobile-user-link">
                        <i class="fas fa-bell"></i>
                        <span>الإشعارات</span>
                    </a>
                    <a href="#" class="mobile-user-link logout" onclick="logoutFromMobile()">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل الخروج</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text" id="hero-text">
                    <h1 id="hero-name">طارق محمد الشتيوي</h1>
                    <p class="subtitle" id="hero-subtitle">شاب هاوٍ ومحب للحاسب الالي</p>
                    <p class="location" id="hero-location"><i class="fas fa-map-marker-alt"></i> القصيم، بريدة</p>

                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span id="hero-email"><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span id="hero-phone">0503839769</span>
                        </div>
                    </div>

                    <div class="cta-buttons">
                        <a href="#about" class="btn btn-primary" id="hero-about-btn">اعرف المزيد</a>
                        <a href="#portfolio" class="btn btn-outline" id="hero-portfolio-btn">أعمالي</a>
                    </div>
                </div>
                
                <div class="hero-image">
                    <div class="profile-card">
                        <div class="profile-avatar">
                            <img src="photo/7.jpg" alt="طارق محمد الشتيوي" class="profile-image" id="hero-profile-image">
                        </div>
                        <h3 id="hero-education">خريج ثانوية صالح العثيم</h3>
                        <div class="gpa-badge" id="hero-gpa">معدل تراكمي: 95%</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Floating Elements -->
        <div class="floating-elements">
            <div class="floating-item" style="top: 20%; left: 10%;">
                <i class="fab fa-python"></i>
            </div>
            <div class="floating-item" style="top: 60%; left: 15%;">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="floating-item" style="top: 30%; right: 10%;">
                <i class="fas fa-code"></i>
            </div>
            <div class="floating-item" style="top: 70%; right: 20%;">
                <i class="fas fa-robot"></i>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="section" id="about">
        <div class="container">
            <h2 class="section-title" id="about-title">نبذة عني</h2>
            <div class="about-content" id="about-content">
                <p id="about-paragraph-1">أتمتع بخلفية تقنية قوية في مجالات متعددة مثل البرمجة، الأمن السيبراني، والصيانة التقنية. بدأت بتطوير مهاراتي من خلال المشاركة في مسابقات BugBounty ودورات متخصصة في البرمجة والروبوت، مما منحني فرصة لتوسيع معرفتي بشكل عملي.</p>

                <p id="about-paragraph-2">أستمتع بتعلم التقنيات الحديثة وأتطلع دائمًا لتطبيق المهارات المكتسبة في مشاريع مبتكرة. بالإضافة إلى ذلك، لدي خبرة في التصميم باستخدام برامج أدوبي والمونتاج الفيديو، وأعتبر نفسي شخصًا يتمتع بمهارات القيادة والتواصل، ولدي القدرة على العمل ضمن فريق وتحمل المسؤولية.</p>
            </div>
        </div>
    </section>

    <!-- Languages Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title" id="languages-title">اللغات</h2>

            <div class="languages" id="languages-container">
                <!-- سيتم تحميل اللغات هنا ديناميكياً -->
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section class="section" id="skills">
        <div class="container">
            <h2 class="section-title">المهارات التقنية</h2>

            <div class="skills-grid" id="skills-container">
                <!-- سيتم تحميل المهارات هنا ديناميكياً -->
            </div>
        </div>
    </section>

    <!-- Experience Section -->
    <section class="section" id="experience">
        <div class="container">
            <h2 class="section-title">الخبرات العملية</h2>

            <div class="timeline" id="experiences-container">
                <!-- سيتم تحميل الخبرات هنا ديناميكياً -->
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section class="section" id="courses">
        <div class="container">
            <h2 class="section-title" id="courses-title">الدورات التدريبية</h2>

            <div class="courses-grid" id="courses-container">
                <!-- سيتم تحميل الدورات هنا ديناميكياً -->
            </div>
        </div>
    </section>

    <!-- Personal Skills Section -->
    <section class="section" id="personal-skills">
        <div class="container">
            <h2 class="section-title">المهارات الشخصية</h2>

            <div class="personal-skills" id="personal-skills-container">
                <!-- سيتم تحميل المهارات الشخصية هنا ديناميكياً -->
            </div>
        </div>
    </section>

    <!-- Certificates Section -->
    <section class="section" id="certificates">
        <div class="container">
            <h2 class="section-title">الشهادات</h2>
            
            <div class="certificates-grid" id="certificatesGrid">
                <!-- سيتم تحميل الشهادات هنا ديناميك -->
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section class="section" id="portfolio">
        <div class="container">
            <h2 class="section-title">أعمالي</h2>

            <div class="portfolio-filters">
                <button class="filter-btn active" data-filter="all">الكل</button>
                <button class="filter-btn" data-filter="image">الصور</button>
                <button class="filter-btn" data-filter="website">المواقع</button>
                <button class="filter-btn" data-filter="youtube">الفيديوهات</button>
            </div>

            <div class="portfolio-grid" id="portfolioGrid">
                <!-- سيتم تحميل الأعمال هنا ديناميكياً من قاعدة البيانات -->
            </div>
        </div>
    </section>


    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2025 طارق محمد الشتيوي. جميع الحقوق محفوظة.</p>
                </div>
                <div class="footer-social" id="footerSocial">
                    <!-- سيتم تحميل روابط التواصل هنا ديناميكياً -->
                </div>
            </div>
        </div>
    </footer>

    <!-- Admin Login Modal -->
    <div id="adminModal" class="admin-modal" style="display: none;">
        <div class="admin-modal-content">
            <span class="close-modal" onclick="closeAdminModal()">&times;</span>
            <h2>تسجيل دخول الإدارة</h2>
            <form id="adminLoginForm">
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="adminUsername" required>
                </div>
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" id="adminPassword" required>
                </div>
                <button type="submit" class="login-btn">دخول</button>
            </form>
        </div>
    </div>

    <!-- نافذة تسجيل الدخول -->
    <div class="login-overlay" id="loginOverlay">
        <div class="login-modal">
            <div class="login-header">
                <button class="login-close" id="loginClose">
                    <i class="fas fa-times"></i>
                </button>
                <h2 class="login-title">تسجيل الدخول</h2>
                <p class="login-subtitle">أدخل بياناتك للوصول إلى لوحة التحكم</p>
            </div>

            <div class="login-content">
                <form class="login-form" id="loginForm">
                    <div class="form-group">
                        <label class="form-label" for="username">اسم المستخدم</label>
                        <input type="text" class="form-input" id="username" name="username" required>
                        <i class="fas fa-user form-icon"></i>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="password">كلمة المرور</label>
                        <input type="password" class="form-input" id="password" name="password" required>
                        <i class="fas fa-lock form-icon"></i>
                    </div>

                    <button type="submit" class="login-submit" id="loginSubmit">
                        <span>تسجيل الدخول</span>
                    </button>

                    <div class="error-message" id="errorMessage"></div>
                    <div class="success-message" id="successMessage"></div>
                </form>
            </div>

            <div class="login-footer">
                <div style="margin-bottom: 1rem; padding: 1rem; background: rgba(79, 70, 229, 0.1); border-radius: 8px; font-size: 0.85rem; color: var(--text-secondary, #6b7280);">
                    <strong>بيانات تجريبية:</strong><br>
                    المدير: admin / admin123<br>
                    المستخدم: tareq / tareq123<br>
                    عربي: مدير / 123456
                </div>
                <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="portfolio-display.js"></script>
    <script src="navbar.js"></script>

    <!-- وظيفة مباشرة للقائمة المحمولة -->
    <script>
        function toggleMobileMenuDirect() {
            const mobileMenu = document.querySelector('.mobile-menu');
            const mobileOverlay = document.querySelector('.mobile-overlay');
            const mobileToggle = document.querySelector('.mobile-toggle');

            if (mobileMenu && mobileOverlay && mobileToggle) {
                const isActive = mobileMenu.classList.contains('active');

                if (isActive) {
                    // إغلاق القائمة المنسدلة للمستخدم أولاً
                    if (typeof closeMobileUserDropdown === 'function') {
                        closeMobileUserDropdown();
                    }

                    // إغلاق القائمة الرئيسية
                    mobileMenu.classList.remove('active');
                    mobileOverlay.classList.remove('active');
                    mobileToggle.classList.remove('active');
                    document.body.style.overflow = 'auto';
                } else {
                    // فتح القائمة
                    mobileMenu.classList.add('active');
                    mobileOverlay.classList.add('active');
                    mobileToggle.classList.add('active');
                    document.body.style.overflow = 'hidden';
                }
            }
        }

        // وظيفة لإظهار/إخفاء زر القائمة المحمولة حسب حجم الشاشة
        function toggleMobileButtonVisibility() {
            const mobileToggle = document.getElementById('mobileToggleBtn');
            if (mobileToggle) {
                if (window.innerWidth <= 768) {
                    mobileToggle.style.display = 'flex';
                    mobileToggle.style.visibility = 'visible';
                    mobileToggle.style.opacity = '1';
                } else {
                    mobileToggle.style.display = 'none';
                }
            }
        }

        // إضافة معالجات إضافية
        document.addEventListener('DOMContentLoaded', function() {
            // إظهار زر القائمة المحمولة فوراً
            toggleMobileButtonVisibility();
            // معالج للإغلاق عند النقر على الخلفية
            const overlay = document.querySelector('.mobile-overlay');
            if (overlay) {
                overlay.addEventListener('click', function() {
                    toggleMobileMenuDirect();
                });
            }

            // معالج للروابط
            const mobileLinks = document.querySelectorAll('.mobile-menu-link');
            mobileLinks.forEach(link => {
                link.addEventListener('click', function() {
                    toggleMobileMenuDirect();
                });
            });

            // إغلاق القائمة عند تغيير حجم الشاشة وإظهار/إخفاء الزر
            window.addEventListener('resize', function() {
                toggleMobileButtonVisibility();

                if (window.innerWidth > 768) {
                    const mobileMenu = document.querySelector('.mobile-menu');
                    const mobileOverlay = document.querySelector('.mobile-overlay');
                    const mobileToggle = document.querySelector('.mobile-toggle');

                    if (mobileMenu) mobileMenu.classList.remove('active');
                    if (mobileOverlay) mobileOverlay.classList.remove('active');
                    if (mobileToggle) mobileToggle.classList.remove('active');
                    document.body.style.overflow = 'auto';

                    // إغلاق القائمة المنسدلة للمستخدم أيضاً
                    closeMobileUserDropdown();
                }
            });

            // إغلاق القائمة بمفتاح Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const mobileMenu = document.querySelector('.mobile-menu');
                    if (mobileMenu && mobileMenu.classList.contains('active')) {
                        toggleMobileMenuDirect();
                    }
                }
            });
        });

        // وظيفة مباشرة للقائمة المنسدلة للمستخدم
        window.toggleUserDropdownDirect = function() {
            const userDropdown = document.getElementById('userDropdown');
            const userToggle = document.getElementById('userToggle');

            if (userDropdown && userToggle) {
                const isActive = userDropdown.classList.contains('active');

                if (isActive) {
                    // إغلاق القائمة
                    userDropdown.classList.remove('active');
                    userToggle.classList.remove('active');
                } else {
                    // فتح القائمة
                    userDropdown.classList.add('active');
                    userToggle.classList.add('active');
                }
            }
        };

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            const userToggle = document.getElementById('userToggle');
            const userDropdown = document.getElementById('userDropdown');

            if (userToggle && userDropdown &&
                !userToggle.contains(e.target) &&
                !userDropdown.contains(e.target)) {
                userDropdown.classList.remove('active');
                userToggle.classList.remove('active');
            }
        });

        // التأكد من ظهور الزر عند تحميل الصفحة
        window.addEventListener('load', function() {
            toggleMobileButtonVisibility();
        });

        // معالج إضافي للتأكد
        setTimeout(function() {
            toggleMobileButtonVisibility();
        }, 500);

        // وظيفة طي/فتح قائمة المستخدم المحمولة
        window.toggleMobileUserMenu = function() {
            const dropdown = document.getElementById('mobileUserDropdown');
            const arrow = document.getElementById('mobileUserArrow');

            if (dropdown && arrow) {
                const isOpen = dropdown.classList.contains('open');

                if (isOpen) {
                    // إغلاق القائمة
                    dropdown.classList.remove('open');
                    arrow.classList.remove('rotated');
                } else {
                    // فتح القائمة
                    dropdown.classList.add('open');
                    arrow.classList.add('rotated');
                }
            }
        };

        // وظيفة تسجيل الخروج من القائمة المحمولة
        window.logoutFromMobile = function() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                // إغلاق القائمة المنسدلة أولاً
                const dropdown = document.getElementById('mobileUserDropdown');
                const arrow = document.getElementById('mobileUserArrow');
                if (dropdown) dropdown.classList.remove('open');
                if (arrow) arrow.classList.remove('rotated');

                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userData');
                updateMobileUserSection();

                // إغلاق القائمة الرئيسية بعد تأخير قصير
                setTimeout(() => {
                    toggleMobileMenuDirect();
                }, 500);

                // إشعار
                showMobileNotification('تم تسجيل الخروج بنجاح', 'info');
            }
        };

        // تحديث قسم المستخدم في القائمة المحمولة
        function updateMobileUserSection() {
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            const userData = JSON.parse(localStorage.getItem('userData') || '{}');

            const mobileLoginSection = document.getElementById('mobileLoginSection');
            const mobileUserLogged = document.getElementById('mobileUserLogged');

            if (isLoggedIn && userData.name) {
                // إخفاء قسم تسجيل الدخول وإظهار قسم المستخدم
                if (mobileLoginSection) mobileLoginSection.style.display = 'none';
                if (mobileUserLogged) mobileUserLogged.style.display = 'block';

                // تحديث بيانات المستخدم
                const mobileUserAvatar = document.getElementById('mobileUserAvatar');
                const mobileUserName = document.getElementById('mobileUserName');
                const mobileUserRole = document.getElementById('mobileUserRole');

                if (mobileUserAvatar) mobileUserAvatar.textContent = userData.name.charAt(0).toUpperCase();
                if (mobileUserName) mobileUserName.textContent = userData.name;
                if (mobileUserRole) mobileUserRole.textContent = userData.role || 'مستخدم';

                // التأكد من إغلاق القائمة المنسدلة
                const dropdown = document.getElementById('mobileUserDropdown');
                const arrow = document.getElementById('mobileUserArrow');
                if (dropdown) dropdown.classList.remove('open');
                if (arrow) arrow.classList.remove('rotated');
            } else {
                // إظهار قسم تسجيل الدخول وإخفاء قسم المستخدم
                if (mobileLoginSection) mobileLoginSection.style.display = 'block';
                if (mobileUserLogged) mobileUserLogged.style.display = 'none';
            }
        }

        // إغلاق القائمة المنسدلة عند النقر على روابط التنقل
        function closeMobileUserDropdown() {
            const dropdown = document.getElementById('mobileUserDropdown');
            const arrow = document.getElementById('mobileUserArrow');
            if (dropdown) dropdown.classList.remove('open');
            if (arrow) arrow.classList.remove('rotated');
        }

        // إشعار للقائمة المحمولة
        function showMobileNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `mobile-notification ${type}`;
            notification.textContent = message;

            Object.assign(notification.style, {
                position: 'fixed',
                top: '90px',
                left: '50%',
                transform: 'translateX(-50%)',
                background: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6',
                color: 'white',
                padding: '1rem 1.5rem',
                borderRadius: '12px',
                boxShadow: '0 10px 25px rgba(0,0,0,0.2)',
                zIndex: '10001',
                fontSize: '0.9rem',
                fontWeight: '500',
                maxWidth: '90%',
                textAlign: 'center'
            });

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // تحديث قسم المستخدم عند تحميل الصفحة
        updateMobileUserSection();

        // مراقبة تغييرات تسجيل الدخول
        window.addEventListener('storage', function(e) {
            if (e.key === 'isLoggedIn' || e.key === 'userData') {
                updateMobileUserSection();
            }
        });
    </script>
</body>
</html>
