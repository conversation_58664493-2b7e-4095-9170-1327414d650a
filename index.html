<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طارق محمد الشتيوي - السيرة الذاتية</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="logo">طارق الشتيوي</div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">الرئيسية</a></li>
                <li><a href="#about" class="nav-link">نبذة عني</a></li>
                <li><a href="#skills" class="nav-link">المهارات التقنية</a></li>
                <li><a href="#personal-skills" class="nav-link">المهارات الشخصية</a></li>
                <li><a href="#experience" class="nav-link">الخبرات</a></li>
                <li><a href="#portfolio" class="nav-link">أعمالي</a></li>
                <li><a href="#courses" class="nav-link">الدورات</a></li>
                <li><a href="#certificates" class="nav-link">الشهادات</a></li>
                <li><a href="#contact" class="nav-link">تواصل</a></li>
            </ul>
            <div class="nav-controls">
                <a href="#" onclick="showAdminLogin()" class="nav-link admin-link">لوحة التحكم</a>
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text" id="hero-text">
                    <h1 id="hero-name">طارق محمد الشتيوي</h1>
                    <p class="subtitle" id="hero-subtitle">شاب متحمس ومحب لتخصص الحاسب الالي</p>
                    <p class="location" id="hero-location"><i class="fas fa-map-marker-alt"></i> القصيم، بريدة</p>

                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span id="hero-email"><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span id="hero-phone">0503839769</span>
                        </div>
                    </div>

                    <div class="cta-buttons">
                        <a href="#contact" class="btn btn-primary" id="hero-contact-btn">تواصل معي</a>
                        <a href="#about" class="btn btn-outline" id="hero-about-btn">اعرف المزيد</a>
                    </div>
                </div>
                
                <div class="hero-image">
                    <div class="profile-card">
                        <div class="profile-avatar">
                            <img src="photo/7.jpg" alt="طارق محمد الشتيوي" class="profile-image" id="hero-profile-image">
                        </div>
                        <h3 id="hero-education">خريج ثانوية صالح العثيم</h3>
                        <div class="gpa-badge" id="hero-gpa">معدل تراكمي: 95%</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Floating Elements -->
        <div class="floating-elements">
            <div class="floating-item" style="top: 20%; left: 10%;">
                <i class="fab fa-python"></i>
            </div>
            <div class="floating-item" style="top: 60%; left: 15%;">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="floating-item" style="top: 30%; right: 10%;">
                <i class="fas fa-code"></i>
            </div>
            <div class="floating-item" style="top: 70%; right: 20%;">
                <i class="fas fa-robot"></i>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="section" id="about">
        <div class="container">
            <h2 class="section-title" id="about-title">نبذة عني</h2>
            <div class="about-content" id="about-content">
                <p id="about-paragraph-1">أتمتع بخلفية تقنية قوية في مجالات متعددة مثل البرمجة، الأمن السيبراني، والصيانة التقنية. بدأت بتطوير مهاراتي من خلال المشاركة في مسابقات BugBounty ودورات متخصصة في البرمجة والروبوت، مما منحني فرصة لتوسيع معرفتي بشكل عملي.</p>

                <p id="about-paragraph-2">أستمتع بتعلم التقنيات الحديثة وأتطلع دائمًا لتطبيق المهارات المكتسبة في مشاريع مبتكرة. بالإضافة إلى ذلك، لدي خبرة في التصميم باستخدام برامج أدوبي والمونتاج الفيديو، وأعتبر نفسي شخصًا يتمتع بمهارات القيادة والتواصل، ولدي القدرة على العمل ضمن فريق وتحمل المسؤولية.</p>
            </div>
        </div>
    </section>

    <!-- Languages Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title" id="languages-title">اللغات</h2>

            <div class="languages" id="languages-container">
                <!-- سيتم تحميل اللغات هنا ديناميكياً -->
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section class="section" id="skills">
        <div class="container">
            <h2 class="section-title">المهارات التقنية</h2>

            <div class="skills-grid" id="skills-container">
                <!-- سيتم تحميل المهارات هنا ديناميكياً -->
            </div>
        </div>
    </section>

    <!-- Experience Section -->
    <section class="section" id="experience">
        <div class="container">
            <h2 class="section-title">الخبرات العملية</h2>

            <div class="timeline" id="experiences-container">
                <!-- سيتم تحميل الخبرات هنا ديناميكياً -->
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section class="section" id="courses">
        <div class="container">
            <h2 class="section-title" id="courses-title">الدورات التدريبية</h2>

            <div class="courses-grid" id="courses-container">
                <!-- سيتم تحميل الدورات هنا ديناميكياً -->
            </div>
        </div>
    </section>

    <!-- Personal Skills Section -->
    <section class="section" id="personal-skills">
        <div class="container">
            <h2 class="section-title">المهارات الشخصية</h2>

            <div class="personal-skills" id="personal-skills-container">
                <!-- سيتم تحميل المهارات الشخصية هنا ديناميكياً -->
            </div>
        </div>
    </section>

    <!-- Certificates Section -->
    <section class="section" id="certificates">
        <div class="container">
            <h2 class="section-title">الشهادات</h2>
            
            <div class="certificates-grid" id="certificatesGrid">
                <!-- سيتم تحميل الشهادات هنا ديناميك -->
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section class="section" id="portfolio">
        <div class="container">
            <h2 class="section-title">أعمالي</h2>

            <div class="portfolio-filters">
                <button class="filter-btn active" data-filter="all">الكل</button>
                <button class="filter-btn" data-filter="website">المواقع</button>
                <button class="filter-btn" data-filter="image">الصور</button>
            </div>

            <div class="portfolio-grid" id="portfolio-container">
                <!-- سيتم تحميل الأعمال هنا ديناميكياً من قاعدة البيانات -->
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="section" id="contact">
        <div class="container">
            <h2 class="section-title">تواصل معي</h2>
            
            <div class="contact-content">
                <div class="contact-info-grid">
                    <div class="contact-card">
                        <i class="fas fa-envelope"></i>
                        <h3>البريد الإلكتروني</h3>
                        <p><EMAIL></p>
                    </div>
                    
                    <div class="contact-card">
                        <i class="fas fa-phone"></i>
                        <h3>رقم الهاتف</h3>
                        <p>0503839769</p>
                    </div>
                    
                    <div class="contact-card">
                        <i class="fas fa-map-marker-alt"></i>
                        <h3>الموقع</h3>
                        <p>القصيم، بريدة</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2025 طارق محمد الشتيوي. جميع الحقوق محفوظة.</p>
                </div>
                <div class="footer-social">
                    <a href="mailto:<EMAIL>" class="social-link" title="البريد الإلكتروني">
                        <i class="fas fa-envelope"></i>
                    </a>
                    <a href="tel:0503839769" class="social-link" title="رقم الهاتف">
                        <i class="fas fa-phone"></i>
                    </a>
                    <a href="https://github.com/TareqAlshetaiwi" class="social-link" title="GitHub" target="_blank">
                        <i class="fab fa-github"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Admin Login Modal -->
    <div id="adminModal" class="admin-modal" style="display: none;">
        <div class="admin-modal-content">
            <span class="close-modal" onclick="closeAdminModal()">&times;</span>
            <h2>تسجيل دخول الإدارة</h2>
            <form id="adminLoginForm">
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="adminUsername" required>
                </div>
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" id="adminPassword" required>
                </div>
                <button type="submit" class="login-btn">دخول</button>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="portfolio-display.js"></script>
</body>
</html>
