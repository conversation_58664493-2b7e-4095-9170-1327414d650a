<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طارق محمد الشتيوي - السيرة الذاتية</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="logo">طارق الشتيوي</div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">الرئيسية</a></li>
                <li><a href="#about" class="nav-link">نبذة عني</a></li>
                <li><a href="#skills" class="nav-link">المهارات</a></li>
                <li><a href="#experience" class="nav-link">الخبرات</a></li>
                <li><a href="#portfolio" class="nav-link">أعمالي</a></li>
                <li><a href="#courses" class="nav-link">الدورات</a></li>
                <li><a href="#certificates" class="nav-link">الشهادات</a></li>
                <li><a href="#contact" class="nav-link">تواصل</a></li>
                <li><a href="#" onclick="showAdminLogin()" class="nav-link admin-link">لوحة التحكم</a></li>
            </ul>
            <button class="theme-toggle" id="themeToggle">
                <i class="fas fa-moon"></i>
            </button>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>طارق محمد الشتيوي</h1>
                    <p class="subtitle"> شاب متحمس ومحب لتخصص الحاسب الالي</p>
                    <p class="location"><i class="fas fa-map-marker-alt"></i> القصيم، بريدة</p>
                    
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>0503839769</span>
                        </div>
                    </div>
                    
                    <div class="cta-buttons">
                        <a href="#contact" class="btn btn-primary">تواصل معي</a>
                        <a href="#about" class="btn btn-outline">اعرف المزيد</a>
                    </div>
                </div>
                
                <div class="hero-image">
                    <div class="profile-card">
                        <div class="profile-avatar">
                            <img src="photo/7.jpg" alt="طارق محمد الشتيوي" class="profile-image">
                        </div>
                        <h3>خريج ثانوية صالح العثيم</h3>
                        <div class="gpa-badge">معدل تراكمي: 95%</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Floating Elements -->
        <div class="floating-elements">
            <div class="floating-item" style="top: 20%; left: 10%;">
                <i class="fab fa-python"></i>
            </div>
            <div class="floating-item" style="top: 60%; left: 15%;">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="floating-item" style="top: 30%; right: 10%;">
                <i class="fas fa-code"></i>
            </div>
            <div class="floating-item" style="top: 70%; right: 20%;">
                <i class="fas fa-robot"></i>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="section" id="about">
        <div class="container">
            <h2 class="section-title">نبذة عني</h2>
            <div class="about-content">
                <p> أتمتع بخلفية تقنية قوية في مجالات متعددة مثل البرمجة، الأمن السيبراني، والصيانة التقنية. بدأت بتطوير مهاراتي من خلال المشاركة في مسابقات BugBounty ودورات متخصصة في البرمجة والروبوت، مما منحني فرصة لتوسيع معرفتي بشكل عملي.</p>
                
                <p>أستمتع بتعلم التقنيات الحديثة وأتطلع دائمًا لتطبيق المهارات المكتسبة في مشاريع مبتكرة. بالإضافة إلى ذلك، لدي خبرة في التصميم باستخدام برامج أدوبي والمونتاج الفيديو، وأعتبر نفسي شخصًا يتمتع بمهارات القيادة والتواصل، ولدي القدرة على العمل ضمن فريق وتحمل المسؤولية.</p>
            </div>
        </div>
    </section>

    <!-- Languages Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">اللغات</h2>
            
            <div class="languages">
                <div class="language-item">
                    <h3>العربية</h3>
                    <p>اللغة الأم</p>
                    <div class="progress-bar">
                        <div class="progress" style="width: 100%"></div>
                    </div>
                </div>
                
                <div class="language-item">
                    <h3>الإنجليزية</h3>
                    <p>مستوى متوسط</p>
                    <div class="progress-bar">
                        <div class="progress" style="width: 40%"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section class="section" id="skills">
        <div class="container">
            <h2 class="section-title">المهارات التقنية</h2>
            
            <div class="skills-grid">
                <div class="skill-category">
                    <h3><i class="fas fa-desktop"></i> أنظمة التشغيل</h3>
                    <ul class="skill-list">
                        <li>كالي لينكس - العمل في مجال الاختراقات</li>
                        <li>تجربة الثغرات من خلال مسابقات BugBounty</li>
                        <li>Windows و Linux</li>
                    </ul>
                </div>
                
                <div class="skill-category">
                    <h3><i class="fas fa-code"></i> البرمجة</h3>
                    <ul class="skill-list">
                        <li>HTML & CSS</li>
                        <li>C++ Programming</li>
                        <li>PHP & MySQL</li>
                        <li>Python</li>
                        <li>Scratch Programming</li>
                    </ul>
                </div>
                
                <div class="skill-category">
                    <h3><i class="fas fa-tools"></i> الصيانة والأنظمة</h3>
                    <ul class="skill-list">
                        <li>تركيب وصيانة أجهزة الكمبيوتر</li>
                        <li>إعداد الشبكات (كبسات وتوصيل)</li>
                        <li>استكشاف الأخطاء وإصلاحها</li>
                    </ul>
                </div>
                
                <div class="skill-category">
                    <h3><i class="fas fa-paint-brush"></i> البرامج والتصميم</h3>
                    <ul class="skill-list">
                        <li>Adobe Photoshop</li>
                        <li>Adobe Premiere Pro</li>
                        <li>Adobe Illustrator</li>
                        <li>Adobe After Effects</li>
                        <li>Final Cut Pro</li>
                        <li>CapCut, VivaCut</li>
                    </ul>
                </div>
                
                <div class="skill-category">
                    <h3><i class="fas fa-brain"></i> الذكاء الاصطناعي</h3>
                    <ul class="skill-list">
                        <li>استخدام أدوات الذكاء الاصطناعي</li>
                        <li>توليد الفيديوهات والصور</li>
                        <li>Google VEO 3</li>
                    </ul>
                </div>
                
                <div class="skill-category">
                    <h3><i class="fab fa-microsoft"></i> Microsoft Office</h3>
                    <ul class="skill-list">
                        <li>Microsoft Word</li>
                        <li>Microsoft Excel</li>
                        <li>Microsoft PowerPoint</li>
                        <li>Microsoft Outlook</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Experience Section -->
    <section class="section" id="experience">
        <div class="container">
            <h2 class="section-title">الخبرات العملية</h2>
            
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-marker"></div>
                        <div class="timeline-card">
                            <h3>العمل الحالي</h3>
                            <h4>جمعية التنمية الأهلية بحي قرطبة والرحاب</h4>
                            <p class="role">الإعلامي، مصمم، منتج فيديوهات وصور</p>
                            <ul>
                                <li>إنتاج المحتوى المرئي والتصويري</li>
                                <li>تصميم المواد الإعلانية والتسويقية</li>
                                <li>إدارة وسائل التواصل الاجتماعي</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-marker"></div>
                        <div class="timeline-card">
                            <h3>التطبيقات العملية</h3>
                            <h4>مصور ومنتج محتوى</h4>
                            <p class="role">مستقل</p>
                            <ul>
                                <li>التصوير الفوتوغرافي والفيديو</li>
                                <li>إنتاج وصناعة مقاطع فيديو إبداعية</li>
                                <li>مونتاج وتحرير المحتوى المرئي</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-marker"></div>
                        <div class="timeline-card">
                            <h3>دورة البرمجة بلغة سكراتش</h3>
                            <h4>20 ساعة تدريبية</h4>
                            <p class="role">مدرب</p>
                            <ul>
                                <li>تدريب الطلاب على أساسيات البرمجة من خلال بيئة سكراتش التفاعلية</li>
                                <li>تطوير مهارات التفكير المنطقي والحاسوبي لدى المتدربين</li>
                                <li>إرشاد الطلاب لإنشاء مشاريع تفاعلية وألعاب بسيطة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section class="section" id="courses">
        <div class="container">
            <h2 class="section-title">الدورات التدريبية</h2>
            
            <div class="courses-grid">
                <div class="course-card">
                    <div class="course-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>دورة البرمجة الأساسية</h3>
                    <p class="course-duration">معتمدة</p>
                    <p>تعلم أساسيات البرمجة والمفاهيم الأساسية</p>
                </div>
                
                <div class="course-card">
                    <div class="course-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>دورة الروبوت</h3>
                    <p class="course-duration">معتمدة</p>
                    <p>فهم مبادئ البرمجة للروبوتات والأتمتة</p>
                </div>
                
                <div class="course-card">
                    <div class="course-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3>دورات العطاء الرقمي</h3>
                    <p class="course-duration">متنوعة</p>
                    <p>دورات متنوعة في المجالات التقنية المختلفة</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Personal Skills Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">المهارات الشخصية</h2>
            
            <div class="personal-skills">
                <div class="skill-item">
                    <i class="fas fa-users"></i>
                    <h3>القيادة واتخاذ القرارات</h3>
                </div>
                <div class="skill-item">
                    <i class="fas fa-comments"></i>
                    <h3>مهارات التواصل الفعّال</h3>
                </div>
                <div class="skill-item">
                    <i class="fas fa-user-friends"></i>
                    <h3>العمل الجماعي</h3>
                </div>
                <div class="skill-item">
                    <i class="fas fa-microphone"></i>
                    <h3>التحدث أمام الجمهور</h3>
                </div>
                <div class="skill-item">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <h3>التدريب على مهارات التكنولوجيا</h3>
                </div>
                <div class="skill-item">
                    <i class="fas fa-tasks"></i>
                    <h3>إدارة المشاريع</h3>
                </div>
            </div>
        </div>
    </section>

    <!-- Certificates Section -->
    <section class="section" id="certificates">
        <div class="container">
            <h2 class="section-title">الشهادات</h2>
            
            <div class="certificates-grid" id="certificatesGrid">
                <!-- سيتم تحميل الشهادات هنا ديناميك -->
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section class="section" id="portfolio">
        <div class="container">
            <h2 class="section-title">أعمالي</h2>
            
            <div class="portfolio-filters">
                <button class="filter-btn" data-filter="website">المواقع</button>
                <button class="filter-btn active" data-filter="image">صور</button>
                <button class="filter-btn" data-filter="youtube">فيديوهات</button>
            </div>
            
            <div class="portfolio-grid" id="portfolioGrid">
                <!-- سيتم تحميل الأعمال هنا ديناميك -->
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="section" id="contact">
        <div class="container">
            <h2 class="section-title">تواصل معي</h2>
            
            <div class="contact-content">
                <div class="contact-info-grid">
                    <div class="contact-card">
                        <i class="fas fa-envelope"></i>
                        <h3>البريد الإلكتروني</h3>
                        <p><EMAIL></p>
                    </div>
                    
                    <div class="contact-card">
                        <i class="fas fa-phone"></i>
                        <h3>رقم الهاتف</h3>
                        <p>0503839769</p>
                    </div>
                    
                    <div class="contact-card">
                        <i class="fas fa-map-marker-alt"></i>
                        <h3>الموقع</h3>
                        <p>القصيم، بريدة</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2025 طارق محمد الشتيوي. جميع الحقوق محفوظة.</p>
                </div>
                <div class="footer-social">
                    <a href="mailto:<EMAIL>" class="social-link" title="البريد الإلكتروني">
                        <i class="fas fa-envelope"></i>
                    </a>
                    <a href="tel:0503839769" class="social-link" title="رقم الهاتف">
                        <i class="fas fa-phone"></i>
                    </a>
                    <a href="https://github.com/TareqAlshetaiwi" class="social-link" title="GitHub" target="_blank">
                        <i class="fab fa-github"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Admin Login Modal -->
    <div id="adminModal" class="admin-modal" style="display: none;">
        <div class="admin-modal-content">
            <span class="close-modal" onclick="closeAdminModal()">&times;</span>
            <h2>تسجيل دخول الإدارة</h2>
            <form id="adminLoginForm">
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="adminUsername" required>
                </div>
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" id="adminPassword" required>
                </div>
                <button type="submit" class="login-btn">دخول</button>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
