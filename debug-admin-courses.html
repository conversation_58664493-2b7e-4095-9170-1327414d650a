<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص صفحة إدارة الدورات</title>
    <link rel="stylesheet" href="admin.css">
    <style>
        .debug-panel {
            background: #f0f0f0;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 2px solid #333;
        }
        .debug-info {
            font-family: monospace;
            background: #fff;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <h1>🔍 تشخيص صفحة إدارة الدورات</h1>
        
        <div class="debug-panel">
            <h3>معلومات التشخيص المباشر:</h3>
            <div class="debug-info">
                <p><strong>currentSection:</strong> <span id="current-section">غير محدد</span></p>
                <p><strong>courses array length:</strong> <span id="courses-length">0</span></p>
                <p><strong>coursesGrid element:</strong> <span id="grid-status">غير موجود</span></p>
                <p><strong>admin-new.js loaded:</strong> <span id="js-status">لا</span></p>
            </div>
            
            <button onclick="manualTest()" class="btn-primary">اختبار يدوي</button>
            <button onclick="forceLoadCourses()" class="btn-secondary">إجبار تحميل الدورات</button>
            <button onclick="showDebugInfo()" class="btn-outline">عرض تفاصيل التشخيص</button>
        </div>
        
        <div class="content-section">
            <h2>الدورات المحملة:</h2>
            <div class="content-grid" id="coursesGrid">
                <div class="empty-state">
                    <i class="fas fa-graduation-cap"></i>
                    <p>جاري التحميل...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تعيين المتغيرات العامة
        let currentSection = 'courses';
        let courses = [];
        let editingId = null;
        
        // تحديث معلومات التشخيص
        function updateDebugInfo() {
            document.getElementById('current-section').textContent = currentSection || 'غير محدد';
            document.getElementById('courses-length').textContent = courses.length;
            document.getElementById('grid-status').textContent = document.getElementById('coursesGrid') ? 'موجود' : 'غير موجود';
            document.getElementById('js-status').textContent = typeof loadAllData === 'function' ? 'نعم' : 'لا';
        }
        
        // اختبار يدوي
        async function manualTest() {
            console.log('=== بدء الاختبار اليدوي ===');
            
            try {
                // اختبار 1: تحميل البيانات من API
                console.log('1. اختبار تحميل البيانات...');
                const response = await fetch('api.php?section=courses');
                const result = await response.json();
                
                console.log('API Response:', result);
                
                if (result.success) {
                    courses = result.data;
                    console.log('تم تحميل', courses.length, 'دورة');
                    
                    // اختبار 2: عرض الدورات
                    console.log('2. اختبار عرض الدورات...');
                    displayCoursesManually();
                    
                } else {
                    console.error('فشل في تحميل البيانات:', result.message);
                }
                
            } catch (error) {
                console.error('خطأ في الاختبار:', error);
            }
            
            updateDebugInfo();
        }
        
        // عرض الدورات يدوياً
        function displayCoursesManually() {
            const grid = document.getElementById('coursesGrid');
            if (!grid) {
                console.error('عنصر coursesGrid غير موجود');
                return;
            }
            
            grid.innerHTML = '';
            
            if (courses.length === 0) {
                grid.innerHTML = '<div class="empty-state"><i class="fas fa-graduation-cap"></i><p>لا توجد دورات</p></div>';
                return;
            }
            
            courses.forEach((course, index) => {
                const card = document.createElement('div');
                card.className = 'admin-card';
                card.innerHTML = `
                    <div class="card-content">
                        <h3>${course.title}</h3>
                        <p><strong>المقدم:</strong> ${course.provider}</p>
                        <p><strong>المدة:</strong> ${course.duration}</p>
                        <p><strong>الحالة:</strong> ${course.status}</p>
                        <p><strong>الوصف:</strong> ${course.description}</p>
                        <div class="card-actions">
                            <button class="btn-primary" onclick="alert('تعديل الدورة ${course.id}')">تعديل</button>
                            <button class="btn-danger" onclick="alert('حذف الدورة ${course.id}')">حذف</button>
                        </div>
                    </div>
                `;
                grid.appendChild(card);
            });
            
            console.log('تم عرض', courses.length, 'دورة في الشبكة');
        }
        
        // إجبار تحميل الدورات باستخدام admin-new.js
        async function forceLoadCourses() {
            console.log('=== إجبار تحميل الدورات ===');
            
            if (typeof loadAllData === 'function') {
                console.log('استدعاء loadAllData...');
                await loadAllData();
                updateDebugInfo();
            } else {
                console.error('دالة loadAllData غير متوفرة');
                alert('admin-new.js غير محمل بشكل صحيح');
            }
        }
        
        // عرض تفاصيل التشخيص
        function showDebugInfo() {
            const info = `
=== تفاصيل التشخيص ===
currentSection: ${currentSection}
courses.length: ${courses.length}
coursesGrid element: ${document.getElementById('coursesGrid') ? 'موجود' : 'غير موجود'}
loadAllData function: ${typeof loadAllData === 'function' ? 'متوفرة' : 'غير متوفرة'}
loadCourses function: ${typeof loadCourses === 'function' ? 'متوفرة' : 'غير متوفرة'}

=== بيانات الدورات ===
${JSON.stringify(courses, null, 2)}
            `;
            
            console.log(info);
            alert('تم طباعة التفاصيل في Console (اضغط F12)');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('صفحة التشخيص محملة');
            updateDebugInfo();
            
            // تحميل تلقائي بعد ثانية
            setTimeout(manualTest, 1000);
        });
    </script>
    
    <!-- تحميل admin-new.js بعد تعيين المتغيرات -->
    <script src="admin-new.js"></script>
</body>
</html>
