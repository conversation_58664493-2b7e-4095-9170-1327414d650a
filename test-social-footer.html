<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار فوتر روابط التواصل</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .footer-social {
            display: flex;
            gap: 15px;
            justify-content: center;
            padding: 20px;
            background: #2c3e50;
            border-radius: 8px;
        }
        .social-link {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        .social-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        .social-link i {
            font-size: 20px;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار فوتر روابط التواصل الاجتماعي</h1>
        
        <div class="input-group">
            <label>LinkedIn:</label>
            <input type="url" id="linkedin" placeholder="https://linkedin.com/in/username">
        </div>
        
        <div class="input-group">
            <label>GitHub:</label>
            <input type="url" id="github" placeholder="https://github.com/username">
        </div>
        
        <div class="input-group">
            <label>Twitter:</label>
            <input type="url" id="twitter" placeholder="https://twitter.com/username">
        </div>
        
        <div class="input-group">
            <label>Instagram:</label>
            <input type="url" id="instagram" placeholder="https://instagram.com/username">
        </div>
        
        <div class="input-group">
            <label>البريد الإلكتروني:</label>
            <input type="email" id="email" placeholder="<EMAIL>">
        </div>
        
        <div class="input-group">
            <label>رقم الجوال:</label>
            <input type="tel" id="phone" placeholder="0501234567">
        </div>
        
        <div class="input-group">
            <label>YouTube:</label>
            <input type="url" id="youtube" placeholder="https://youtube.com/channel/username">
        </div>
        
        <button onclick="updateFooter()">تحديث الفوتر</button>
        <button onclick="clearFooter()">مسح الفوتر</button>
        <button onclick="loadCurrentData()">تحميل البيانات الحالية</button>
    </div>

    <div class="test-container">
        <h2>معاينة الفوتر:</h2>
        <div class="footer-social" id="footerSocial">
            <!-- سيتم تحميل الأيقونات هنا -->
        </div>
    </div>

    <div class="test-container">
        <h2>سجل الأحداث:</h2>
        <div id="log" style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap;"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // نسخ دالة عرض الروابط من script.js
        function displaySocialLinks(socialLinks) {
            log('عرض روابط التواصل الاجتماعي: ' + JSON.stringify(socialLinks));
            
            const container = document.getElementById('footerSocial');
            if (!container) {
                log('خطأ: لم يتم العثور على عنصر footerSocial');
                return;
            }

            container.innerHTML = '';

            const socialConfig = {
                email: { icon: 'fas fa-envelope', title: 'البريد الإلكتروني', prefix: 'mailto:' },
                phone: { icon: 'fas fa-phone', title: 'رقم الهاتف', prefix: 'tel:' },
                linkedin: { icon: 'fab fa-linkedin', title: 'LinkedIn', prefix: '' },
                github: { icon: 'fab fa-github', title: 'GitHub', prefix: '' },
                twitter: { icon: 'fab fa-twitter', title: 'Twitter', prefix: '' },
                instagram: { icon: 'fab fa-instagram', title: 'Instagram', prefix: '' },
                youtube: { icon: 'fab fa-youtube', title: 'YouTube', prefix: '' }
            };

            let linksAdded = 0;

            Object.keys(socialConfig).forEach(key => {
                const value = socialLinks[key];
                if (value && value.trim()) {
                    log(`إضافة رابط ${key}: ${value}`);
                    const config = socialConfig[key];
                    const link = document.createElement('a');
                    link.href = config.prefix + value;
                    link.className = 'social-link';
                    link.title = config.title;
                    if (!config.prefix.startsWith('mailto:') && !config.prefix.startsWith('tel:')) {
                        link.target = '_blank';
                    }
                    link.innerHTML = `<i class="${config.icon}"></i>`;
                    container.appendChild(link);
                    linksAdded++;
                } else {
                    log(`تخطي رابط ${key}: فارغ أو غير موجود`);
                }
            });

            log(`تم إضافة ${linksAdded} رابط إلى الفوتر`);

            if (linksAdded === 0) {
                container.innerHTML = '<p style="color: #666; text-align: center;">لا توجد روابط للعرض</p>';
            }
        }

        function updateFooter() {
            const socialLinks = {
                linkedin: document.getElementById('linkedin').value,
                github: document.getElementById('github').value,
                twitter: document.getElementById('twitter').value,
                instagram: document.getElementById('instagram').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                youtube: document.getElementById('youtube').value
            };

            log('تحديث الفوتر بالبيانات الجديدة');
            displaySocialLinks(socialLinks);
        }

        function clearFooter() {
            log('مسح جميع الحقول والفوتر');
            document.querySelectorAll('input').forEach(input => input.value = '');
            displaySocialLinks({});
        }

        async function loadCurrentData() {
            try {
                log('تحميل البيانات الحالية من الخادم...');
                const response = await fetch('get-data.php?type=social-links');
                const result = await response.json();
                
                if (result.success && result.data) {
                    log('تم تحميل البيانات بنجاح: ' + JSON.stringify(result.data));
                    
                    // ملء الحقول
                    Object.keys(result.data).forEach(key => {
                        const input = document.getElementById(key);
                        if (input) {
                            input.value = result.data[key] || '';
                        }
                    });
                    
                    // عرض في الفوتر
                    displaySocialLinks(result.data);
                } else {
                    log('لا توجد بيانات محفوظة');
                }
            } catch (error) {
                log('خطأ في تحميل البيانات: ' + error.message);
            }
        }

        // تحميل البيانات عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل الصفحة');
            loadCurrentData();
        });
    </script>
</body>
</html>
