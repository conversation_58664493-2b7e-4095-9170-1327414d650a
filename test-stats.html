<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإحصائيات</title>
    <link rel="stylesheet" href="admin.css">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4f46e5;
        }
        .stat-label {
            color: #666;
            margin-top: 0.5rem;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <h1>🔢 اختبار إحصائيات الدورات</h1>
        
        <div class="test-section">
            <h2>كاردات الإحصائيات:</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalCourses">0</div>
                    <div class="stat-label">إجمالي الدورات</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number" id="totalHours">0</div>
                    <div class="stat-label">ساعات التدريب</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number" id="completedCourses">0</div>
                    <div class="stat-label">الدورات المكتملة</div>
                </div>
            </div>
            
            <button onclick="testStats()">اختبار الإحصائيات</button>
            <button onclick="manualUpdate()">تحديث يدوي</button>
        </div>
        
        <div class="test-section">
            <h2>معلومات التشخيص:</h2>
            <div id="debug-info"></div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let currentSection = 'courses';
        let courses = [];
        
        // اختبار الإحصائيات
        async function testStats() {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML = '<p>جاري الاختبار...</p>';
            
            try {
                // تحميل البيانات
                const response = await fetch('api.php?section=courses');
                const result = await response.json();
                
                if (result.success) {
                    courses = result.data;
                    console.log('Courses loaded:', courses);
                    
                    // تحديث الإحصائيات
                    updateCoursesStats();
                    
                    debugDiv.innerHTML = `
                        <p><strong>✅ تم تحميل البيانات بنجاح</strong></p>
                        <p>عدد الدورات: ${courses.length}</p>
                        <p>البيانات: <pre>${JSON.stringify(courses, null, 2)}</pre></p>
                    `;
                    
                } else {
                    debugDiv.innerHTML = `<p style="color: red;">❌ فشل في تحميل البيانات: ${result.message}</p>`;
                }
                
            } catch (error) {
                debugDiv.innerHTML = `<p style="color: red;">❌ خطأ: ${error.message}</p>`;
                console.error('Error:', error);
            }
        }
        
        // تحديث الإحصائيات يدوياً
        function manualUpdate() {
            if (courses.length === 0) {
                alert('لا توجد دورات محملة. اضغط "اختبار الإحصائيات" أولاً.');
                return;
            }
            
            updateCoursesStats();
            alert('تم تحديث الإحصائيات!');
        }
        
        // دالة تحديث الإحصائيات (نسخة من admin-new.js)
        function updateCoursesStats() {
            console.log('updateCoursesStats called with', courses.length, 'courses');
            
            const totalCoursesEl = document.getElementById('totalCourses');
            const totalHoursEl = document.getElementById('totalHours');
            const completedCoursesEl = document.getElementById('completedCourses');
            
            if (totalCoursesEl) {
                totalCoursesEl.textContent = courses.length;
                console.log('Total courses updated:', courses.length);
            }
            
            if (completedCoursesEl) {
                const completed = courses.filter(course => course.status === 'معتمدة' || course.status === 'completed').length;
                completedCoursesEl.textContent = completed;
                console.log('Completed courses updated:', completed);
            }
            
            if (totalHoursEl) {
                const totalHours = courses.reduce((total, course) => {
                    const hours = parseInt(course.duration) || 0;
                    return total + hours;
                }, 0);
                totalHoursEl.textContent = totalHours;
                console.log('Total hours updated:', totalHours);
            }
        }
        
        // تحميل تلقائي
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            testStats();
        });
    </script>
</body>
</html>
