<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المهارات الشخصية - طارق الشتيوي</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="admin.css">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-cog"></i> لوحة التحكم</h2>
            </div>
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="admin.html" class="nav-link">
                            <i class="fas fa-briefcase"></i>
                            <span>الأعمال</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-skills.html" class="nav-link">
                            <i class="fas fa-code"></i>
                            <span>المهارات التقنية</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="admin-personal-skills.html" class="nav-link">
                            <i class="fas fa-user-cog"></i>
                            <span>المهارات الشخصية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-experiences.html" class="nav-link">
                            <i class="fas fa-briefcase"></i>
                            <span>الخبرات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-courses.html" class="nav-link">
                            <i class="fas fa-graduation-cap"></i>
                            <span>الدورات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-certificates.html" class="nav-link">
                            <i class="fas fa-certificate"></i>
                            <span>الشهادات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-profile.html" class="nav-link">
                            <i class="fas fa-user"></i>
                            <span>الملف الشخصي</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="index.html" class="nav-link" target="_blank">
                            <i class="fas fa-eye"></i>
                            <span>معاينة الموقع</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <h1>إدارة المهارات الشخصية</h1>
                <div class="header-actions">
                    <button class="btn-primary" onclick="showAddModal()">
                        <i class="fas fa-plus"></i> إضافة مهارة جديدة
                    </button>
                </div>
            </header>

            <!-- Personal Skills Section -->
            <section class="admin-section active">
                <div class="section-content">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <i class="fas fa-user-cog"></i>
                            <div>
                                <h3 id="totalSkills">0</h3>
                                <p>إجمالي المهارات</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-star"></i>
                            <div>
                                <h3 id="advancedSkills">0</h3>
                                <p>مهارات متقدمة</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-chart-line"></i>
                            <div>
                                <h3 id="intermediateSkills">0</h3>
                                <p>مهارات متوسطة</p>
                            </div>
                        </div>
                    </div>

                    <div class="content-grid" id="skillsGrid">
                        <!-- سيتم تحميل المهارات هنا -->
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Add/Edit Modal -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة مهارة جديدة</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addForm">
                    <div id="personalSkillForm" class="form-section">
                        <input type="hidden" id="skillId">

                        <div class="form-group">
                            <label for="skillTitle">عنوان المهارة</label>
                            <input type="text" id="skillTitle" required>
                        </div>

                        <div class="form-group">
                            <label for="skillIcon">الأيقونة</label>
                            <select id="skillIcon" required>
                                <option value="fas fa-users">القيادة</option>
                                <option value="fas fa-comments">التواصل</option>
                                <option value="fas fa-user-friends">العمل الجماعي</option>
                                <option value="fas fa-microphone">التحدث</option>
                                <option value="fas fa-chalkboard-teacher">التدريب</option>
                                <option value="fas fa-tasks">إدارة المشاريع</option>
                                <option value="fas fa-lightbulb">الإبداع</option>
                                <option value="fas fa-puzzle-piece">حل المشاكل</option>
                                <option value="fas fa-clock">إدارة الوقت</option>
                                <option value="fas fa-handshake">التفاوض</option>
                                <option value="fas fa-brain">التفكير النقدي</option>
                                <option value="fas fa-heart">الذكاء العاطفي</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="skillDescription">الوصف</label>
                            <textarea id="skillDescription" rows="3"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="skillLevel">المستوى</label>
                            <select id="skillLevel" required>
                                <option value="مبتدئ">مبتدئ</option>
                                <option value="متوسط">متوسط</option>
                                <option value="متقدم">متقدم</option>
                                <option value="خبير">خبير</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="skillOrder">ترتيب العرض</label>
                            <input type="number" id="skillOrder" min="1" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal()">إلغاء</button>
                <button type="button" class="btn-primary" onclick="savePersonalSkill()">حفظ</button>
            </div>
        </div>
    </div>

    <script src="admin-personal-skills.js"></script>
</body>
</html>
