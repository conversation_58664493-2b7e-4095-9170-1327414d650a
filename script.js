// Dark Mode Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const themeToggle = document.getElementById('themeToggle');
    const body = document.body;
    const icon = themeToggle.querySelector('i');
    
    // Check for saved theme preference or default to light mode
    const currentTheme = localStorage.getItem('theme') || 'light';
    
    // Apply the saved theme on page load
    if (currentTheme === 'dark') {
        body.setAttribute('data-theme', 'dark');
        icon.className = 'fas fa-sun';
    } else {
        body.removeAttribute('data-theme');
        icon.className = 'fas fa-moon';
    }
    
    // Theme toggle functionality
    themeToggle.addEventListener('click', function() {
        const currentTheme = body.getAttribute('data-theme');
        
        if (currentTheme === 'dark') {
            // Switch to light mode
            body.removeAttribute('data-theme');
            icon.className = 'fas fa-moon';
            localStorage.setItem('theme', 'light');
        } else {
            // Switch to dark mode
            body.setAttribute('data-theme', 'dark');
            icon.className = 'fas fa-sun';
            localStorage.setItem('theme', 'dark');
        }
    });
});

// Smooth Scrolling for Navigation Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = target.offsetTop - headerHeight;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    });
});

// Header Background Change on Scroll
window.addEventListener('scroll', function() {
    const header = document.querySelector('.header');
    const scrolled = window.pageYOffset;
    
    if (scrolled > 100) {
        header.style.background = 'rgba(255, 255, 255, 0.98)';
        header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        
        // Dark mode adjustment
        if (document.body.getAttribute('data-theme') === 'dark') {
            header.style.background = 'rgba(17, 24, 39, 0.98)';
            header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.3)';
        }
    } else {
        header.style.background = 'rgba(255, 255, 255, 0.95)';
        header.style.boxShadow = 'none';
        
        // Dark mode adjustment
        if (document.body.getAttribute('data-theme') === 'dark') {
            header.style.background = 'rgba(17, 24, 39, 0.95)';
        }
    }
});

// Scroll Reveal Animation
function revealElements() {
    const reveals = document.querySelectorAll('.scroll-reveal');
    
    reveals.forEach(element => {
        const windowHeight = window.innerHeight;
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;
        
        if (elementTop < windowHeight - elementVisible) {
            element.classList.add('revealed');
        }
    });
}

// Add scroll reveal class to elements
document.addEventListener('DOMContentLoaded', function() {
    const elementsToReveal = [
        '.skill-category',
        '.timeline-item',
        '.course-card',
        '.skill-item',
        '.language-item',
        '.contact-card'
    ];
    
    elementsToReveal.forEach(selector => {
        document.querySelectorAll(selector).forEach(el => {
            el.classList.add('scroll-reveal');
        });
    });
    
    // Initial check
    revealElements();
});

// Listen for scroll events
window.addEventListener('scroll', revealElements);

// Active Navigation Link
function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop - 150;
        const sectionHeight = section.clientHeight;
        
        if (window.pageYOffset >= sectionTop && window.pageYOffset < sectionTop + sectionHeight) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === '#' + current) {
            link.classList.add('active');
        }
    });
}

window.addEventListener('scroll', updateActiveNavLink);

// Typing Effect for Hero Section
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    type();
}

// تحميل بيانات الملف الشخصي
async function loadProfileData() {
    try {
        // محاولة قراءة المعلومات الشخصية من الملف الجديد
        const personalResponse = await fetch('get-personal-info.php');
        const personalResult = await personalResponse.json();

        let personalInfo = {};
        if (personalResult.success && personalResult.data) {
            personalInfo = personalResult.data;
            console.log('تم تحميل المعلومات الشخصية من الخادم:', personalInfo);
        } else {
            // محاولة قراءة من localStorage
            const localPersonalInfo = localStorage.getItem('personalInfo');
            if (localPersonalInfo) {
                personalInfo = JSON.parse(localPersonalInfo);
                console.log('تم تحميل المعلومات الشخصية من localStorage:', personalInfo);
            }
        }

        // محاولة قراءة باقي البيانات من الملف القديم
        let otherData = {};
        try {
            const response = await fetch('get-data.php?type=profile');
            const result = await response.json();
            if (result.success && result.data) {
                otherData = result.data;
            }
        } catch (error) {
            console.log('لا توجد بيانات إضافية');
        }

        // دمج البيانات
        const profileData = {
            personal_info: personalInfo,
            about_section: otherData.about_section || {},
            languages_section: otherData.languages_section || {},
            hero_buttons: otherData.hero_buttons || {},
            floating_icons: otherData.floating_icons || [],
            social_links: otherData.social_links || {},
            meta_data: otherData.meta_data || {}
        };

        // حفظ البيانات المدمجة في localStorage
        localStorage.setItem('profileData', JSON.stringify(profileData));

        return profileData;

    } catch (error) {
        console.log('خطأ في تحميل البيانات، استخدام البيانات الافتراضية:', error);

        // البيانات الافتراضية
        return {
            personal_info: {
                name: "طارق محمد الشتيوي",
                subtitle: "شاب هاوٍ ومحب للحاسب الالي",
                email: "<EMAIL>",
                phone: "0503839769",
                location: "القصيم، بريدة"
            }
        };
    }
}

// تحديث عناصر الصفحة الرئيسية بالبيانات المحملة
function updatePageWithProfileData(profileData, useTypingEffect = false) {
    console.log('=== تحديث الصفحة الرئيسية ===');
    console.log('البيانات المستلمة:', profileData);
    console.log('استخدام تأثير الكتابة:', useTypingEffect);

    // تحديث الاسم والعنوان الفرعي في القسم الرئيسي باستخدام المعرفات الصحيحة
    const heroTitle = document.getElementById('hero-name');
    const heroSubtitle = document.getElementById('hero-subtitle');
    const heroLocation = document.getElementById('hero-location');

    if (profileData.personal_info) {
        const name = profileData.personal_info.name || 'طارق محمد الشتيوي';
        const subtitle = profileData.personal_info.subtitle || 'شاب متحمس ومحب لتخصص الحاسب الالي';
        const location = profileData.personal_info.location || 'القصيم، بريدة';

        console.log('تحديث الاسم إلى:', name);
        console.log('تحديث العنوان الفرعي إلى:', subtitle);
        console.log('تحديث الموقع إلى:', location);

        if (useTypingEffect) {
            // تحديث البيانات فوراً قبل بدء تأثير الكتابة
            if (heroTitle) {
                heroTitle.textContent = name;
                setTimeout(() => {
                    typeWriter(heroTitle, name, 150);
                }, 500);
            }

            if (heroSubtitle) {
                heroSubtitle.textContent = subtitle;
                setTimeout(() => {
                    typeWriter(heroSubtitle, subtitle, 100);
                }, 3000);
            }
        } else {
            // تحديث فوري للبيانات
            if (heroTitle) {
                heroTitle.textContent = name;
                console.log('تم تحديث الاسم في العنصر');
            }
            if (heroSubtitle) {
                heroSubtitle.textContent = subtitle;
                console.log('تم تحديث العنوان الفرعي في العنصر');
            }
        }

        // تحديث الموقع (إزالة الأيقونة وإضافة النص فقط)
        if (heroLocation) {
            heroLocation.innerHTML = '<i class="fas fa-map-marker-alt"></i> ' + location;
            console.log('تم تحديث الموقع في العنصر');
        }
    }

    // تحديث معلومات الاتصال
    updateContactInfo(profileData.personal_info);

    // تحديث قسم نبذة عني إذا كان موجوداً
    if (profileData.about_section) {
        updateAboutSection(profileData.about_section);
    }

    // تحديث الصورة الشخصية
    if (profileData.personal_info && profileData.personal_info.profile_image) {
        updateProfileImage(profileData.personal_info.profile_image);
    }
}

// تحديث معلومات الاتصال
function updateContactInfo(personalInfo) {
    if (!personalInfo) return;

    console.log('تحديث معلومات الاتصال:', personalInfo);

    // تحديث البريد الإلكتروني في القسم الرئيسي
    const heroEmail = document.getElementById('hero-email');
    if (heroEmail && personalInfo.email) {
        heroEmail.textContent = personalInfo.email;
        console.log('تم تحديث البريد الإلكتروني إلى:', personalInfo.email);
    }

    // تحديث رقم الهاتف في القسم الرئيسي
    const heroPhone = document.getElementById('hero-phone');
    if (heroPhone && personalInfo.phone) {
        heroPhone.textContent = personalInfo.phone;
        console.log('تم تحديث رقم الهاتف إلى:', personalInfo.phone);
    }

    // تحديث البريد الإلكتروني في جميع الأماكن
    const emailElements = document.querySelectorAll('[href^="mailto:"], .contact-card p, .contact-item span');
    emailElements.forEach(element => {
        if (element.textContent && element.textContent.includes('@')) {
            if (personalInfo.email) {
                element.textContent = personalInfo.email;
                if (element.tagName === 'A') {
                    element.href = `mailto:${personalInfo.email}`;
                }
            }
        }
    });

    // تحديث رقم الهاتف في جميع الأماكن
    const phoneElements = document.querySelectorAll('[href^="tel:"], .contact-card p, .contact-item span');
    phoneElements.forEach(element => {
        if (element.textContent && (element.textContent.includes('050') || element.textContent.includes('05') || element.textContent.includes('966'))) {
            if (personalInfo.phone) {
                element.textContent = personalInfo.phone;
                if (element.tagName === 'A') {
                    element.href = `tel:${personalInfo.phone}`;
                }
            }
        }
    });

    // تحديث الموقع
    const locationElements = document.querySelectorAll('.location, .contact-card p');
    locationElements.forEach(element => {
        if (element.textContent && (element.textContent.includes('القصيم') || element.textContent.includes('بريدة'))) {
            if (personalInfo.location) {
                element.textContent = personalInfo.location;
            }
        }
    });
}

// تحديث قسم نبذة عني
function updateAboutSection(aboutData) {
    const aboutSection = document.querySelector('#about');
    if (!aboutSection || !aboutData) return;

    // تحديث عنوان القسم
    const aboutTitle = aboutSection.querySelector('h2');
    if (aboutTitle && aboutData.title) {
        aboutTitle.textContent = aboutData.title;
    }

    // تحديث فقرات نبذة عني
    const aboutParagraphs = aboutSection.querySelectorAll('p');
    if (aboutData.paragraphs && aboutData.paragraphs.length > 0) {
        aboutParagraphs.forEach((p, index) => {
            if (aboutData.paragraphs[index]) {
                p.textContent = aboutData.paragraphs[index];
            }
        });
    }
}

// التحقق من حالة تسجيل الدخول وتحديث زر لوحة التحكم
function checkAdminStatus() {
    const isLoggedIn = localStorage.getItem('adminLoggedIn');
    const adminLink = document.getElementById('adminAccessLink');

    if (isLoggedIn === 'true' && adminLink) {
        adminLink.innerHTML = '<i class="fas fa-cog"></i> لوحة التحكم';
        adminLink.style.color = '#10b981';
        console.log('Admin is logged in, updated admin link');
    }
}

// معالج الوصول للوحة التحكم
function handleAdminAccess() {
    const isLoggedIn = localStorage.getItem('adminLoggedIn');

    if (isLoggedIn === 'true') {
        // إذا كان مسجل دخول، انتقل مباشرة للوحة التحكم
        goBackToAdmin();
    } else {
        // إذا لم يكن مسجل دخول، اعرض نافذة تسجيل الدخول
        showAdminLogin();
    }
}

// العودة للوحة التحكم
function goBackToAdmin() {
    // تحديث وقت آخر نشاط
    localStorage.setItem('adminLastActivity', new Date().getTime().toString());

    // الانتقال للوحة التحكم
    window.location.href = 'admin-portfolio.html';
}

// تحديث الصورة الشخصية
function updateProfileImage(imagePath) {
    const profileImages = document.querySelectorAll('.hero-image img, .profile-image img, .about-image img');

    profileImages.forEach(img => {
        if (img && imagePath) {
            // التحقق من أن المسار صحيح
            const newSrc = imagePath.startsWith('http') ? imagePath : imagePath;
            img.src = newSrc;

            // إضافة معالج للخطأ للعودة للصورة الافتراضية
            img.onerror = function() {
                this.src = 'photo/7.jpg';
            };
        }
    });
}

// مراقبة تغييرات localStorage
function watchLocalStorageChanges() {
    // مراقبة تغييرات localStorage
    window.addEventListener('storage', async function(e) {
        if (e.key === 'profileData') {
            const profileData = await loadProfileData();
            updatePageWithProfileData(profileData);
        }
    });

    // مراقبة التغييرات في نفس النافذة (للتحديث الفوري)
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function(key, value) {
        originalSetItem.apply(this, arguments);
        if (key === 'profileData' || key === 'personalInfo') {
            setTimeout(async () => {
                const profileData = await loadProfileData();
                updatePageWithProfileData(profileData, false); // تحديث فوري بدون تأثير الكتابة
            }, 100);
        }
    };
}

// Initialize typing effect with dynamic data
document.addEventListener('DOMContentLoaded', async function() {
    console.log('=== بدء تحميل الصفحة الرئيسية ===');

    // التحقق من إعادة تحميل البيانات
    const shouldRefresh = localStorage.getItem('refreshProfileData');
    const useTypingEffect = !shouldRefresh; // لا تستخدم تأثير الكتابة إذا كان قادماً من لوحة التحكم

    console.log('استخدام تأثير الكتابة:', useTypingEffect);

    // تحميل بيانات الملف الشخصي وتحديث الصفحة
    const profileData = await loadProfileData();
    console.log('البيانات المحملة:', profileData);

    updatePageWithProfileData(profileData, useTypingEffect);

    // إزالة معرف إعادة التحميل
    if (shouldRefresh) {
        localStorage.removeItem('refreshProfileData');
        console.log('تم إزالة معرف إعادة التحميل');
    }

    // بدء مراقبة التغييرات
    watchLocalStorageChanges();

    console.log('=== انتهاء تحميل الصفحة الرئيسية ===');
});

// Skills Progress Animation
function animateSkillBars() {
    const progressBars = document.querySelectorAll('.progress');
    
    progressBars.forEach(bar => {
        const rect = bar.getBoundingClientRect();
        const isVisible = rect.top < window.innerHeight && rect.bottom >= 0;
        
        if (isVisible && !bar.classList.contains('animated')) {
            const width = bar.style.width;
            bar.style.width = '0%';
            bar.classList.add('animated');
            
            setTimeout(() => {
                bar.style.width = width;
            }, 100);
        }
    });
}

window.addEventListener('scroll', animateSkillBars);
document.addEventListener('DOMContentLoaded', animateSkillBars);

// Parallax Effect for Floating Elements
function parallaxEffect() {
    const scrolled = window.pageYOffset;
    const floatingItems = document.querySelectorAll('.floating-item');
    
    floatingItems.forEach((item, index) => {
        const speed = 0.5 + (index * 0.1);
        const yPos = -(scrolled * speed);
        item.style.transform = `translateY(${yPos}px)`;
    });
}

window.addEventListener('scroll', parallaxEffect);

// Contact Form Interaction (if you add a contact form later)
function initContactForm() {
    const contactCards = document.querySelectorAll('.contact-card');
    
    contactCards.forEach(card => {
        card.addEventListener('click', function() {
            const email = this.textContent.includes('@') ? this.querySelector('p').textContent : null;
            const phone = this.textContent.includes('050') ? this.querySelector('p').textContent : null;
            
            if (email) {
                window.location.href = `mailto:${email}`;
            } else if (phone) {
                window.location.href = `tel:${phone}`;
            }
        });
        
        // Add hover effect
        card.style.cursor = 'pointer';
    });
}

document.addEventListener('DOMContentLoaded', initContactForm);

// Lazy Loading for Images (if you add images later)
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

document.addEventListener('DOMContentLoaded', lazyLoadImages);

// Mobile Menu Toggle (if you want to add a hamburger menu)
function initMobileMenu() {
    // Create mobile menu toggle button
    const nav = document.querySelector('.nav');
    const navMenu = document.querySelector('.nav-menu');
    
    // Check if we need mobile menu (on smaller screens)
    function checkMobileMenu() {
        if (window.innerWidth <= 768) {
            if (!document.querySelector('.mobile-toggle')) {
                const mobileToggle = document.createElement('button');
                mobileToggle.className = 'mobile-toggle';
                mobileToggle.innerHTML = '<i class="fas fa-bars"></i>';
                mobileToggle.setAttribute('aria-label', 'Toggle mobile menu');
                
                mobileToggle.addEventListener('click', function() {
                    navMenu.classList.toggle('active');
                    const icon = this.querySelector('i');
                    icon.className = navMenu.classList.contains('active') 
                        ? 'fas fa-times' 
                        : 'fas fa-bars';
                });
                
                nav.insertBefore(mobileToggle, navMenu);
            }
        } else {
            const mobileToggle = document.querySelector('.mobile-toggle');
            if (mobileToggle) {
                mobileToggle.remove();
                navMenu.classList.remove('active');
            }
        }
    }
    
    checkMobileMenu();
    window.addEventListener('resize', checkMobileMenu);
}

document.addEventListener('DOMContentLoaded', initMobileMenu);

// Performance optimization: Throttle scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Apply throttling to scroll events
const throttledParallax = throttle(parallaxEffect, 16);
const throttledReveal = throttle(revealElements, 16);
const throttledNavUpdate = throttle(updateActiveNavLink, 16);

window.removeEventListener('scroll', parallaxEffect);
window.removeEventListener('scroll', revealElements);
window.removeEventListener('scroll', updateActiveNavLink);

window.addEventListener('scroll', throttledParallax);
window.addEventListener('scroll', throttledReveal);
window.addEventListener('scroll', throttledNavUpdate);

// Add loading animation
document.addEventListener('DOMContentLoaded', function() {
    // Remove any loading spinner if present
    const loader = document.querySelector('.loader');
    if (loader) {
        loader.style.display = 'none';
    }
    
    // Add loaded class to body for CSS transitions
    document.body.classList.add('loaded');
});

// Print functionality
function printResume() {
    window.print();
}

// Add print button functionality if needed
document.addEventListener('DOMContentLoaded', function() {
    const printBtn = document.querySelector('.print-btn');
    if (printBtn) {
        printBtn.addEventListener('click', printResume);
    }
});

// Error handling for missing elements
function safeQuerySelector(selector, callback) {
    const element = document.querySelector(selector);
    if (element && typeof callback === 'function') {
        callback(element);
    }
}

// Initialize all features safely
document.addEventListener('DOMContentLoaded', function() {
    console.log('موقع طارق محمد الشتيوي تم تحميله بنجاح! 🚀');
    
    // Initialize features with error handling
    try {
        initContactForm();
        initMobileMenu();
        lazyLoadImages();

        // التحقق من تسجيل الدخول وإظهار زر لوحة التحكم
        checkAdminStatus();
    } catch (error) {
        console.warn('تحذير: بعض الميزات قد لا تعمل بشكل صحيح:', error);
    }
});

// Admin Login Functions
function showAdminLogin() {
    document.getElementById('adminModal').style.display = 'block';
}

function closeAdminModal() {
    document.getElementById('adminModal').style.display = 'none';
    document.getElementById('adminLoginForm').reset();
}

// معالج تسجيل دخول الإدارة
document.addEventListener('DOMContentLoaded', function() {
    const adminForm = document.getElementById('adminLoginForm');
    if (adminForm) {
        adminForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('adminUsername').value;
            const password = document.getElementById('adminPassword').value;
            
            // بيانات تسجيل الدخول (يمكنك تغييرها)
            if (username === 'admin' && password === 'admin123') {
                // حفظ حالة تسجيل الدخول مع الوقت
                localStorage.setItem('adminLoggedIn', 'true');
                localStorage.setItem('adminLoginTime', new Date().getTime().toString());
                localStorage.setItem('adminLastActivity', new Date().getTime().toString());

                // الانتقال للوحة التحكم
                window.location.href = 'admin-portfolio.html';
            } else {
                alert('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        });
    }
    
    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('adminModal');
        if (event.target === modal) {
            closeAdminModal();
        }
    });
});

// تحميل المحتوى في الصفحة الرئيسية
async function loadPublicPortfolio() {
    console.log('=== تحميل البورتفوليو في الصفحة الرئيسية ===');
    try {
        // تحميل البيانات من API
        const websitesResponse = await fetch('api.php?section=websites');
        const websitesData = await websitesResponse.json();
        const websites = websitesData.success ? websitesData.data : [];
        console.log('المواقع المحملة:', websites);

        const imagesResponse = await fetch('api.php?section=images');
        const imagesData = await imagesResponse.json();
        const images = imagesData.success ? imagesData.data : [];
        console.log('الصور المحملة:', images);

        const grid = document.getElementById('portfolioGrid');
        if (!grid) return;

        grid.innerHTML = '';

        // دمج جميع المحتويات
        const allItems = [
            ...websites.map(item => ({
                ...item,
                category: item.type === 'youtube' ? 'youtube' : 'website'
            })),
            ...images.map(item => ({
                ...item,
                category: 'image',
                src: item.url || `image/${item.filename}` // إضافة مسار الصورة
            }))
        ].sort((a, b) => b.id - a.id);

        console.log('جميع العناصر بعد الدمج:', allItems);

        if (allItems.length === 0) {
            grid.innerHTML = '<p style="text-align: center; color: var(--text-secondary); grid-column: 1/-1;">لا يوجد محتوى متاح حالياً</p>';
            return;
        }

        allItems.forEach(item => {
            console.log('إنشاء عنصر:', item);
            const itemElement = createPublicPortfolioItem(item);
            grid.appendChild(itemElement);
        });
    } catch (error) {
        console.error('Error loading portfolio:', error);
        const grid = document.getElementById('portfolioGrid');
        if (grid) {
            grid.innerHTML = '<p style="text-align: center; color: var(--text-secondary); grid-column: 1/-1;">حدث خطأ في تحميل المحتوى</p>';
        }
    }
}

// إنشاء عنصر للصفحة الرئيسية
function createPublicPortfolioItem(item) {
    const div = document.createElement('div');
    div.className = `portfolio-item ${item.category}`;
    
    let mediaElement = '';
    
    switch(item.category) {
        case 'website':
            mediaElement = `
                <div class="website-card">
                    <i class="fas fa-globe"></i>
                    <h4>${item.name}</h4>
                    <a href="${item.url}" target="_blank" class="visit-btn">
                        <i class="fas fa-external-link-alt"></i> زيارة الموقع
                    </a>
                </div>
            `;
            break;
            
        case 'image':
            mediaElement = `<img src="${item.src}" alt="${item.title}" loading="lazy">`;
            break;
            
        case 'youtube':
            mediaElement = `
                <div class="youtube-card" onclick="openYouTubeModal('${item.videoId}', '${item.title}')">
                    <img src="${item.thumbnail}" alt="${item.title}" loading="lazy">
                    <div class="youtube-play">
                        <i class="fab fa-youtube"></i>
                    </div>
                </div>
            `;
            break;
    }
    
    div.innerHTML = `
        <div class="portfolio-media">
            ${mediaElement}
        </div>
        <div class="portfolio-info">
            <h3>${item.title || item.name}</h3>
            <p>${item.description}</p>
        </div>
    `;
    
    return div;
}

// فتح نافذة يوتيوب منبثقة
function openYouTubeModal(videoId, title) {
    // إنشاء النافذة المنبثقة إذا لم تكن موجودة
    let modal = document.getElementById('youtubeModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'youtubeModal';
        modal.className = 'youtube-modal';
        modal.innerHTML = `
            <div class="youtube-modal-content">
                <span class="youtube-close" onclick="closeYouTubeModal()">&times;</span>
                <iframe id="youtubeIframe" class="youtube-iframe" src="" allowfullscreen></iframe>
            </div>
        `;
        document.body.appendChild(modal);
    }
    
    // تحديث مصدر الفيديو
    const iframe = document.getElementById('youtubeIframe');
    iframe.src = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`;
    
    // عرض النافذة
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // إغلاق النافذة عند النقر خارجها
    modal.onclick = function(event) {
        if (event.target === modal) {
            closeYouTubeModal();
        }
    };
}

// إغلاق نافذة يوتيوب
function closeYouTubeModal() {
    const modal = document.getElementById('youtubeModal');
    const iframe = document.getElementById('youtubeIframe');
    
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
    
    if (iframe) {
        iframe.src = '';
    }
}

// إضافة معالج لمفتاح Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeYouTubeModal();
    }
});

// فتح فيديو يوتيوب (الدالة القديمة للتوافق)
function openYouTube(url) {
    // استخراج معرف الفيديو من الرابط
    const videoId = extractYouTubeID(url);
    if (videoId) {
        openYouTubeModal(videoId, 'فيديو يوتيوب');
    } else {
        window.open(url, '_blank');
    }
}

// استخراج معرف يوتيوب من الرابط
function extractYouTubeID(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
}

// فلترة المحتوى
function filterPortfolio(filter) {
    const items = document.querySelectorAll('.portfolio-item');
    const buttons = document.querySelectorAll('.filter-btn');
    
    // تحديث الأزرار
    buttons.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
    
    // فلترة العناصر مع تأثير الانتقال
    items.forEach((item, index) => {
        const shouldShow = filter === 'all' || item.classList.contains(filter);

        if (shouldShow) {
            setTimeout(() => {
                item.style.display = 'block';
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        } else {
            item.style.opacity = '0';
            item.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                item.style.display = 'none';
            }, 300);
        }
    });
}

// تحميل روابط التواصل في الفوتر
async function loadSocialLinks() {
    try {
        let socialLinks = {};

        // تحميل من الخادم مباشرة (أولوية أعلى)
        try {
            const socialResponse = await fetch('get-data.php?type=social-links');
            const socialResult = await socialResponse.json();

            if (socialResult.success && socialResult.data) {
                socialLinks = socialResult.data;
            }
        } catch (error) {
            console.log('خطأ في تحميل من الخادم:', error);
        }

        // إذا لم توجد بيانات من الخادم، محاولة تحميل من localStorage
        if (Object.keys(socialLinks).length === 0 || !socialLinks) {
            const profileData = localStorage.getItem('profileData');
            if (profileData) {
                try {
                    const data = JSON.parse(profileData);
                    socialLinks = data.social_links || {};
                } catch (parseError) {
                    console.log('خطأ في تحليل localStorage:', parseError);
                }
            }
        }

        // عرض الروابط في الفوتر
        displaySocialLinks(socialLinks);

    } catch (error) {
        console.error('خطأ في تحميل الروابط:', error);
        // عرض الروابط الافتراضية
        displayDefaultSocialLinks();
    }
}

// عرض روابط التواصل
function displaySocialLinks(socialLinks) {
    const container = document.getElementById('footerSocial');
    if (!container) return;

    container.innerHTML = '';

    // التأكد من أن socialLinks هو كائن صالح
    if (!socialLinks || typeof socialLinks !== 'object') {
        socialLinks = {};
    }

    // تعريف الأيقونات والعناوين
    const socialConfig = {
        email: { icon: 'fas fa-envelope', title: 'البريد الإلكتروني', prefix: 'mailto:' },
        phone: { icon: 'fas fa-phone', title: 'رقم الهاتف', prefix: 'tel:' },
        linkedin: { icon: 'fab fa-linkedin', title: 'LinkedIn', prefix: '' },
        github: { icon: 'fab fa-github', title: 'GitHub', prefix: '' },
        twitter: { icon: 'fab fa-twitter', title: 'Twitter', prefix: '' },
        instagram: { icon: 'fab fa-instagram', title: 'Instagram', prefix: '' },
        youtube: { icon: 'fab fa-youtube', title: 'YouTube', prefix: '' }
    };

    let linksAdded = 0;

    // إضافة الروابط الموجودة فقط
    Object.keys(socialConfig).forEach(key => {
        const value = socialLinks[key];

        if (value && typeof value === 'string' && value.trim() !== '') {
            const config = socialConfig[key];
            const link = document.createElement('a');
            link.href = config.prefix + value.trim();
            link.className = 'social-link';
            link.title = config.title;
            if (!config.prefix.startsWith('mailto:') && !config.prefix.startsWith('tel:')) {
                link.target = '_blank';
            }
            link.innerHTML = `<i class="${config.icon}"></i>`;
            container.appendChild(link);
            linksAdded++;
        }
    });

    // إذا لم توجد روابط، عرض الروابط الافتراضية
    if (linksAdded === 0) {
        console.log('🔄 عرض الروابط الافتراضية...');
        displayDefaultSocialLinks();
    }
}

// عرض الروابط الافتراضية
function displayDefaultSocialLinks() {
    const container = document.getElementById('footerSocial');
    if (!container) return;

    container.innerHTML = `
        <a href="mailto:<EMAIL>" class="social-link" title="البريد الإلكتروني">
            <i class="fas fa-envelope"></i>
        </a>
        <a href="tel:0503839769" class="social-link" title="رقم الهاتف">
            <i class="fas fa-phone"></i>
        </a>
        <a href="https://github.com/TareqAlshetaiwi" class="social-link" title="GitHub" target="_blank">
            <i class="fab fa-github"></i>
        </a>
    `;
}

// وظائف القائمة المحمولة
function toggleMobileMenu() {
    // عدم تشغيل القائمة في الشاشات الكبيرة
    if (window.innerWidth > 991) {
        return;
    }

    const mobileMenu = document.getElementById('mobileMenu');
    const overlay = document.getElementById('mobileMenuOverlay');

    mobileMenu.classList.toggle('active');
    overlay.classList.toggle('active');

    // منع التمرير عند فتح القائمة
    if (mobileMenu.classList.contains('active')) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = 'auto';
    }
}

function closeMobileMenu() {
    const mobileMenu = document.getElementById('mobileMenu');
    const overlay = document.getElementById('mobileMenuOverlay');

    if (mobileMenu) mobileMenu.classList.remove('active');
    if (overlay) overlay.classList.remove('active');
    document.body.style.overflow = 'auto';
}

// إغلاق القائمة المحمولة عند تكبير الشاشة
function handleMobileMenuVisibility() {
    if (window.innerWidth > 991) {
        closeMobileMenu(); // إغلاق القائمة المحمولة إذا كانت مفتوحة
    }
}

// إضافة معالجات الأحداث
document.addEventListener('DOMContentLoaded', function() {
    loadPublicPortfolio();
    loadPublicCertificates();
    loadPublicSkills();
    loadPublicPersonalSkills();
    loadPublicExperiences();
    loadPublicCourses();

    // تأخير قصير لضمان تحميل العناصر
    setTimeout(() => {
        loadSocialLinks();
    }, 500);

    // إعداد القائمة المحمولة
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mobileMenuClose = document.getElementById('mobileMenuClose');
    const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');

    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', toggleMobileMenu);
    }

    if (mobileMenuClose) {
        mobileMenuClose.addEventListener('click', closeMobileMenu);
    }

    if (mobileMenuOverlay) {
        mobileMenuOverlay.addEventListener('click', closeMobileMenu);
    }

    // التحكم في ظهور القائمة المحمولة
    handleMobileMenuVisibility();
    window.addEventListener('resize', handleMobileMenuVisibility);

    // مراقبة تحديث روابط التواصل
    window.addEventListener('storage', function(e) {
        if (e.key === 'socialLinksUpdated' || e.key === 'profileData') {
            loadSocialLinks();
        }
    });

    // مراقبة إضافية للتحديث في نفس النافذة
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function(key, value) {
        originalSetItem.apply(this, arguments);
        if (key === 'socialLinksUpdated' || key === 'profileData') {
            setTimeout(() => {
                loadSocialLinks();
            }, 100);
        }
    };

    // معالجات أزرار الفلترة
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            filterPortfolio(this.dataset.filter);
        });
    });

    // عرض جميع المحتويات افتراضي عند تحميل الصفحة
    setTimeout(() => {
        filterPortfolio('all');
    }, 500);
});

// تحميل الشهادات في الصفحة الرئيسية
function loadPublicCertificates() {
    const certificates = JSON.parse(localStorage.getItem('certificates')) || [];
    const grid = document.getElementById('certificatesGrid');
    
    if (!grid) return;
    
    grid.innerHTML = '';
    
    if (certificates.length === 0) {
        // عرض الشهادات الافتراضية إذا لم توجد شهادات محفوظة
        const defaultCertificates = [
            { id: 1, title: 'شهادة البرمجة الأساسية', description: 'شهادة في أساسيات البرمجة', file: 'photo/1.pdf' },
            { id: 2, title: 'شهادة الروبوت', description: 'شهادة في برمجة الروبوتات', file: 'photo/2.pdf' },
            { id: 3, title: 'شهادة الأمن السيبراني', description: 'شهادة في الأمن السيبراني', file: 'photo/3.pdf' },
            { id: 4, title: 'شهادة التصميم', description: 'شهادة في التصميم الجرافيكي', file: 'photo/4.pdf' },
            { id: 5, title: 'شهادة المونتاج', description: 'شهادة في مونتاج الفيديو', file: 'photo/5.pdf' },
            { id: 6, title: 'شهادة الذكاء الاصطناعي', description: 'شهادة في الذكاء الاصطناعي', file: 'photo/6.pdf' }
        ];
        
        defaultCertificates.forEach(cert => {
            const certElement = createPublicCertificateCard(cert);
            grid.appendChild(certElement);
        });
        return;
    }
    
    certificates.forEach(cert => {
        const certElement = createPublicCertificateCard(cert);
        grid.appendChild(certElement);
    });
}

function createPublicCertificateCard(cert) {
    const div = document.createElement('div');
    div.className = 'certificate-card';
    
    div.innerHTML = `
        <div class="certificate-preview">
            ${cert.file ? 
                (cert.file.startsWith('data:') ? 
                    `<embed src="${cert.file}" type="application/pdf" width="100%" height="100%">` :
                    `<iframe src="${cert.file}" frameborder="0"></iframe>`
                ) :
                `<div style="text-align: center;">
                    <i class="fas fa-certificate"></i>
                    <h4 style="margin-top: 0.5rem; color: var(--text-secondary);">شهادة ${cert.id || cert.title}</h4>
                </div>`
            }
        </div>
        <div class="certificate-info">
            ${cert.description ? `<p style="color: var(--text-secondary); margin-bottom: 1rem;">${cert.description}</p>` : ''}
            <a href="${cert.file || 'photo/' + cert.id + '.pdf'}" target="_blank" class="certificate-link">
                <i class="fas fa-eye"></i> عرض الشهادة
            </a>
        </div>
    `;
    
    return div;
}

// تحميل المهارات
function loadPublicSkills() {
    const skills = JSON.parse(localStorage.getItem('skills')) || [];
    const skillsGrid = document.querySelector('.skills-grid');
    
    if (!skillsGrid || skills.length === 0) return;
    
    // مسح المحتوى الحالي
    skillsGrid.innerHTML = '';
    
    skills.forEach(skill => {
        const skillElement = document.createElement('div');
        skillElement.className = 'skill-category';
        
        skillElement.innerHTML = `
            <h3><i class="${skill.icon}"></i> ${skill.category}</h3>
            <ul class="skill-list">
                ${skill.items.map(item => `<li>${item}</li>`).join('')}
            </ul>
        `;
        
        skillsGrid.appendChild(skillElement);
    });
}

// تحميل الخبرات
function loadPublicExperiences() {
    const experiences = JSON.parse(localStorage.getItem('experiences')) || [];
    const timeline = document.querySelector('.timeline');
    
    if (!timeline || experiences.length === 0) return;
    
    // مسح المحتوى الحالي
    timeline.innerHTML = '';
    
    experiences.forEach(exp => {
        const expElement = document.createElement('div');
        expElement.className = 'timeline-item';
        
        expElement.innerHTML = `
            <div class="timeline-content">
                <div class="timeline-marker"></div>
                <div class="timeline-card">
                    <h3>${exp.title}</h3>
                    <h4>${exp.company}</h4>
                    <p class="role">${exp.role}</p>
                    ${exp.period ? `<p class="period">${exp.period}</p>` : ''}
                    ${exp.tasks && exp.tasks.length > 0 ? `
                        <ul>
                            ${exp.tasks.map(task => `<li>${task}</li>`).join('')}
                        </ul>
                    ` : ''}
                </div>
            </div>
        `;
        
        timeline.appendChild(expElement);
    });
}

// تحميل الدورات
function loadPublicCourses() {
    const courses = JSON.parse(localStorage.getItem('courses')) || [];
    const coursesGrid = document.querySelector('.courses-grid');
    
    if (!coursesGrid || courses.length === 0) return;
    
    // مسح المحتوى الحالي
    coursesGrid.innerHTML = '';
    
    courses.forEach(course => {
        const courseElement = document.createElement('div');
        courseElement.className = 'course-card';
        
        courseElement.innerHTML = `
            <div class="course-icon">
                <i class="${course.icon}"></i>
            </div>
            <h3>${course.title}</h3>
            <p class="course-duration">${course.duration}</p>
            <p>${course.description}</p>
        `;
        
        coursesGrid.appendChild(courseElement);
    });
}

// تحميل المهارات الشخصية في الصفحة الرئيسية
async function loadPublicPersonalSkills() {
    try {
        // محاولة تحميل من localStorage أولاً
        let personalSkills = JSON.parse(localStorage.getItem('personalSkills'));

        // إذا لم توجد في localStorage، حمل من JSON
        if (!personalSkills) {
            const response = await fetch('data/personal-skills.json');
            if (response.ok) {
                personalSkills = await response.json();
                localStorage.setItem('personalSkills', JSON.stringify(personalSkills));
            }
        }

        const container = document.getElementById('personal-skills-container');
        if (!container) return;

        container.innerHTML = '';

        if (!personalSkills || personalSkills.length === 0) {
            container.innerHTML = '<div class="no-items">لا توجد مهارات شخصية لعرضها حالياً</div>';
            return;
        }

        // ترتيب المهارات حسب الترتيب
        const sortedSkills = [...personalSkills].sort((a, b) => (a.order || 0) - (b.order || 0));

        sortedSkills.forEach(skill => {
            const skillItem = document.createElement('div');
            skillItem.className = 'skill-item';

            skillItem.innerHTML = `
                <i class="${skill.icon}"></i>
                <h3>${skill.title}</h3>
                ${skill.description ? `<p class="skill-description">${skill.description}</p>` : ''}
            `;

            container.appendChild(skillItem);
        });

    } catch (error) {
        console.error('خطأ في تحميل المهارات الشخصية:', error);

        // في حالة الخطأ، عرض المهارات الافتراضية
        const container = document.getElementById('personal-skills-container');
        if (container) {
            container.innerHTML = `
                <div class="skill-item">
                    <i class="fas fa-users"></i>
                    <h3>القيادة واتخاذ القرارات</h3>
                </div>
                <div class="skill-item">
                    <i class="fas fa-comments"></i>
                    <h3>مهارات التواصل الفعّال</h3>
                </div>
                <div class="skill-item">
                    <i class="fas fa-user-friends"></i>
                    <h3>العمل الجماعي</h3>
                </div>
                <div class="skill-item">
                    <i class="fas fa-microphone"></i>
                    <h3>التحدث أمام الجمهور</h3>
                </div>
                <div class="skill-item">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <h3>التدريب على مهارات التكنولوجيا</h3>
                </div>
                <div class="skill-item">
                    <i class="fas fa-tasks"></i>
                    <h3>إدارة المشاريع</h3>
                </div>
            `;
        }
    }
}
