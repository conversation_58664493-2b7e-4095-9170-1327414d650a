<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إدارة الشهادات</title>
    <link rel="stylesheet" href="admin.css">
    <style>
        body { padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        button { padding: 10px 20px; margin: 5px; background: #4f46e5; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .content-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>🧪 اختبار إدارة الشهادات</h1>
    
    <div class="test-section">
        <h2>1. اختبار تحميل البيانات</h2>
        <button onclick="testDataLoad()">اختبار تحميل البيانات</button>
        <div id="data-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. اختبار الدوال</h2>
        <button onclick="testFunctions()">اختبار الدوال</button>
        <div id="functions-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. عرض الشهادات</h2>
        <div class="content-grid" id="certificatesGrid">
            <!-- سيتم عرض الشهادات هنا -->
        </div>
    </div>
    
    <div class="test-section">
        <h2>4. اختبار النموذج</h2>
        <button onclick="testForm()">اختبار النموذج</button>
        <div id="form-result"></div>
    </div>

    <script>
        // متغيرات عامة
        let currentSection = 'certificates';
        let certificates = [];
        let editingId = null;
        
        async function testDataLoad() {
            const resultDiv = document.getElementById('data-result');
            resultDiv.innerHTML = '<p>جاري الاختبار...</p>';
            
            try {
                const response = await fetch('api.php?section=certificates');
                const result = await response.json();
                
                if (result.success) {
                    certificates = result.data;
                    resultDiv.innerHTML = `<p class="success">✅ تم تحميل ${certificates.length} شهادة</p>`;
                    displayCertificates();
                } else {
                    resultDiv.innerHTML = `<p class="error">❌ فشل في تحميل البيانات: ${result.message}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ خطأ: ${error.message}</p>`;
            }
        }
        
        function testFunctions() {
            const resultDiv = document.getElementById('functions-result');
            let results = '';
            
            // اختبار وجود الدوال
            const functions = [
                'handleCertificateSubmit',
                'loadCertificates', 
                'createCertificateCard',
                'editCertificate',
                'deleteCertificate',
                'updateCertificatesStats'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    results += `<p class="success">✅ ${funcName} موجودة</p>`;
                } else {
                    results += `<p class="error">❌ ${funcName} غير موجودة</p>`;
                }
            });
            
            resultDiv.innerHTML = results;
        }
        
        function displayCertificates() {
            const container = document.getElementById('certificatesGrid');
            container.innerHTML = '';
            
            if (certificates.length === 0) {
                container.innerHTML = '<p>لا توجد شهادات</p>';
                return;
            }
            
            const typeLabels = {
                degree: 'شهادة جامعية',
                diploma: 'دبلوم', 
                certificate: 'شهادة تدريبية',
                professional: 'شهادة مهنية',
                online: 'شهادة أونلاين',
                workshop: 'ورشة عمل'
            };
            
            certificates.forEach(certificate => {
                const div = document.createElement('div');
                div.className = 'admin-card';
                
                const fileExtension = certificate.file ? certificate.file.split('.').pop().toLowerCase() : '';
                const isPDF = fileExtension === 'pdf';
                
                div.innerHTML = `
                    <div class="card-actions">
                        <button class="action-btn edit-btn" onclick="testEdit(${certificate.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="testDelete(${certificate.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="certificate-header">
                            <i class="fas fa-certificate"></i>
                            <h3>${certificate.title}</h3>
                        </div>
                        <p><strong>الجهة المانحة:</strong> ${certificate.provider}</p>
                        <p><strong>النوع:</strong> ${typeLabels[certificate.type] || certificate.type}</p>
                        ${certificate.file ? `
                            <div class="certificate-file">
                                ${isPDF ? 
                                    `<p><i class="fas fa-file-pdf"></i> <a href="${certificate.file}" target="_blank">عرض الشهادة (PDF)</a></p>` :
                                    `<img src="${certificate.file}" alt="${certificate.title}" style="max-width: 200px; border-radius: 5px;">`
                                }
                            </div>
                        ` : ''}
                    </div>
                `;
                container.appendChild(div);
            });
        }
        
        function testEdit(id) {
            alert(`اختبار تعديل الشهادة رقم ${id}`);
        }
        
        function testDelete(id) {
            if (confirm(`هل تريد حذف الشهادة رقم ${id}؟`)) {
                alert(`تم حذف الشهادة رقم ${id}`);
            }
        }
        
        function testForm() {
            const resultDiv = document.getElementById('form-result');
            
            // اختبار وجود عناصر النموذج
            const elements = [
                'certTitle',
                'certProvider', 
                'certType',
                'certFile'
            ];
            
            let results = '<p>اختبار عناصر النموذج في صفحة admin-certificates.html:</p>';
            
            elements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    results += `<p class="success">✅ ${elementId} موجود</p>`;
                } else {
                    results += `<p class="error">❌ ${elementId} غير موجود</p>`;
                }
            });
            
            results += '<p><a href="admin-certificates.html" target="_blank">فتح صفحة إدارة الشهادات</a></p>';
            
            resultDiv.innerHTML = results;
        }
        
        // تحميل تلقائي
        document.addEventListener('DOMContentLoaded', function() {
            testDataLoad();
            setTimeout(testFunctions, 500);
        });
    </script>
    
    <!-- تحميل admin-new.js للاختبار -->
    <script src="admin-new.js"></script>
</body>
</html>
