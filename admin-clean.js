// المتغيرات العامة
let currentSection = 'portfolio';
let editingId = null;
let websites = [];
let albumImages = [];
let youtubeVideos = [];
let certificates = [];
let skills = [];
let experiences = [];
let courses = [];

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin panel loading...');
    
    // التحقق من تسجيل الدخول
    if (!checkAuth()) return;
    
    // تحميل البيانات
    loadAllData();
    
    // تهيئة لوحة التحكم
    initializeAdmin();
});

// التحقق من المصادقة
function checkAuth() {
    const isLoggedIn = localStorage.getItem('adminLoggedIn');
    if (!isLoggedIn || isLoggedIn !== 'true') {
        alert('يجب تسجيل الدخول أولاً');
        window.location.href = 'index.html';
        return false;
    }
    return true;
}

// تحميل جميع البيانات
function loadAllData() {
    websites = JSON.parse(localStorage.getItem('websites')) || [];
    albumImages = JSON.parse(localStorage.getItem('albumImages')) || [];
    youtubeVideos = JSON.parse(localStorage.getItem('youtubeVideos')) || [];
    certificates = JSON.parse(localStorage.getItem('certificates')) || [];
    skills = JSON.parse(localStorage.getItem('skills')) || [];
    experiences = JSON.parse(localStorage.getItem('experiences')) || [];
    courses = JSON.parse(localStorage.getItem('courses')) || [];
    
    console.log('Data loaded successfully');
}

// تهيئة لوحة التحكم
function initializeAdmin() {
    // إعداد معالجات الأحداث
    setupEventListeners();
    
    // تحديد القسم الحالي من الصفحة
    const currentPage = window.location.pathname.split('/').pop();
    if (currentPage.includes('portfolio')) currentSection = 'portfolio';
    else if (currentPage.includes('skills')) currentSection = 'skills';
    else if (currentPage.includes('experience')) currentSection = 'experience';
    else if (currentPage.includes('courses')) currentSection = 'courses';
    else if (currentPage.includes('certificates')) currentSection = 'certificates';
    else if (currentPage.includes('profile')) currentSection = 'profile';
    
    // تحميل المحتوى المناسب
    loadSectionData(currentSection);
    updateStats();
    
    console.log('Admin panel initialized for section:', currentSection);
}

// إعداد معالجات الأحداث
function setupEventListeners() {
    // معالج النموذج
    const form = document.getElementById('addForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
    
    // معالج تغيير نوع المحتوى
    const contentType = document.getElementById('contentType');
    if (contentType) {
        contentType.addEventListener('change', toggleContentForm);
    }
}

// تحميل بيانات القسم
function loadSectionData(section) {
    switch(section) {
        case 'portfolio':
            loadPortfolioItems();
            break;
        case 'skills':
            loadSkills();
            break;
        case 'experience':
            loadExperiences();
            break;
        case 'courses':
            loadCourses();
            break;
        case 'certificates':
            loadCertificates();
            break;
    }
}

// تحميل عناصر البورتفوليو
function loadPortfolioItems() {
    const grid = document.getElementById('portfolioGrid');
    if (!grid) return;
    
    grid.innerHTML = '';
    
    // دمج جميع أنواع المحتوى
    const allItems = [
        ...websites.map(item => ({...item, type: 'website'})),
        ...albumImages.map(item => ({...item, type: 'image'})),
        ...youtubeVideos.map(item => ({...item, type: 'youtube'}))
    ];
    
    // ترتيب حسب التاريخ
    allItems.sort((a, b) => b.id - a.id);
    
    if (allItems.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-folder-open"></i><p>لا توجد أعمال بعد</p></div>';
        return;
    }
    
    allItems.forEach(item => {
        const card = createPortfolioCard(item);
        grid.appendChild(card);
    });
}

// إنشاء كارد البورتفوليو
function createPortfolioCard(item) {
    const div = document.createElement('div');
    div.className = 'admin-card';
    
    let mediaContent = '';
    let actionButtons = '';
    
    switch(item.type) {
        case 'website':
            mediaContent = `
                <div class="card-preview website-preview">
                    <i class="fas fa-globe"></i>
                    <h4>${item.name}</h4>
                </div>
            `;
            actionButtons = `
                <button class="action-btn edit-btn" onclick="editWebsite(${item.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteWebsite(${item.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            break;
            
        case 'image':
            mediaContent = `<img src="${item.src}" alt="${item.title}" class="card-image">`;
            actionButtons = `
                <button class="action-btn edit-btn" onclick="editImage(${item.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteImage(${item.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            break;
            
        case 'youtube':
            mediaContent = `
                <div class="card-preview youtube-preview">
                    <img src="${item.thumbnail}" alt="${item.title}">
                    <div class="youtube-overlay">
                        <i class="fab fa-youtube"></i>
                    </div>
                </div>
            `;
            actionButtons = `
                <button class="action-btn edit-btn" onclick="editYoutube(${item.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteYoutube(${item.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            break;
    }
    
    div.innerHTML = `
        <div class="card-actions">
            ${actionButtons}
        </div>
        <div class="card-media">
            ${mediaContent}
        </div>
        <div class="card-content">
            <h3>${item.title || item.name}</h3>
            <p>${item.description || 'لا يوجد وصف'}</p>
            <span class="card-type">${getTypeLabel(item.type)}</span>
        </div>
    `;
    
    return div;
}

// الحصول على تسمية النوع
function getTypeLabel(type) {
    const labels = {
        website: 'موقع إلكتروني',
        image: 'صورة',
        youtube: 'فيديو يوتيوب'
    };
    return labels[type] || type;
}

// عرض نافذة الإضافة
function showAddModal() {
    console.log('Opening add modal for section:', currentSection);
    
    editingId = null;
    
    // تحديث عنوان النافذة
    const modalTitle = document.getElementById('modalTitle');
    if (modalTitle) {
        modalTitle.textContent = getModalTitle();
    }
    
    // إخفاء جميع النماذج
    document.querySelectorAll('.form-section').forEach(f => {
        f.style.display = 'none';
    });
    
    // عرض النموذج المناسب
    showCurrentForm();
    
    // مسح النموذج
    const form = document.getElementById('addForm');
    if (form) {
        form.reset();
    }
    
    // عرض النافذة
    const modal = document.getElementById('addModal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    } else {
        console.error('Modal not found!');
        showMessage('خطأ في فتح النافذة', 'error');
    }
}

// عرض النموذج الحالي
function showCurrentForm() {
    const formMap = {
        portfolio: 'portfolioForm',
        certificates: 'certificatesForm',
        skills: 'skillsForm',
        experience: 'experienceForm',
        courses: 'coursesForm'
    };
    
    const formId = formMap[currentSection];
    if (formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.style.display = 'block';
        }
    }
    
    // إذا كان البورتفوليو، عرض نموذج المواقع افتراضي
    if (currentSection === 'portfolio') {
        toggleContentForm();
    }
}

// الحصول على عنوان النافذة
function getModalTitle() {
    const titles = {
        portfolio: editingId ? 'تعديل عمل' : 'إضافة عمل جديد',
        skills: editingId ? 'تعديل مهارة' : 'إضافة مهارة جديدة',
        experience: editingId ? 'تعديل خبرة' : 'إضافة خبرة جديدة',
        courses: editingId ? 'تعديل دورة' : 'إضافة دورة جديدة',
        certificates: editingId ? 'تعديل شهادة' : 'إضافة شهادة جديدة'
    };
    
    return titles[currentSection] || 'إضافة محتوى جديد';
}

// إغلاق النافذة
function closeModal() {
    const modal = document.getElementById('addModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
    editingId = null;
}

// تغيير نوع المحتوى
function toggleContentForm() {
    const contentType = document.getElementById('contentType');
    if (!contentType) return;

    const selectedType = contentType.value;

    // إخفاء جميع النماذج الفرعية
    document.querySelectorAll('.content-fields').forEach(field => {
        field.style.display = 'none';
    });

    // عرض النموذج المناسب
    const targetForm = document.getElementById(selectedType + 'Fields');
    if (targetForm) {
        targetForm.style.display = 'block';
    }
}

// معالج إرسال النموذج
function handleFormSubmit(e) {
    e.preventDefault();

    console.log('Form submitted for section:', currentSection);

    try {
        switch(currentSection) {
            case 'portfolio':
                handlePortfolioSubmit();
                break;
            case 'certificates':
                handleCertificateSubmit();
                break;
            case 'skills':
                handleSkillSubmit();
                break;
            case 'experience':
                handleExperienceSubmit();
                break;
            case 'courses':
                handleCourseSubmit();
                break;
            default:
                throw new Error('قسم غير معروف');
        }
    } catch (error) {
        console.error('Error submitting form:', error);
        showMessage('حدث خطأ أثناء الحفظ: ' + error.message, 'error');
    }
}

// معالج البورتفوليو
function handlePortfolioSubmit() {
    const contentType = document.getElementById('contentType').value;

    switch(contentType) {
        case 'website':
            handleWebsiteSubmit();
            break;
        case 'image':
            handleImageSubmit();
            break;
        case 'youtube':
            handleYoutubeSubmit();
            break;
        default:
            throw new Error('نوع محتوى غير معروف');
    }
}

// معالج المواقع
function handleWebsiteSubmit() {
    const name = document.getElementById('websiteName').value.trim();
    const url = document.getElementById('websiteUrl').value.trim();
    const description = document.getElementById('websiteDescription').value.trim();

    if (!name || !url) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }

    if (editingId) {
        // تعديل موقع موجود
        const website = websites.find(item => item.id === editingId);
        if (website) {
            website.name = name;
            website.url = url;
            website.description = description;
        }
    } else {
        // إضافة موقع جديد
        const newWebsite = {
            id: Date.now(),
            name: name,
            url: url,
            description: description,
            date: new Date().toLocaleDateString('ar-SA')
        };
        websites.unshift(newWebsite);
    }

    localStorage.setItem('websites', JSON.stringify(websites));
    loadPortfolioItems();
    updateStats();
    closeModal();
    showMessage(editingId ? 'تم تحديث الموقع بنجاح!' : 'تم إضافة الموقع بنجاح!', 'success');
}

// تحديث الإحصائيات
function updateStats() {
    const totalWebsitesEl = document.getElementById('totalWebsites');
    const totalImagesEl = document.getElementById('totalImages');
    const totalVideosEl = document.getElementById('totalVideos');

    if (totalWebsitesEl) totalWebsitesEl.textContent = websites.length;
    if (totalImagesEl) totalImagesEl.textContent = albumImages.length;
    if (totalVideosEl) totalVideosEl.textContent = youtubeVideos.length;
}

// عرض رسالة
function showMessage(message, type = 'success') {
    // إزالة الرسائل السابقة
    const existingMessages = document.querySelectorAll('.message');
    existingMessages.forEach(msg => msg.remove());

    // إنشاء رسالة جديدة
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;

    // إضافة الرسالة للصفحة
    document.body.appendChild(messageDiv);

    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

// تسجيل الخروج
function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        localStorage.removeItem('adminLoggedIn');
        window.location.href = 'index.html';
    }
}

// دوال تحميل الأقسام الأخرى
function loadSkills() {
    const grid = document.getElementById('skillsGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (skills.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-code"></i><p>لا توجد مهارات بعد</p></div>';
        return;
    }

    skills.forEach(skill => {
        const card = createSkillCard(skill);
        grid.appendChild(card);
    });
}

function loadExperiences() {
    const grid = document.getElementById('experienceGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (experiences.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-briefcase"></i><p>لا توجد خبرات بعد</p></div>';
        return;
    }

    experiences.forEach(experience => {
        const card = createExperienceCard(experience);
        grid.appendChild(card);
    });
}

function loadCourses() {
    const grid = document.getElementById('coursesGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (courses.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-graduation-cap"></i><p>لا توجد دورات بعد</p></div>';
        return;
    }

    courses.forEach(course => {
        const card = createCourseCard(course);
        grid.appendChild(card);
    });
}

function loadCertificates() {
    const grid = document.getElementById('certificatesGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (certificates.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-certificate"></i><p>لا توجد شهادات بعد</p></div>';
        return;
    }

    certificates.forEach(certificate => {
        const card = createCertificateCard(certificate);
        grid.appendChild(card);
    });
}

// دوال إنشاء الكروت
function createSkillCard(skill) {
    const div = document.createElement('div');
    div.className = 'admin-card';

    const skillsList = Array.isArray(skill.items) ? skill.items.join(', ') : skill.items;

    div.innerHTML = `
        <div class="card-actions">
            <button class="action-btn edit-btn" onclick="editSkill(${skill.id})" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" onclick="deleteSkill(${skill.id})" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="card-content">
            <div class="skill-header">
                <i class="${skill.icon || 'fas fa-code'}"></i>
                <h3>${skill.category}</h3>
            </div>
            <p class="skill-items">${skillsList}</p>
            <span class="skill-level level-${skill.level || 'intermediate'}">${getLevelLabel(skill.level)}</span>
        </div>
    `;

    return div;
}

function createExperienceCard(experience) {
    const div = document.createElement('div');
    div.className = 'admin-card';

    const tasksList = Array.isArray(experience.tasks) ? experience.tasks.join(', ') : experience.tasks;

    div.innerHTML = `
        <div class="card-actions">
            <button class="action-btn edit-btn" onclick="editExperience(${experience.id})" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" onclick="deleteExperience(${experience.id})" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="card-content">
            <h3>${experience.title}</h3>
            <h4>${experience.company}</h4>
            <p class="experience-period">${experience.period}</p>
            <p class="experience-role">${experience.role}</p>
            <p class="experience-tasks">${tasksList}</p>
            <span class="experience-type">${getExperienceTypeLabel(experience.type)}</span>
        </div>
    `;

    return div;
}

function createCourseCard(course) {
    const div = document.createElement('div');
    div.className = 'admin-card';

    div.innerHTML = `
        <div class="card-actions">
            <button class="action-btn edit-btn" onclick="editCourse(${course.id})" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" onclick="deleteCourse(${course.id})" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="card-content">
            <div class="course-header">
                <i class="${course.icon || 'fas fa-graduation-cap'}"></i>
                <h3>${course.title}</h3>
            </div>
            <h4>${course.provider}</h4>
            <p class="course-duration">${course.duration}</p>
            <p class="course-description">${course.description}</p>
            <span class="course-status status-${course.status}">${getStatusLabel(course.status)}</span>
        </div>
    `;

    return div;
}

function createCertificateCard(certificate) {
    const div = document.createElement('div');
    div.className = 'admin-card';

    div.innerHTML = `
        <div class="card-actions">
            <button class="action-btn edit-btn" onclick="editCertificate(${certificate.id})" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" onclick="deleteCertificate(${certificate.id})" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="card-content">
            <h3>${certificate.title}</h3>
            <h4>${certificate.issuer}</h4>
            <p class="certificate-date">${certificate.date}</p>
            <p class="certificate-description">${certificate.description}</p>
            <span class="certificate-type">${getCertificateTypeLabel(certificate.type)}</span>
        </div>
    `;

    return div;
}

// دوال المساعدة للتسميات
function getLevelLabel(level) {
    const labels = {
        beginner: 'مبتدئ',
        intermediate: 'متوسط',
        advanced: 'متقدم',
        expert: 'خبير'
    };
    return labels[level] || 'متوسط';
}

function getExperienceTypeLabel(type) {
    const labels = {
        fulltime: 'دوام كامل',
        parttime: 'دوام جزئي',
        freelance: 'عمل حر',
        contract: 'عقد مؤقت',
        internship: 'تدريب'
    };
    return labels[type] || 'دوام كامل';
}

function getStatusLabel(status) {
    const labels = {
        completed: 'مكتملة',
        'in-progress': 'قيد التقدم',
        planned: 'مخطط لها'
    };
    return labels[status] || 'مكتملة';
}

function getCertificateTypeLabel(type) {
    const labels = {
        degree: 'شهادة جامعية',
        diploma: 'دبلوم',
        certificate: 'شهادة تدريبية',
        professional: 'شهادة مهنية',
        online: 'شهادة أونلاين',
        workshop: 'ورشة عمل'
    };
    return labels[type] || 'شهادة تدريبية';
}

// معالجات النماذج
function handleSkillSubmit() {
    const category = document.getElementById('skillCategory').value.trim();
    const icon = document.getElementById('skillIcon').value.trim();
    const items = document.getElementById('skillItems').value.trim();
    const level = document.getElementById('skillLevel').value;
    const description = document.getElementById('skillDescription').value.trim();

    if (!category || !icon || !items) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }

    const skillItems = items.split(',').map(item => item.trim()).filter(item => item);

    if (editingId) {
        const skill = skills.find(item => item.id === editingId);
        if (skill) {
            skill.category = category;
            skill.icon = icon;
            skill.items = skillItems;
            skill.level = level;
            skill.description = description;
        }
    } else {
        const newSkill = {
            id: Date.now(),
            category: category,
            icon: icon,
            items: skillItems,
            level: level,
            description: description,
            date: new Date().toLocaleDateString('ar-SA')
        };
        skills.unshift(newSkill);
    }

    localStorage.setItem('skills', JSON.stringify(skills));
    loadSkills();
    closeModal();
    showMessage(editingId ? 'تم تحديث المهارة بنجاح!' : 'تم إضافة المهارة بنجاح!', 'success');
}

function handleExperienceSubmit() {
    const title = document.getElementById('expTitle').value.trim();
    const company = document.getElementById('expCompany').value.trim();
    const type = document.getElementById('expType').value;
    const role = document.getElementById('expRole').value.trim();
    const period = document.getElementById('expPeriod').value.trim();
    const tasks = document.getElementById('expTasks').value.trim();
    const technologies = document.getElementById('expTechnologies').value.trim();
    const location = document.getElementById('expLocation').value.trim();
    const companyUrl = document.getElementById('expCompanyUrl').value.trim();

    if (!title || !company || !role || !period || !tasks) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }

    const tasksList = tasks.split(',').map(task => task.trim()).filter(task => task);

    if (editingId) {
        const experience = experiences.find(item => item.id === editingId);
        if (experience) {
            experience.title = title;
            experience.company = company;
            experience.type = type;
            experience.role = role;
            experience.period = period;
            experience.tasks = tasksList;
            experience.technologies = technologies;
            experience.location = location;
            experience.companyUrl = companyUrl;
        }
    } else {
        const newExperience = {
            id: Date.now(),
            title: title,
            company: company,
            type: type,
            role: role,
            period: period,
            tasks: tasksList,
            technologies: technologies,
            location: location,
            companyUrl: companyUrl,
            date: new Date().toLocaleDateString('ar-SA')
        };
        experiences.unshift(newExperience);
    }

    localStorage.setItem('experiences', JSON.stringify(experiences));
    loadExperiences();
    closeModal();
    showMessage(editingId ? 'تم تحديث الخبرة بنجاح!' : 'تم إضافة الخبرة بنجاح!', 'success');
}

function handleCourseSubmit() {
    const title = document.getElementById('courseTitle').value.trim();
    const provider = document.getElementById('courseProvider').value.trim();
    const icon = document.getElementById('courseIcon').value.trim();
    const duration = document.getElementById('courseDuration').value.trim();
    const date = document.getElementById('courseDate').value;
    const status = document.getElementById('courseStatus').value;
    const description = document.getElementById('courseDescription').value.trim();
    const skills = document.getElementById('courseSkills').value.trim();
    const certificateUrl = document.getElementById('courseCertificateUrl').value.trim();
    const courseUrl = document.getElementById('courseUrl').value.trim();
    const rating = document.getElementById('courseRating').value;

    if (!title || !provider || !duration || !date || !status || !description) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }

    const skillsList = skills ? skills.split(',').map(skill => skill.trim()).filter(skill => skill) : [];

    if (editingId) {
        const course = courses.find(item => item.id === editingId);
        if (course) {
            course.title = title;
            course.provider = provider;
            course.icon = icon;
            course.duration = duration;
            course.date = date;
            course.status = status;
            course.description = description;
            course.skills = skillsList;
            course.certificateUrl = certificateUrl;
            course.courseUrl = courseUrl;
            course.rating = rating;
        }
    } else {
        const newCourse = {
            id: Date.now(),
            title: title,
            provider: provider,
            icon: icon,
            duration: duration,
            date: date,
            status: status,
            description: description,
            skills: skillsList,
            certificateUrl: certificateUrl,
            courseUrl: courseUrl,
            rating: rating,
            addedDate: new Date().toLocaleDateString('ar-SA')
        };
        courses.unshift(newCourse);
    }

    localStorage.setItem('courses', JSON.stringify(courses));
    loadCourses();
    closeModal();
    showMessage(editingId ? 'تم تحديث الدورة بنجاح!' : 'تم إضافة الدورة بنجاح!', 'success');
}

function handleCertificateSubmit() {
    const title = document.getElementById('certificateTitle').value.trim();
    const issuer = document.getElementById('certificateIssuer').value.trim();
    const type = document.getElementById('certificateType').value;
    const date = document.getElementById('certificateDate').value;
    const expiry = document.getElementById('certificateExpiry').value;
    const number = document.getElementById('certificateNumber').value.trim();
    const description = document.getElementById('certificateDescription').value.trim();
    const skills = document.getElementById('certificateSkills').value.trim();
    const url = document.getElementById('certificateUrl').value.trim();
    const image = document.getElementById('certificateImage').value.trim();
    const grade = document.getElementById('certificateGrade').value;
    const status = document.getElementById('certificateStatus').value;

    if (!title || !issuer || !type || !date || !description || !status) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }

    const skillsList = skills ? skills.split(',').map(skill => skill.trim()).filter(skill => skill) : [];

    if (editingId) {
        const certificate = certificates.find(item => item.id === editingId);
        if (certificate) {
            certificate.title = title;
            certificate.issuer = issuer;
            certificate.type = type;
            certificate.date = date;
            certificate.expiry = expiry;
            certificate.number = number;
            certificate.description = description;
            certificate.skills = skillsList;
            certificate.url = url;
            certificate.image = image;
            certificate.grade = grade;
            certificate.status = status;
        }
    } else {
        const newCertificate = {
            id: Date.now(),
            title: title,
            issuer: issuer,
            type: type,
            date: date,
            expiry: expiry,
            number: number,
            description: description,
            skills: skillsList,
            url: url,
            image: image,
            grade: grade,
            status: status,
            addedDate: new Date().toLocaleDateString('ar-SA')
        };
        certificates.unshift(newCertificate);
    }

    localStorage.setItem('certificates', JSON.stringify(certificates));
    loadCertificates();
    closeModal();
    showMessage(editingId ? 'تم تحديث الشهادة بنجاح!' : 'تم إضافة الشهادة بنجاح!', 'success');
}

// دوال مؤقتة للصور واليوتيوب
function handleImageSubmit() {
    showMessage('ميزة إضافة الصور قيد التطوير', 'error');
}

function handleYoutubeSubmit() {
    showMessage('ميزة إضافة فيديوهات اليوتيوب قيد التطوير', 'error');
}

// دوال التعديل والحذف للمواقع
function editWebsite(id) {
    const website = websites.find(item => item.id === id);
    if (!website) return;

    editingId = id;
    document.getElementById('contentType').value = 'website';
    toggleContentForm();

    document.getElementById('websiteName').value = website.name;
    document.getElementById('websiteUrl').value = website.url;
    document.getElementById('websiteDescription').value = website.description;

    showAddModal();
}

function deleteWebsite(id) {
    if (confirm('هل أنت متأكد من حذف هذا الموقع؟')) {
        websites = websites.filter(item => item.id !== id);
        localStorage.setItem('websites', JSON.stringify(websites));
        loadPortfolioItems();
        updateStats();
        showMessage('تم حذف الموقع بنجاح!', 'success');
    }
}

// دوال التعديل والحذف للصور واليوتيوب
function editImage(id) {
    showMessage('ميزة تعديل الصور قيد التطوير', 'error');
}

function deleteImage(id) {
    if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
        albumImages = albumImages.filter(item => item.id !== id);
        localStorage.setItem('albumImages', JSON.stringify(albumImages));
        loadPortfolioItems();
        updateStats();
        showMessage('تم حذف الصورة بنجاح!', 'success');
    }
}

function editYoutube(id) {
    showMessage('ميزة تعديل فيديوهات اليوتيوب قيد التطوير', 'error');
}

function deleteYoutube(id) {
    if (confirm('هل أنت متأكد من حذف هذا الفيديو؟')) {
        youtubeVideos = youtubeVideos.filter(item => item.id !== id);
        localStorage.setItem('youtubeVideos', JSON.stringify(youtubeVideos));
        loadPortfolioItems();
        updateStats();
        showMessage('تم حذف الفيديو بنجاح!', 'success');
    }
}

// دوال التعديل والحذف للمهارات
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;

    editingId = id;

    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = Array.isArray(skill.items) ? skill.items.join(', ') : skill.items;
    document.getElementById('skillLevel').value = skill.level;
    document.getElementById('skillDescription').value = skill.description;

    showAddModal();
}

function deleteSkill(id) {
    if (confirm('هل أنت متأكد من حذف هذه المهارة؟')) {
        skills = skills.filter(item => item.id !== id);
        localStorage.setItem('skills', JSON.stringify(skills));
        loadSkills();
        showMessage('تم حذف المهارة بنجاح!', 'success');
    }
}

// دوال التعديل والحذف للخبرات
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;

    editingId = id;

    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expType').value = exp.type;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = Array.isArray(exp.tasks) ? exp.tasks.join(', ') : exp.tasks;
    document.getElementById('expTechnologies').value = exp.technologies;
    document.getElementById('expLocation').value = exp.location;
    document.getElementById('expCompanyUrl').value = exp.companyUrl;

    showAddModal();
}

function deleteExperience(id) {
    if (confirm('هل أنت متأكد من حذف هذه الخبرة؟')) {
        experiences = experiences.filter(item => item.id !== id);
        localStorage.setItem('experiences', JSON.stringify(experiences));
        loadExperiences();
        showMessage('تم حذف الخبرة بنجاح!', 'success');
    }
}

// دوال التعديل والحذف للدورات
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;

    editingId = id;

    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseProvider').value = course.provider;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDate').value = course.date;
    document.getElementById('courseStatus').value = course.status;
    document.getElementById('courseDescription').value = course.description;
    document.getElementById('courseSkills').value = Array.isArray(course.skills) ? course.skills.join(', ') : course.skills;
    document.getElementById('courseCertificateUrl').value = course.certificateUrl;
    document.getElementById('courseUrl').value = course.courseUrl;
    document.getElementById('courseRating').value = course.rating;

    showAddModal();
}

function deleteCourse(id) {
    if (confirm('هل أنت متأكد من حذف هذه الدورة؟')) {
        courses = courses.filter(item => item.id !== id);
        localStorage.setItem('courses', JSON.stringify(courses));
        loadCourses();
        showMessage('تم حذف الدورة بنجاح!', 'success');
    }
}

// دوال التعديل والحذف للشهادات
function editCertificate(id) {
    const certificate = certificates.find(item => item.id === id);
    if (!certificate) return;

    editingId = id;

    document.getElementById('certificateTitle').value = certificate.title;
    document.getElementById('certificateIssuer').value = certificate.issuer;
    document.getElementById('certificateType').value = certificate.type;
    document.getElementById('certificateDate').value = certificate.date;
    document.getElementById('certificateExpiry').value = certificate.expiry;
    document.getElementById('certificateNumber').value = certificate.number;
    document.getElementById('certificateDescription').value = certificate.description;
    document.getElementById('certificateSkills').value = Array.isArray(certificate.skills) ? certificate.skills.join(', ') : certificate.skills;
    document.getElementById('certificateUrl').value = certificate.url;
    document.getElementById('certificateImage').value = certificate.image;
    document.getElementById('certificateGrade').value = certificate.grade;
    document.getElementById('certificateStatus').value = certificate.status;

    showAddModal();
}

function deleteCertificate(id) {
    if (confirm('هل أنت متأكد من حذف هذه الشهادة؟')) {
        certificates = certificates.filter(item => item.id !== id);
        localStorage.setItem('certificates', JSON.stringify(certificates));
        loadCertificates();
        showMessage('تم حذف الشهادة بنجاح!', 'success');
    }
}
