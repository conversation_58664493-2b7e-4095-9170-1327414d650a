<?php
$certificates = [
    [
        "id" => 1,
        "title" => "شهادة أساسيات البرمجة",
        "provider" => "معهد التدريب التقني",
        "type" => "certificate",
        "file" => "image/certificate1.jpg",
        "order" => 1
    ],
    [
        "id" => 2,
        "title" => "شهادة الأمن السيبراني",
        "provider" => "أكاديمية الأمن السيبراني",
        "type" => "professional",
        "file" => "image/certificate2.pdf",
        "order" => 2
    ],
    [
        "id" => 3,
        "title" => "شهادة التطوع الرقمي",
        "provider" => "منصة العطاء الرقمي",
        "type" => "online",
        "file" => "image/certificate3.jpg",
        "order" => 3
    ]
];

$json = json_encode($certificates, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
file_put_contents('data/certificates.json', $json);

echo "تم إنشاء ملف certificates.json بنجاح\n";
echo "عدد الشهادات: " . count($certificates) . "\n";
?>
