<?php
$certificates = [
    [
        "id" => 1,
        "title" => "شهادة أساسيات البرمجة",
        "provider" => "معهد التدريب التقني",
        "issue_date" => "2023-06-15",
        "expiry_date" => "",
        "image" => "image/certificate1.jpg",
        "description" => "شهادة في أساسيات البرمجة والتطوير",
        "verification_url" => "",
        "skills" => ["HTML", "CSS", "JavaScript"],
        "order" => 1
    ],
    [
        "id" => 2,
        "title" => "شهادة الأمن السيبراني",
        "provider" => "أكاديمية الأمن السيبراني",
        "issue_date" => "2023-08-20",
        "expiry_date" => "2025-08-20",
        "image" => "image/certificate2.jpg",
        "description" => "شهادة متخصصة في الأمن السيبراني",
        "verification_url" => "",
        "skills" => ["Penetration Testing", "Network Security", "Ethical Hacking"],
        "order" => 2
    ],
    [
        "id" => 3,
        "title" => "شهادة التطوع الرقمي",
        "provider" => "منصة العطاء الرقمي",
        "issue_date" => "2023-12-01",
        "expiry_date" => "",
        "image" => "image/certificate3.jpg",
        "description" => "شهادة تقدير للمشاركة في التطوع الرقمي",
        "verification_url" => "",
        "skills" => ["التطوع", "العمل الجماعي", "المسؤولية الاجتماعية"],
        "order" => 3
    ]
];

$json = json_encode($certificates, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
file_put_contents('data/certificates.json', $json);

echo "تم إنشاء ملف certificates.json بنجاح\n";
echo "عدد الشهادات: " . count($certificates) . "\n";
?>
