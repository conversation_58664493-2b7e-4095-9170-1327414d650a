<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النوافذ المنبثقة</title>
    <link rel="stylesheet" href="admin.css">
    <style>
        body { padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .test-btn { margin: 10px; padding: 10px 20px; background: #4f46e5; color: white; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>اختبار النوافذ المنبثقة</h1>
    
    <div class="test-section">
        <h2>اختبار نافذة المهارات</h2>
        <button class="test-btn" onclick="testSkillsModal()">فتح نافذة المهارات</button>
        <button class="test-btn" onclick="testEditSkill()">اختبار تعديل مهارة</button>
    </div>
    
    <div class="test-section">
        <h2>اختبار نافذة الخبرات</h2>
        <button class="test-btn" onclick="testExperienceModal()">فتح نافذة الخبرات</button>
        <button class="test-btn" onclick="testEditExperience()">اختبار تعديل خبرة</button>
    </div>

    <!-- نافذة منبثقة للاختبار -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">اختبار النافذة</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addForm">
                    <!-- نموذج المهارات -->
                    <div id="skillsForm" class="form-section" style="display: none;">
                        <div class="form-group">
                            <label>فئة المهارة:</label>
                            <select id="skillCategory" required>
                                <option value="">اختر الفئة</option>
                                <option value="frontend">تطوير الواجهات الأمامية</option>
                                <option value="backend">تطوير الخلفية</option>
                                <option value="database">قواعد البيانات</option>
                                <option value="tools">الأدوات والتقنيات</option>
                                <option value="design">التصميم</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>أيقونة الفئة:</label>
                            <input type="text" id="skillIcon" placeholder="fas fa-code" required>
                            <small>استخدم أيقونات Font Awesome مثل: fas fa-code</small>
                        </div>
                        
                        <div class="form-group">
                            <label>المهارات (مفصولة بفاصلة):</label>
                            <textarea id="skillItems" placeholder="HTML, CSS, JavaScript, React" required></textarea>
                            <small>اكتب المهارات مفصولة بفاصلة</small>
                        </div>
                        
                        <div class="form-group">
                            <label>مستوى الخبرة:</label>
                            <select id="skillLevel" required>
                                <option value="">اختر المستوى</option>
                                <option value="beginner">مبتدئ</option>
                                <option value="intermediate">متوسط</option>
                                <option value="advanced">متقدم</option>
                                <option value="expert">خبير</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>وصف المهارات:</label>
                            <textarea id="skillDescription" placeholder="وصف مختصر عن هذه المجموعة من المهارات"></textarea>
                        </div>
                    </div>
                    
                    <!-- نموذج الخبرات -->
                    <div id="experienceForm" class="form-section" style="display: none;">
                        <div class="form-group">
                            <label>المسمى الوظيفي:</label>
                            <input type="text" id="expTitle" placeholder="مطور ويب" required>
                        </div>
                        
                        <div class="form-group">
                            <label>اسم الشركة:</label>
                            <input type="text" id="expCompany" placeholder="شركة التقنية المتقدمة" required>
                        </div>
                        
                        <div class="form-group">
                            <label>نوع العمل:</label>
                            <select id="expType" required>
                                <option value="">اختر نوع العمل</option>
                                <option value="fulltime">دوام كامل</option>
                                <option value="parttime">دوام جزئي</option>
                                <option value="freelance">عمل حر</option>
                                <option value="contract">عقد مؤقت</option>
                                <option value="internship">تدريب</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>الدور والمسؤوليات:</label>
                            <textarea id="expRole" placeholder="وصف الدور والمسؤوليات الرئيسية" required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label>الفترة الزمنية:</label>
                            <input type="text" id="expPeriod" placeholder="يناير 2020 - ديسمبر 2022" required>
                        </div>
                        
                        <div class="form-group">
                            <label>المهام والإنجازات (مفصولة بفاصلة):</label>
                            <textarea id="expTasks" placeholder="تطوير تطبيقات ويب, إدارة قواعد البيانات, تحسين الأداء" required></textarea>
                            <small>اكتب المهام والإنجازات مفصولة بفاصلة</small>
                        </div>
                        
                        <div class="form-group">
                            <label>التقنيات المستخدمة:</label>
                            <input type="text" id="expTechnologies" placeholder="React, Node.js, MongoDB">
                        </div>
                        
                        <div class="form-group">
                            <label>موقع الشركة:</label>
                            <input type="text" id="expLocation" placeholder="الرياض, السعودية">
                        </div>
                        
                        <div class="form-group">
                            <label>رابط الشركة:</label>
                            <input type="url" id="expCompanyUrl" placeholder="https://company.com">
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="closeModal()">إلغاء</button>
                        <button type="submit" class="btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let currentSection = 'skills';
        let editingId = null;
        
        // بيانات تجريبية
        const testSkill = {
            id: 1,
            category: "أنظمة التشغيل",
            icon: "fas fa-desktop",
            items: ["كالي لينكس", "Windows", "Linux"],
            level: "advanced",
            description: "خبرة في أنظمة التشغيل المختلفة"
        };
        
        const testExperience = {
            id: 1,
            title: "مطور ويب",
            company: "شركة التقنية",
            type: "fulltime",
            role: "تطوير المواقع",
            period: "2020 - 2023",
            tasks: ["تطوير الواجهات", "برمجة الخوادم"],
            technologies: "React, Node.js",
            location: "الرياض",
            companyUrl: "https://example.com"
        };
        
        function testSkillsModal() {
            currentSection = 'skills';
            editingId = null;
            showModal('skillsForm', 'إضافة مهارة جديدة');
        }
        
        function testEditSkill() {
            currentSection = 'skills';
            editingId = 1;
            showModal('skillsForm', 'تعديل المهارة');
            
            setTimeout(() => {
                document.getElementById('skillCategory').value = testSkill.category;
                document.getElementById('skillIcon').value = testSkill.icon;
                document.getElementById('skillItems').value = testSkill.items.join(', ');
                document.getElementById('skillLevel').value = testSkill.level;
                document.getElementById('skillDescription').value = testSkill.description;
            }, 100);
        }
        
        function testExperienceModal() {
            currentSection = 'experience';
            editingId = null;
            showModal('experienceForm', 'إضافة خبرة جديدة');
        }
        
        function testEditExperience() {
            currentSection = 'experience';
            editingId = 1;
            showModal('experienceForm', 'تعديل الخبرة');
            
            setTimeout(() => {
                document.getElementById('expTitle').value = testExperience.title;
                document.getElementById('expCompany').value = testExperience.company;
                document.getElementById('expType').value = testExperience.type;
                document.getElementById('expRole').value = testExperience.role;
                document.getElementById('expPeriod').value = testExperience.period;
                document.getElementById('expTasks').value = testExperience.tasks.join(', ');
                document.getElementById('expTechnologies').value = testExperience.technologies;
                document.getElementById('expLocation').value = testExperience.location;
                document.getElementById('expCompanyUrl').value = testExperience.companyUrl;
            }, 100);
        }
        
        function showModal(formId, title) {
            // إخفاء جميع النماذج
            document.querySelectorAll('.form-section').forEach(f => {
                f.style.display = 'none';
            });
            
            // عرض النموذج المطلوب
            const form = document.getElementById(formId);
            if (form) {
                form.style.display = 'block';
            }
            
            // تحديث العنوان
            document.getElementById('modalTitle').textContent = title;
            
            // عرض النافذة
            document.getElementById('addModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
        
        function closeModal() {
            document.getElementById('addModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    </script>
</body>
</html>
