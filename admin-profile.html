<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الملف الشخصي - طارق الشتيوي</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="admin.css">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-cog"></i> لوحة التحكم</h2>
            </div>
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="admin-portfolio.html">
                            <i class="fas fa-briefcase"></i>
                            <span>إدارة الأعمال</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-skills.html">
                            <i class="fas fa-code"></i>
                            <span>إدارة المهارات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-experience.html">
                            <i class="fas fa-briefcase"></i>
                            <span>إدارة الخبرات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-courses.html">
                            <i class="fas fa-graduation-cap"></i>
                            <span>إدارة الدورات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="admin-certificates.html">
                            <i class="fas fa-certificate"></i>
                            <span>إدارة الشهادات</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="admin-profile.html">
                            <i class="fas fa-user"></i>
                            <span>إدارة الملف الشخصي</span>
                        </a>
                    </li>
                    <li class="nav-item logout" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل الخروج</span>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <h1>إدارة الملف الشخصي</h1>
                <div class="header-actions">
                    <button class="btn-primary" onclick="saveProfile()">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                </div>
            </header>

            <!-- Profile Section -->
            <section class="admin-section active">
                <div class="section-content">
                    <div class="profile-container">
                        <!-- معلومات شخصية -->
                        <div class="profile-card">
                            <h3><i class="fas fa-user"></i> المعلومات الشخصية</h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>الاسم الكامل:</label>
                                    <input type="text" id="fullName" placeholder="طارق الشتيوي">
                                </div>
                                <div class="form-group">
                                    <label>المسمى الوظيفي:</label>
                                    <input type="text" id="jobTitle" placeholder="مطور ويب">
                                </div>
                                <div class="form-group">
                                    <label>البريد الإلكتروني:</label>
                                    <input type="email" id="email" placeholder="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label>رقم الهاتف:</label>
                                    <input type="tel" id="phone" placeholder="+966 50 123 4567">
                                </div>
                                <div class="form-group">
                                    <label>الموقع:</label>
                                    <input type="text" id="location" placeholder="الرياض, السعودية">
                                </div>
                                <div class="form-group">
                                    <label>تاريخ الميلاد:</label>
                                    <input type="date" id="birthDate">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>نبذة شخصية:</label>
                                <textarea id="bio" rows="4" placeholder="اكتب نبذة مختصرة عن نفسك وخبراتك"></textarea>
                            </div>
                        </div>

                        <!-- روابط التواصل -->
                        <div class="profile-card">
                            <h3><i class="fas fa-link"></i> روابط التواصل الاجتماعي</h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>LinkedIn:</label>
                                    <input type="url" id="linkedin" placeholder="https://linkedin.com/in/username">
                                </div>
                                <div class="form-group">
                                    <label>GitHub:</label>
                                    <input type="url" id="github" placeholder="https://github.com/username">
                                </div>
                                <div class="form-group">
                                    <label>Twitter:</label>
                                    <input type="url" id="twitter" placeholder="https://twitter.com/username">
                                </div>
                                <div class="form-group">
                                    <label>Instagram:</label>
                                    <input type="url" id="instagram" placeholder="https://instagram.com/username">
                                </div>
                                <div class="form-group">
                                    <label>الموقع الشخصي:</label>
                                    <input type="url" id="website" placeholder="https://yourwebsite.com">
                                </div>
                                <div class="form-group">
                                    <label>YouTube:</label>
                                    <input type="url" id="youtube" placeholder="https://youtube.com/channel/username">
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات الموقع -->
                        <div class="profile-card">
                            <h3><i class="fas fa-cog"></i> إعدادات الموقع</h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>عنوان الموقع:</label>
                                    <input type="text" id="siteTitle" placeholder="طارق الشتيوي - مطور ويب">
                                </div>
                                <div class="form-group">
                                    <label>وصف الموقع:</label>
                                    <input type="text" id="siteDescription" placeholder="موقع شخصي لعرض الأعمال والخبرات">
                                </div>
                                <div class="form-group">
                                    <label>الكلمات المفتاحية:</label>
                                    <input type="text" id="siteKeywords" placeholder="مطور ويب, برمجة, تصميم">
                                </div>
                                <div class="form-group">
                                    <label>لغة الموقع:</label>
                                    <select id="siteLanguage">
                                        <option value="ar">العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- صورة الملف الشخصي -->
                        <div class="profile-card">
                            <h3><i class="fas fa-image"></i> صورة الملف الشخصي</h3>
                            <div class="profile-image-section">
                                <div class="current-image">
                                    <img id="profileImagePreview" src="photo/7.jpg" alt="الصورة الشخصية">
                                </div>
                                <div class="image-controls">
                                    <div class="form-group">
                                        <label>رابط الصورة:</label>
                                        <input type="url" id="profileImageUrl" placeholder="https://example.com/image.jpg">
                                    </div>
                                    <button type="button" class="btn-secondary" onclick="updateProfileImage()">
                                        <i class="fas fa-sync"></i> تحديث الصورة
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات الأمان -->
                        <div class="profile-card">
                            <h3><i class="fas fa-shield-alt"></i> إعدادات الأمان</h3>
                            <div class="form-group">
                                <label>كلمة المرور الحالية:</label>
                                <input type="password" id="currentPassword" placeholder="كلمة المرور الحالية">
                            </div>
                            <div class="form-group">
                                <label>كلمة المرور الجديدة:</label>
                                <input type="password" id="newPassword" placeholder="كلمة المرور الجديدة">
                            </div>
                            <div class="form-group">
                                <label>تأكيد كلمة المرور:</label>
                                <input type="password" id="confirmPassword" placeholder="تأكيد كلمة المرور الجديدة">
                            </div>
                            <button type="button" class="btn-warning" onclick="changePassword()">
                                <i class="fas fa-key"></i> تغيير كلمة المرور
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        // تعيين القسم الحالي
        let currentSection = 'profile';
        
        // تحميل الملف الرئيسي للجافا سكريبت
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل البيانات والتهيئة
            loadProfileData();
        });
        
        // تحميل بيانات الملف الشخصي
        function loadProfileData() {
            const profile = JSON.parse(localStorage.getItem('userProfile')) || {};
            
            // تحميل البيانات في النموذج
            Object.keys(profile).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = profile[key] || '';
                }
            });
            
            // تحديث صورة المعاينة
            const profileImage = document.getElementById('profileImagePreview');
            if (profile.profileImageUrl && profileImage) {
                profileImage.src = profile.profileImageUrl;
            }
        }
        
        // حفظ الملف الشخصي
        function saveProfile() {
            const profile = {};
            const fields = [
                'fullName', 'jobTitle', 'email', 'phone', 'location', 'birthDate', 'bio',
                'linkedin', 'github', 'twitter', 'instagram', 'website', 'youtube',
                'siteTitle', 'siteDescription', 'siteKeywords', 'siteLanguage', 'profileImageUrl'
            ];
            
            fields.forEach(field => {
                const element = document.getElementById(field);
                if (element) {
                    profile[field] = element.value;
                }
            });
            
            localStorage.setItem('userProfile', JSON.stringify(profile));
            showMessage('تم حفظ الملف الشخصي بنجاح!', 'success');
        }
        
        // تحديث صورة الملف الشخصي
        function updateProfileImage() {
            const imageUrl = document.getElementById('profileImageUrl').value;
            const preview = document.getElementById('profileImagePreview');
            
            if (imageUrl && preview) {
                preview.src = imageUrl;
                showMessage('تم تحديث الصورة!', 'success');
            } else {
                showMessage('يرجى إدخال رابط صحيح للصورة', 'error');
            }
        }
        
        // تغيير كلمة المرور
        function changePassword() {
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (!currentPassword || !newPassword || !confirmPassword) {
                showMessage('يرجى ملء جميع حقول كلمة المرور', 'error');
                return;
            }
            
            if (newPassword !== confirmPassword) {
                showMessage('كلمة المرور الجديدة وتأكيدها غير متطابقين', 'error');
                return;
            }
            
            if (newPassword.length < 6) {
                showMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
                return;
            }
            
            // هنا يمكن إضافة التحقق من كلمة المرور الحالية
            localStorage.setItem('adminPassword', newPassword);
            
            // مسح الحقول
            document.getElementById('currentPassword').value = '';
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
            
            showMessage('تم تغيير كلمة المرور بنجاح!', 'success');
        }
        
        // عرض رسالة
        function showMessage(message, type = 'success') {
            // إزالة الرسائل السابقة
            const existingMessages = document.querySelectorAll('.message');
            existingMessages.forEach(msg => msg.remove());
            
            // إنشاء رسالة جديدة
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            
            // إضافة الرسالة للصفحة
            document.body.appendChild(messageDiv);
            
            // إزالة الرسالة بعد 3 ثوان
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }
    </script>
    <script src="admin.js"></script>
</body>
</html>
