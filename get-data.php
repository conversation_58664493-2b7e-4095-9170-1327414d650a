<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

// دالة لقراءة ملف JSON
function readJsonFile($filename) {
    $filepath = __DIR__ . '/data/' . $filename;
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        return json_decode($content, true) ?: [];
    }
    return [];
}

// الحصول على نوع البيانات المطلوبة
$type = $_GET['type'] ?? '';

try {
    switch ($type) {
        case 'websites':
            $data = readJsonFile('websites.json');
            break;
            
        case 'images':
            $data = readJsonFile('images.json');
            // إضافة المسار الكامل للصور
            foreach ($data as &$image) {
                $image['src'] = 'image/' . $image['filename'];
            }
            break;
            
        case 'skills':
            $data = readJsonFile('skills.json');
            break;
            
        case 'experiences':
            $data = readJsonFile('experiences.json');
            break;
            
        case 'courses':
            $data = readJsonFile('courses.json');
            break;
            
        case 'certificates':
            $data = readJsonFile('certificates.json');
            break;

        case 'profile':
            $data = readJsonFile('profile.json');
            break;

        case 'social-links':
            $data = readJsonFile('social-links.json');
            break;
            
        case 'portfolio':
            // دمج المواقع والصور للبورتفوليو
            $websites = readJsonFile('websites.json');
            $images = readJsonFile('images.json');
            
            // إضافة نوع لكل عنصر
            foreach ($websites as &$website) {
                $website['type'] = 'website';
            }
            
            foreach ($images as &$image) {
                $image['type'] = 'image';
                $image['src'] = 'image/' . $image['filename'];
            }
            
            // دمج البيانات
            $data = array_merge($websites, $images);
            
            // ترتيب حسب الترتيب أو التاريخ
            usort($data, function($a, $b) {
                if (isset($a['order']) && isset($b['order'])) {
                    return $a['order'] - $b['order'];
                }
                return strtotime($b['date'] ?? '0') - strtotime($a['date'] ?? '0');
            });
            break;
            
        case 'stats':
            // إحصائيات عامة
            $websites = readJsonFile('websites.json');
            $images = readJsonFile('images.json');
            $skills = readJsonFile('skills.json');
            $experiences = readJsonFile('experiences.json');
            $courses = readJsonFile('courses.json');
            $certificates = readJsonFile('certificates.json');
            
            $data = [
                'websites' => count($websites),
                'images' => count($images),
                'skills' => count($skills),
                'experiences' => count($experiences),
                'courses' => count($courses),
                'certificates' => count($certificates),
                'total_portfolio' => count($websites) + count($images)
            ];
            break;
            
        default:
            throw new Exception('نوع البيانات غير صحيح');
    }
    
    // ترتيب البيانات حسب الترتيب أو التاريخ (إذا لم يتم ترتيبها مسبقاً)
    if ($type !== 'portfolio' && $type !== 'stats' && isset($data[0])) {
        usort($data, function($a, $b) {
            if (isset($a['order']) && isset($b['order'])) {
                return $a['order'] - $b['order'];
            }
            return strtotime($b['date'] ?? '0') - strtotime($a['date'] ?? '0');
        });
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data,
        'count' => is_array($data) ? count($data) : 0
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
