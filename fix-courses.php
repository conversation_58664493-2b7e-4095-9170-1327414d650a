<?php
// إنشاء ملف courses.json بدون BOM

$courses = [
    [
        "id" => 1,
        "title" => "دورة البرمجة الأساسية",
        "icon" => "fas fa-code",
        "status" => "معتمدة",
        "description" => "تعلم أساسيات البرمجة والمفاهيم الأساسية",
        "duration" => "40 ساعة",
        "provider" => "معهد التدريب التقني",
        "completion_date" => "2023-06-15",
        "certificate_url" => "",
        "skills_gained" => [
            "أساسيات البرمجة",
            "المنطق البرمجي",
            "حل المشاكل"
        ],
        "rating" => "5",
        "order" => 1
    ],
    [
        "id" => 2,
        "title" => "دورة الروبوت",
        "icon" => "fas fa-robot",
        "status" => "معتمدة",
        "description" => "فهم مبادئ البرمجة للروبوتات والأتمتة",
        "duration" => "30 ساعة",
        "provider" => "أكاديمية الروبوتات",
        "completion_date" => "2023-08-20",
        "certificate_url" => "",
        "skills_gained" => [
            "برمجة الروبوتات",
            "الأتمتة",
            "الذكاء الاصطناعي"
        ],
        "rating" => "4",
        "order" => 2
    ],
    [
        "id" => 3,
        "title" => "دورات العطاء الرقمي",
        "icon" => "fas fa-heart",
        "status" => "متنوعة",
        "description" => "دورات متنوعة في المجالات التقنية المختلفة",
        "duration" => "متغيرة",
        "provider" => "منصة العطاء الرقمي",
        "completion_date" => "2023-12-01",
        "certificate_url" => "",
        "skills_gained" => [
            "التطوع الرقمي",
            "المهارات التقنية",
            "العمل الجماعي"
        ],
        "rating" => "5",
        "order" => 3
    ]
];

// حفظ الملف بدون BOM
$json = json_encode($courses, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
file_put_contents('data/courses.json', $json);

echo "تم إنشاء ملف courses.json بنجاح بدون BOM\n";
echo "حجم الملف: " . filesize('data/courses.json') . " بايت\n";
echo "أول 50 حرف: " . substr(file_get_contents('data/courses.json'), 0, 50) . "\n";

// اختبار قراءة الملف
$test = json_decode(file_get_contents('data/courses.json'), true);
echo "اختبار القراءة: " . (is_array($test) ? "نجح" : "فشل") . "\n";
echo "عدد العناصر: " . count($test) . "\n";
?>
