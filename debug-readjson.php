<?php
header('Content-Type: application/json; charset=utf-8');

// دالة لقراءة ملف JSON (نفس الدالة من get-data.php)
function readJsonFile($filename) {
    $filepath = __DIR__ . '/data/' . $filename;
    echo "Filepath: " . $filepath . "\n";
    echo "File exists: " . (file_exists($filepath) ? 'Yes' : 'No') . "\n";
    
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        echo "File content length: " . strlen($content) . "\n";
        echo "First 100 chars: " . substr($content, 0, 100) . "\n";
        
        $decoded = json_decode($content, true);
        echo "JSON decode result: " . ($decoded ? 'Success' : 'Failed') . "\n";
        echo "JSON error: " . json_last_error_msg() . "\n";
        
        return $decoded ?: [];
    }
    return [];
}

echo "=== تشخيص قراءة ملف courses.json ===\n\n";

$data = readJsonFile('courses.json');
echo "\nFinal result count: " . count($data) . "\n";
echo "Final result: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
?>
