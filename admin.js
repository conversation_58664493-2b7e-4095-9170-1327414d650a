// المتغيرات العامة
let currentSection = 'portfolio';
let editingId = null;
let websites = [];
let albumImages = [];
let youtubeVideos = [];
let certificates = [];
let skills = [];
let experiences = [];
let courses = [];

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin panel loading...');
    
    // التحقق من تسجيل الدخول
    if (!checkAuth()) return;
    
    // تحميل البيانات
    loadAllData();
    
    // تهيئة لوحة التحكم
    initializeAdmin();
});

// التحقق من المصادقة
function checkAuth() {
    const isLoggedIn = localStorage.getItem('adminLoggedIn');
    if (!isLoggedIn || isLoggedIn !== 'true') {
        alert('يجب تسجيل الدخول أولاً');
        window.location.href = 'index.html';
        return false;
    }
    return true;
}

// تحميل جميع البيانات
function loadAllData() {
    websites = JSON.parse(localStorage.getItem('websites')) || [];
    albumImages = JSON.parse(localStorage.getItem('albumImages')) || [];
    youtubeVideos = JSON.parse(localStorage.getItem('youtubeVideos')) || [];
    certificates = JSON.parse(localStorage.getItem('certificates')) || [];
    skills = JSON.parse(localStorage.getItem('skills')) || [];
    experiences = JSON.parse(localStorage.getItem('experiences')) || [];
    courses = JSON.parse(localStorage.getItem('courses')) || [];
    
    console.log('Data loaded successfully');
}

// تهيئة لوحة التحكم
function initializeAdmin() {
    // إعداد معالجات الأحداث
    setupEventListeners();
    
    // تحميل المحتوى الافتراضي
    showSection('portfolio');
    updateStats();
    
    console.log('Admin panel initialized');
}

// إعداد معالجات الأحداث
function setupEventListeners() {
    // معالج النموذج
    const form = document.getElementById('addForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
    
    // معالج تغيير نوع المحتوى
    const contentType = document.getElementById('contentType');
    if (contentType) {
        contentType.addEventListener('change', toggleContentForm);
    }
}

// عرض قسم معين
function showSection(section) {
    console.log('Switching to section:', section);
    
    // إخفاء جميع الأقسام
    document.querySelectorAll('.admin-section').forEach(s => {
        s.classList.remove('active');
    });
    
    // إزالة النشاط من جميع عناصر التنقل
    document.querySelectorAll('.nav-item').forEach(n => {
        n.classList.remove('active');
    });
    
    // عرض القسم المحدد
    const sectionElement = document.getElementById(section + '-section');
    if (sectionElement) {
        sectionElement.classList.add('active');
    }
    
    // تفعيل عنصر التنقل
    const navItem = document.querySelector(`[onclick="showSection('${section}')"]`);
    if (navItem) {
        navItem.classList.add('active');
    }
    
    currentSection = section;
    
    // تحديث العنوان
    updateSectionTitle(section);
    
    // تحميل البيانات حسب القسم
    loadSectionData(section);
}

// تحديث عنوان القسم
function updateSectionTitle(section) {
    const titles = {
        portfolio: 'إدارة الأعمال',
        skills: 'إدارة المهارات',
        experience: 'إدارة الخبرات',
        courses: 'إدارة الدورات',
        certificates: 'إدارة الشهادات',
        profile: 'إدارة الملف الشخصي'
    };
    
    const titleElement = document.getElementById('sectionTitle');
    if (titleElement) {
        titleElement.textContent = titles[section] || 'لوحة التحكم';
    }
}

// تحميل بيانات القسم
function loadSectionData(section) {
    switch(section) {
        case 'portfolio':
            loadPortfolioItems();
            break;
        case 'skills':
            loadSkills();
            break;
        case 'experience':
            loadExperiences();
            break;
        case 'courses':
            loadCourses();
            break;
        case 'certificates':
            loadCertificates();
            break;
    }
}

// تحميل عناصر البورتفوليو
function loadPortfolioItems() {
    const grid = document.getElementById('portfolioGrid');
    if (!grid) return;
    
    grid.innerHTML = '';
    
    // دمج جميع أنواع المحتوى
    const allItems = [
        ...websites.map(item => ({...item, type: 'website'})),
        ...albumImages.map(item => ({...item, type: 'image'})),
        ...youtubeVideos.map(item => ({...item, type: 'youtube'}))
    ];
    
    // ترتيب حسب التاريخ
    allItems.sort((a, b) => b.id - a.id);
    
    if (allItems.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-folder-open"></i><p>لا توجد أعمال بعد</p></div>';
        return;
    }
    
    allItems.forEach(item => {
        const card = createPortfolioCard(item);
        grid.appendChild(card);
    });
}

// إنشاء كارد البورتفوليو
function createPortfolioCard(item) {
    const div = document.createElement('div');
    div.className = 'admin-card';
    
    let mediaContent = '';
    let actionButtons = '';
    
    switch(item.type) {
        case 'website':
            mediaContent = `
                <div class="card-preview website-preview">
                    <i class="fas fa-globe"></i>
                    <h4>${item.name}</h4>
                </div>
            `;
            actionButtons = `
                <button class="action-btn edit-btn" onclick="editWebsite(${item.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteWebsite(${item.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            break;
            
        case 'image':
            mediaContent = `<img src="${item.src}" alt="${item.title}" class="card-image">`;
            actionButtons = `
                <button class="action-btn edit-btn" onclick="editImage(${item.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteImage(${item.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            break;
            
        case 'youtube':
            mediaContent = `
                <div class="card-preview youtube-preview">
                    <img src="${item.thumbnail}" alt="${item.title}">
                    <div class="youtube-overlay">
                        <i class="fab fa-youtube"></i>
                    </div>
                </div>
            `;
            actionButtons = `
                <button class="action-btn edit-btn" onclick="editYoutube(${item.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteYoutube(${item.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            break;
    }
    
    div.innerHTML = `
        <div class="card-actions">
            ${actionButtons}
        </div>
        <div class="card-media">
            ${mediaContent}
        </div>
        <div class="card-content">
            <h3>${item.title || item.name}</h3>
            <p>${item.description || 'لا يوجد وصف'}</p>
            <span class="card-type">${getTypeLabel(item.type)}</span>
        </div>
    `;
    
    return div;
}

// الحصول على تسمية النوع
function getTypeLabel(type) {
    const labels = {
        website: 'موقع إلكتروني',
        image: 'صورة',
        youtube: 'فيديو يوتيوب'
    };
    return labels[type] || type;
}

// عرض نافذة الإضافة
function showAddModal() {
    console.log('Opening add modal for section:', currentSection);
    
    editingId = null;
    
    // تحديث عنوان النافذة
    const modalTitle = document.getElementById('modalTitle');
    if (modalTitle) {
        modalTitle.textContent = getModalTitle();
    }
    
    // إخفاء جميع النماذج
    document.querySelectorAll('.form-section').forEach(f => {
        f.style.display = 'none';
    });
    
    // عرض النموذج المناسب
    showCurrentForm();
    
    // مسح النموذج
    const form = document.getElementById('addForm');
    if (form) {
        form.reset();
    }
    
    // عرض النافذة
    const modal = document.getElementById('addModal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    } else {
        console.error('Modal not found!');
        showMessage('خطأ في فتح النافذة', 'error');
    }
}

// عرض النموذج الحالي
function showCurrentForm() {
    const formMap = {
        portfolio: 'portfolioForm',
        certificates: 'certificatesForm',
        skills: 'skillsForm',
        experience: 'experienceForm',
        courses: 'coursesForm'
    };
    
    const formId = formMap[currentSection];
    if (formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.style.display = 'block';
        }
    }
    
    // إذا كان البورتفوليو، عرض نموذج المواقع افتراضي
    if (currentSection === 'portfolio') {
        toggleContentForm();
    }
}

// الحصول على عنوان النافذة
function getModalTitle() {
    const titles = {
        portfolio: editingId ? 'تعديل عمل' : 'إضافة عمل جديد',
        skills: editingId ? 'تعديل مهارة' : 'إضافة مهارة جديدة',
        experience: editingId ? 'تعديل خبرة' : 'إضافة خبرة جديدة',
        courses: editingId ? 'تعديل دورة' : 'إضافة دورة جديدة',
        certificates: editingId ? 'تعديل شهادة' : 'إضافة شهادة جديدة'
    };
    
    return titles[currentSection] || 'إضافة محتوى جديد';
}

// إغلاق النافذة
function closeModal() {
    const modal = document.getElementById('addModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
    editingId = null;
}

// تغيير نوع المحتوى
function toggleContentForm() {
    const contentType = document.getElementById('contentType');
    if (!contentType) return;
    
    const selectedType = contentType.value;
    
    // إخفاء جميع النماذج الفرعية
    document.querySelectorAll('.content-fields').forEach(field => {
        field.style.display = 'none';
    });
    
    // عرض النموذج المناسب
    const targetForm = document.getElementById(selectedType + 'Fields');
    if (targetForm) {
        targetForm.style.display = 'block';
    }
}

// معالج إرسال النموذج
function handleFormSubmit(e) {
    e.preventDefault();
    
    console.log('Form submitted for section:', currentSection);
    
    try {
        switch(currentSection) {
            case 'portfolio':
                handlePortfolioSubmit();
                break;
            case 'certificates':
                handleCertificateSubmit();
                break;
            case 'skills':
                handleSkillSubmit();
                break;
            case 'experience':
                handleExperienceSubmit();
                break;
            case 'courses':
                handleCourseSubmit();
                break;
            default:
                throw new Error('قسم غير معروف');
        }
    } catch (error) {
        console.error('Error submitting form:', error);
        showMessage('حدث خطأ أثناء الحفظ: ' + error.message, 'error');
    }
}

// معالج البورتفوليو
function handlePortfolioSubmit() {
    const contentType = document.getElementById('contentType').value;
    
    switch(contentType) {
        case 'website':
            handleWebsiteSubmit();
            break;
        case 'image':
            handleImageSubmit();
            break;
        case 'youtube':
            handleYoutubeSubmit();
            break;
        default:
            throw new Error('نوع محتوى غير معروف');
    }
}

// معالج المواقع
function handleWebsiteSubmit() {
    const name = document.getElementById('websiteName').value.trim();
    const url = document.getElementById('websiteUrl').value.trim();
    const description = document.getElementById('websiteDescription').value.trim();
    
    if (!name || !url) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }
    
    if (editingId) {
        // تعديل موقع موجود
        const website = websites.find(item => item.id === editingId);
        if (website) {
            website.name = name;
            website.url = url;
            website.description = description;
        }
    } else {
        // إضافة موقع جديد
        const newWebsite = {
            id: Date.now(),
            name: name,
            url: url,
            description: description,
            date: new Date().toLocaleDateString('ar-SA')
        };
        websites.unshift(newWebsite);
    }
    
    localStorage.setItem('websites', JSON.stringify(websites));
    loadPortfolioItems();
    updateStats();
    closeModal();
    showMessage(editingId ? 'تم تحديث الموقع بنجاح!' : 'تم إضافة الموقع بنجاح!', 'success');
}

// تحديث الإحصائيات
function updateStats() {
    const totalWebsitesEl = document.getElementById('totalWebsites');
    const totalImagesEl = document.getElementById('totalImages');
    const totalVideosEl = document.getElementById('totalVideos');
    
    if (totalWebsitesEl) totalWebsitesEl.textContent = websites.length;
    if (totalImagesEl) totalImagesEl.textContent = albumImages.length;
    if (totalVideosEl) totalVideosEl.textContent = youtubeVideos.length;
}

// عرض رسالة
function showMessage(message, type = 'success') {
    // إزالة الرسائل السابقة
    const existingMessages = document.querySelectorAll('.message');
    existingMessages.forEach(msg => msg.remove());
    
    // إنشاء رسالة جديدة
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    // إضافة الرسالة للصفحة
    document.body.appendChild(messageDiv);
    
    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

// تسجيل الخروج
function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        localStorage.removeItem('adminLoggedIn');
        window.location.href = 'index.html';
    }
}

// دوال التعديل والحذف للمواقع
function editWebsite(id) {
    const website = websites.find(item => item.id === id);
    if (!website) return;
    
    editingId = id;
    document.getElementById('contentType').value = 'website';
    toggleContentForm();
    
    document.getElementById('websiteName').value = website.name;
    document.getElementById('websiteUrl').value = website.url;
    document.getElementById('websiteDescription').value = website.description;
    
    showAddModal();
}

function deleteWebsite(id) {
    if (confirm('هل أنت متأكد من حذف هذا الموقع؟')) {
        websites = websites.filter(item => item.id !== id);
        localStorage.setItem('websites', JSON.stringify(websites));
        loadPortfolioItems();
        updateStats();
        showMessage('تم حذف الموقع بنجاح!', 'success');
    }
}

// دوال مؤقتة للصور واليوتيوب (سأكملها في الرسالة التالية)
function editImage(id) {
    showMessage('ميزة تعديل الصور قيد التطوير', 'error');
}

function deleteImage(id) {
    if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
        albumImages = albumImages.filter(item => item.id !== id);
        localStorage.setItem('albumImages', JSON.stringify(albumImages));
        loadPortfolioItems();
        updateStats();
        showMessage('تم حذف الصورة بنجاح!', 'success');
    }
}

function editYoutube(id) {
    showMessage('ميزة تعديل فيديوهات اليوتيوب قيد التطوير', 'error');
}

function deleteYoutube(id) {
    if (confirm('هل أنت متأكد من حذف هذا الفيديو؟')) {
        youtubeVideos = youtubeVideos.filter(item => item.id !== id);
        localStorage.setItem('youtubeVideos', JSON.stringify(youtubeVideos));
        loadPortfolioItems();
        updateStats();
        showMessage('تم حذف الفيديو بنجاح!', 'success');
    }
}

// دوال الأقسام الأخرى
function loadSkills() {
    const grid = document.getElementById('skillsGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (skills.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-code"></i><p>لا توجد مهارات بعد</p></div>';
        return;
    }

    skills.forEach(skill => {
        const card = createSkillCard(skill);
        grid.appendChild(card);
    });
}

function loadExperiences() {
    const grid = document.getElementById('experienceGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (experiences.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-briefcase"></i><p>لا توجد خبرات بعد</p></div>';
        return;
    }

    experiences.forEach(experience => {
        const card = createExperienceCard(experience);
        grid.appendChild(card);
    });
}

function loadCourses() {
    const grid = document.getElementById('coursesGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (courses.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-graduation-cap"></i><p>لا توجد دورات بعد</p></div>';
        return;
    }

    courses.forEach(course => {
        const card = createCourseCard(course);
        grid.appendChild(card);
    });
}

function loadCertificates() {
    const grid = document.getElementById('certificatesGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (certificates.length === 0) {
        grid.innerHTML = '<div class="empty-state"><i class="fas fa-certificate"></i><p>لا توجد شهادات بعد</p></div>';
        return;
    }

    certificates.forEach(certificate => {
        const card = createCertificateCard(certificate);
        grid.appendChild(card);
    });
}

function handleCertificateSubmit() {
    showMessage('ميزة إضافة الشهادات قيد التطوير', 'error');
}

function handleSkillSubmit() {
    showMessage('ميزة إضافة المهارات قيد التطوير', 'error');
}

function handleExperienceSubmit() {
    showMessage('ميزة إضافة الخبرات قيد التطوير', 'error');
}

function handleCourseSubmit() {
    showMessage('ميزة إضافة الدورات قيد التطوير', 'error');
}

function handleImageSubmit() {
    showMessage('ميزة إضافة الصور قيد التطوير', 'error');
}

function handleYoutubeSubmit() {
    showMessage('ميزة إضافة فيديوهات اليوتيوب قيد التطوير', 'error');
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('exp
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
    if (!skill) return;
// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
function editExperience(id) {
    const exp = experiences.find(item => item.id === id);
    if (!exp) return;
    
    editingId = id;
    currentSection = 'experience';
    
    document.getElementById('expTitle').value = exp.title;
    document.getElementById('expCompany').value = exp.company;
    document.getElementById('expRole').value = exp.role;
    document.getElementById('expPeriod').value = exp.period;
    document.getElementById('expTasks').value = exp.tasks.join(', ');
    
    showAddModal();
}

// تعديل دورة
function editCourse(id) {
    const course = courses.find(item => item.id === id);
    if (!course) return;
    
    editingId = id;
    currentSection = 'courses';
    
    document.getElementById('courseTitle').value = course.title;
    document.getElementById('courseIcon').value = course.icon;
    document.getElementById('courseDuration').value = course.duration;
    document.getElementById('courseDescription').value = course.description;
    
    showAddModal();
}

// تعديل مهارة
function editSkill(id) {
    const skill = skills.find(item => item.id === id);
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}

// تعديل خبرة
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}
    if (!skill) return;
    
    editingId = id;
    currentSection = 'skills';
    
    document.getElementById('skillCategory').value = skill.category;
    document.getElementById('skillIcon').value = skill.icon;
    document.getElementById('skillItems').value = skill.items.join(', ');
    
    showAddModal();
}





















