<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// تحديد مجلد البيانات
$dataDir = __DIR__ . '/data/';
$dataFile = $dataDir . 'social-links.json';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير صحيحة');
    }

    // قراءة البيانات المرسلة
    $input = file_get_contents('php://input');
    $socialLinks = json_decode($input, true);

    if (!$socialLinks) {
        throw new Exception('بيانات غير صحيحة');
    }

    // التحقق من وجود مجلد البيانات
    if (!is_dir($dataDir)) {
        mkdir($dataDir, 0755, true);
    }

    // حفظ البيانات
    $result = file_put_contents($dataFile, json_encode($socialLinks, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

    if ($result === false) {
        throw new Exception('فشل في حفظ البيانات');
    }

    echo json_encode([
        'success' => true,
        'message' => 'تم حفظ روابط التواصل الاجتماعي بنجاح'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
